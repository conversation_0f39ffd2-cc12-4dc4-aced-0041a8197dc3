"use client";

import { useState } from "react";
import { Search } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { getApiUrl, API_ENDPOINTS } from "@/lib/api-config";

interface Product {
  itemId: number;
  productMainUrl: string;
  title: string;
  price: string;
  orderTotal: string;
  fortnightOrder: string;
  exclusivePrice?: boolean;
  dsDiscount?: number;
  starProduct?: boolean;
  qualitySupplier?: boolean;
  trends?: Array<{ value: number; date: string }>;
}

interface ApiResponse {
  success: boolean;
  module?: {
    productDTOPageInfo: {
      dataList: Product[];
      totalCount: number;
      totalPage: number;
    };
  };
}

export default function Home() {
  const [searchQuery, setSearchQuery] = useState("");
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(false);
  const [searched, setSearched] = useState(false);

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!searchQuery.trim()) return;

    setLoading(true);
    setSearched(true);

    try {
      const response = await fetch(getApiUrl(API_ENDPOINTS.QUERY_SELECTION_PAGE), {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          keyWorld: searchQuery,
          pageNum: 1,
          pageSize: 20,
          productPoolLevel: 1,
        }),
      });

      const data: ApiResponse = await response.json();
      
      if (data.success && data.module?.productDTOPageInfo?.dataList) {
        setProducts(data.module.productDTOPageInfo.dataList);
      } else {
        setProducts([]);
      }
    } catch (error) {
      console.error("Search error:", error);
      setProducts([]);
    } finally {
      setLoading(false);
    }
  };

  const formatPrice = (price: string) => {
    return price ? `$${price}` : "Price not available";
  };

  const formatOrderCount = (orderTotal: string) => {
    if (!orderTotal) return "New";
    
    const num = parseInt(orderTotal);
    if (num >= 10000) {
      return `${Math.floor(num / 1000)}K+`;
    } else if (num >= 1000) {
      return `${Math.floor(num / 100) / 10}K+`;
    } else if (num >= 100) {
      return `${Math.floor(num / 10) * 10}+`;
    } else if (num >= 10) {
      return `${Math.floor(num / 10) * 10}+`;
    }
    return `${num}+`;
  };

  const formatFortnightOrders = (fortnightOrder: string) => {
    if (!fortnightOrder) return "New";
    
    const num = parseInt(fortnightOrder);
    if (num >= 1000) {
      return `${Math.floor(num / 100) / 10}K+`;
    } else if (num >= 100) {
      return `${Math.floor(num / 10) * 10}+`;
    } else if (num >= 10) {
      return `${Math.floor(num / 10) * 10}+`;
    }
    return `${num}+`;
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Search Header */}
      <div className={`transition-all duration-500 ${searched ? "py-6 border-b" : "py-20"}`}>
        <div className="max-w-2xl mx-auto px-4">
          <div className={`text-center mb-8 transition-all duration-500 ${searched ? "mb-4" : ""}`}>
            <h1 className={`font-bold text-gray-800 transition-all duration-500 ${searched ? "text-2xl" : "text-5xl mb-4"}`}>
              Product Search
            </h1>
            {!searched && (
              <p className="text-gray-600 text-lg">
                Discover trending products with sales data insights
              </p>
            )}
          </div>
          
          <form onSubmit={handleSearch} className="relative">
            <div className="relative flex items-center">
              <Input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search for products..."
                className="w-full h-12 text-lg pl-4 pr-20 rounded-full border-2 border-gray-200 focus:border-blue-500 focus:ring-0"
                disabled={loading}
              />
              <Button
                type="submit"
                disabled={loading || !searchQuery.trim()}
                className="absolute right-1 h-10 px-6 rounded-full bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
              >
                {loading ? (
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                ) : (
                  <Search className="w-4 h-4" />
                )}
              </Button>
            </div>
          </form>
        </div>
      </div>

      {/* Results */}
      {searched && (
        <div className="max-w-7xl mx-auto px-4 py-8">
          {loading ? (
            <div className="text-center py-12">
              <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4" />
              <p className="text-gray-600">Searching for products...</p>
            </div>
          ) : products.length > 0 ? (
            <>
              <div className="mb-6">
                <p className="text-gray-600">
                  Found {products.length} products for "{searchQuery}"
                </p>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {products.map((product) => (
                  <Card key={product.itemId} className="overflow-hidden hover:shadow-lg transition-shadow">
                    <div className="aspect-square bg-gray-100 relative">
                      {product.productMainUrl ? (
                        <img
                          src={product.productMainUrl}
                          alt={product.title}
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.src = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzlmYTZiMiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPk5vIEltYWdlPC90ZXh0Pjwvc3ZnPg==";
                          }}
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center text-gray-400">
                          No Image
                        </div>
                      )}
                      
                      {/* Badges */}
                      <div className="absolute top-2 left-2 flex flex-col gap-1">
                        {product.exclusivePrice && (
                          <Badge variant="destructive" className="text-xs">
                            Exclusive Price
                          </Badge>
                        )}
                        {product.starProduct && (
                          <Badge variant="secondary" className="text-xs">
                            ⭐ 4+ Star
                          </Badge>
                        )}
                        {product.qualitySupplier && (
                          <Badge variant="outline" className="text-xs bg-white">
                            Quality Supplier
                          </Badge>
                        )}
                      </div>
                    </div>
                    
                    <CardContent className="p-4">
                      <h3 className="font-medium text-sm line-clamp-2 mb-2 min-h-[2.5rem]">
                        {product.title}
                      </h3>
                      
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-lg font-bold text-green-600">
                            {formatPrice(product.price)}
                          </span>
                          {product.dsDiscount && product.dsDiscount > 0 && (
                            <Badge variant="destructive" className="text-xs">
                              -{Math.round(product.dsDiscount * 100)}%
                            </Badge>
                          )}
                        </div>
                        
                        <div className="text-xs text-gray-600 space-y-1">
                          <div className="flex justify-between">
                            <span>Total Orders</span>
                            <span className="font-medium text-green-600">
                              {formatOrderCount(product.orderTotal)}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span>Last 14 Days Orders</span>
                            <span className="font-medium text-blue-600">
                              {formatFortnightOrders(product.fortnightOrder)}
                            </span>
                          </div>
                        </div>
                        
                        {/* Simple trend indicator */}
                        <div className="flex items-center gap-1 mt-2">
                          <div className="flex-1 h-1 bg-gray-200 rounded-full overflow-hidden">
                            <div 
                              className="h-full bg-gradient-to-r from-green-400 to-green-600 rounded-full transition-all duration-300"
                              style={{ width: `${Math.min(100, (parseInt(product.fortnightOrder || "0") / Math.max(parseInt(product.orderTotal || "1"), 1)) * 100 * 10)}%` }}
                            />
                          </div>
                          <span className="text-xs text-gray-500">Trend</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </>
          ) : (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4">
                <Search className="w-16 h-16 mx-auto mb-4 opacity-50" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No products found
              </h3>
              <p className="text-gray-600">
                Try searching with different keywords or check your spelling.
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
