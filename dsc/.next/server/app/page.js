(()=>{var e={};e.id=974,e.ids=[974],e.modules={375:(e,r,t)=>{Promise.resolve().then(t.bind(t,597))},554:(e,r)=>{"use strict";function t(e){return e.endsWith("/route")}Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"isAppRouteRoute",{enumerable:!0,get:function(){return t}})},597:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n});let n=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/code/aidc-t-selection/dsc/app/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/code/aidc-t-selection/dsc/app/page.tsx","default")},660:(e,r)=>{"use strict";function t(e){let r=5381;for(let t=0;t<e.length;t++)r=(r<<5)+r+e.charCodeAt(t)|0;return r>>>0}function n(e){return t(e).toString(36).slice(0,5)}Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{djb2Hash:function(){return t},hexHash:function(){return n}})},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1437:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{INTERCEPTION_ROUTE_MARKERS:function(){return o},extractInterceptionRouteInformation:function(){return a},isInterceptionRouteAppPath:function(){return i}});let n=t(4722),o=["(..)(..)","(.)","(..)","(...)"];function i(e){return void 0!==e.split("/").find(e=>o.find(r=>e.startsWith(r)))}function a(e){let r,t,i;for(let n of e.split("/"))if(t=o.find(e=>n.startsWith(e))){[r,i]=e.split(t,2);break}if(!r||!t||!i)throw Object.defineProperty(Error("Invalid interception route: "+e+". Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>"),"__NEXT_ERROR_CODE",{value:"E269",enumerable:!1,configurable:!0});switch(r=(0,n.normalizeAppPath)(r),t){case"(.)":i="/"===r?"/"+i:r+"/"+i;break;case"(..)":if("/"===r)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..) marker at the root level, use (.) instead."),"__NEXT_ERROR_CODE",{value:"E207",enumerable:!1,configurable:!0});i=r.split("/").slice(0,-1).concat(i).join("/");break;case"(...)":i="/"+i;break;case"(..)(..)":let a=r.split("/");if(a.length<=2)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..)(..) marker at the root level or one level up."),"__NEXT_ERROR_CODE",{value:"E486",enumerable:!1,configurable:!0});i=a.slice(0,-2).concat(i).join("/");break;default:throw Object.defineProperty(Error("Invariant: unexpected marker"),"__NEXT_ERROR_CODE",{value:"E112",enumerable:!1,configurable:!0})}return{interceptingRoute:r,interceptedRoute:i}}},1658:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{fillMetadataSegment:function(){return p},normalizeMetadataPageToRoute:function(){return m},normalizeMetadataRoute:function(){return f}});let n=t(8304),o=function(e){return e&&e.__esModule?e:{default:e}}(t(8671)),i=t(6341),a=t(4396),s=t(660),l=t(4722),c=t(2958),d=t(5499);function u(e){let r=o.default.dirname(e);if(e.endsWith("/sitemap"))return"";let t="";return r.split("/").some(e=>(0,d.isGroupSegment)(e)||(0,d.isParallelRouteSegment)(e))&&(t=(0,s.djb2Hash)(r).toString(36).slice(0,6)),t}function p(e,r,t){let n=(0,l.normalizeAppPath)(e),s=(0,a.getNamedRouteRegex)(n,{prefixRouteKeys:!1}),d=(0,i.interpolateDynamicPath)(n,r,s),{name:p,ext:f}=o.default.parse(t),m=u(o.default.posix.join(e,p)),g=m?`-${m}`:"";return(0,c.normalizePathSep)(o.default.join(d,`${p}${g}${f}`))}function f(e){if(!(0,n.isMetadataPage)(e))return e;let r=e,t="";if("/robots"===e?r+=".txt":"/manifest"===e?r+=".webmanifest":t=u(e),!r.endsWith("/route")){let{dir:e,name:n,ext:i}=o.default.parse(r);r=o.default.posix.join(e,`${n}${t?`-${t}`:""}${i}`,"route")}return r}function m(e,r){let t=e.endsWith("/route"),n=t?e.slice(0,-6):e,o=n.endsWith("/sitemap")?".xml":"";return(r?`${n}/[__metadata_id__]`:`${n}${o}`)+(t?"/route":"")}},2380:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6346,23)),Promise.resolve().then(t.t.bind(t,7924,23)),Promise.resolve().then(t.t.bind(t,5656,23)),Promise.resolve().then(t.t.bind(t,99,23)),Promise.resolve().then(t.t.bind(t,8243,23)),Promise.resolve().then(t.t.bind(t,8827,23)),Promise.resolve().then(t.t.bind(t,2763,23)),Promise.resolve().then(t.t.bind(t,7173,23))},2437:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"getPathMatch",{enumerable:!0,get:function(){return o}});let n=t(5362);function o(e,r){let t=[],o=(0,n.pathToRegexp)(e,t,{delimiter:"/",sensitive:"boolean"==typeof(null==r?void 0:r.sensitive)&&r.sensitive,strict:null==r?void 0:r.strict}),i=(0,n.regexpToFunction)((null==r?void 0:r.regexModifier)?new RegExp(r.regexModifier(o.source),o.flags):o,t);return(e,n)=>{if("string"!=typeof e)return!1;let o=i(e);if(!o)return!1;if(null==r?void 0:r.removeUnnamedParams)for(let e of t)"number"==typeof e.name&&delete o.params[e.name];return{...n,...o.params}}}},2704:()=>{},2785:(e,r)=>{"use strict";function t(e){let r={};for(let[t,n]of e.entries()){let e=r[t];void 0===e?r[t]=n:Array.isArray(e)?e.push(n):r[t]=[e,n]}return r}function n(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function o(e){let r=new URLSearchParams;for(let[t,o]of Object.entries(e))if(Array.isArray(o))for(let e of o)r.append(t,n(e));else r.set(t,n(o));return r}function i(e){for(var r=arguments.length,t=Array(r>1?r-1:0),n=1;n<r;n++)t[n-1]=arguments[n];for(let r of t){for(let t of r.keys())e.delete(t);for(let[t,n]of r.entries())e.append(t,n)}return e}Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{assign:function(){return i},searchParamsToUrlQuery:function(){return t},urlQueryToSearchParams:function(){return o}})},2958:(e,r)=>{"use strict";function t(e){return e.replace(/\\/g,"/")}Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"normalizePathSep",{enumerable:!0,get:function(){return t}})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3293:(e,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"escapeStringRegexp",{enumerable:!0,get:function(){return o}});let t=/[|\\{}()[\]^$+*?.-]/,n=/[|\\{}()[\]^$+*?.-]/g;function o(e){return t.test(e)?e.replace(n,"\\$&"):e}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3736:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"parseRelativeUrl",{enumerable:!0,get:function(){return o}}),t(4827);let n=t(2785);function o(e,r,t){void 0===t&&(t=!0);let o=new URL("http://n"),i=r?new URL(r,o):e.startsWith(".")?new URL("http://n"):o,{pathname:a,searchParams:s,search:l,hash:c,href:d,origin:u}=new URL(e,i);if(u!==o.origin)throw Object.defineProperty(Error("invariant: invalid relative URL, router received "+e),"__NEXT_ERROR_CODE",{value:"E159",enumerable:!1,configurable:!0});return{pathname:a,query:t?(0,n.searchParamsToUrlQuery)(s):void 0,search:l,hash:c,href:d.slice(u.length)}}},3873:e=>{"use strict";e.exports=require("path")},4236:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6444,23)),Promise.resolve().then(t.t.bind(t,6042,23)),Promise.resolve().then(t.t.bind(t,8170,23)),Promise.resolve().then(t.t.bind(t,9477,23)),Promise.resolve().then(t.t.bind(t,9345,23)),Promise.resolve().then(t.t.bind(t,2089,23)),Promise.resolve().then(t.t.bind(t,6577,23)),Promise.resolve().then(t.t.bind(t,1307,23))},4396:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{getNamedMiddlewareRegex:function(){return g},getNamedRouteRegex:function(){return m},getRouteRegex:function(){return u},parseParameter:function(){return l}});let n=t(6143),o=t(1437),i=t(3293),a=t(2887),s=/^([^[]*)\[((?:\[[^\]]*\])|[^\]]+)\](.*)$/;function l(e){let r=e.match(s);return r?c(r[2]):c(e)}function c(e){let r=e.startsWith("[")&&e.endsWith("]");r&&(e=e.slice(1,-1));let t=e.startsWith("...");return t&&(e=e.slice(3)),{key:e,repeat:t,optional:r}}function d(e,r,t){let n={},l=1,d=[];for(let u of(0,a.removeTrailingSlash)(e).slice(1).split("/")){let e=o.INTERCEPTION_ROUTE_MARKERS.find(e=>u.startsWith(e)),a=u.match(s);if(e&&a&&a[2]){let{key:r,optional:t,repeat:o}=c(a[2]);n[r]={pos:l++,repeat:o,optional:t},d.push("/"+(0,i.escapeStringRegexp)(e)+"([^/]+?)")}else if(a&&a[2]){let{key:e,repeat:r,optional:o}=c(a[2]);n[e]={pos:l++,repeat:r,optional:o},t&&a[1]&&d.push("/"+(0,i.escapeStringRegexp)(a[1]));let s=r?o?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)";t&&a[1]&&(s=s.substring(1)),d.push(s)}else d.push("/"+(0,i.escapeStringRegexp)(u));r&&a&&a[3]&&d.push((0,i.escapeStringRegexp)(a[3]))}return{parameterizedRoute:d.join(""),groups:n}}function u(e,r){let{includeSuffix:t=!1,includePrefix:n=!1,excludeOptionalTrailingSlash:o=!1}=void 0===r?{}:r,{parameterizedRoute:i,groups:a}=d(e,t,n),s=i;return o||(s+="(?:/)?"),{re:RegExp("^"+s+"$"),groups:a}}function p(e){let r,{interceptionMarker:t,getSafeRouteKey:n,segment:o,routeKeys:a,keyPrefix:s,backreferenceDuplicateKeys:l}=e,{key:d,optional:u,repeat:p}=c(o),f=d.replace(/\W/g,"");s&&(f=""+s+f);let m=!1;(0===f.length||f.length>30)&&(m=!0),isNaN(parseInt(f.slice(0,1)))||(m=!0),m&&(f=n());let g=f in a;s?a[f]=""+s+d:a[f]=d;let h=t?(0,i.escapeStringRegexp)(t):"";return r=g&&l?"\\k<"+f+">":p?"(?<"+f+">.+?)":"(?<"+f+">[^/]+?)",u?"(?:/"+h+r+")?":"/"+h+r}function f(e,r,t,l,c){let d,u=(d=0,()=>{let e="",r=++d;for(;r>0;)e+=String.fromCharCode(97+(r-1)%26),r=Math.floor((r-1)/26);return e}),f={},m=[];for(let d of(0,a.removeTrailingSlash)(e).slice(1).split("/")){let e=o.INTERCEPTION_ROUTE_MARKERS.some(e=>d.startsWith(e)),a=d.match(s);if(e&&a&&a[2])m.push(p({getSafeRouteKey:u,interceptionMarker:a[1],segment:a[2],routeKeys:f,keyPrefix:r?n.NEXT_INTERCEPTION_MARKER_PREFIX:void 0,backreferenceDuplicateKeys:c}));else if(a&&a[2]){l&&a[1]&&m.push("/"+(0,i.escapeStringRegexp)(a[1]));let e=p({getSafeRouteKey:u,segment:a[2],routeKeys:f,keyPrefix:r?n.NEXT_QUERY_PARAM_PREFIX:void 0,backreferenceDuplicateKeys:c});l&&a[1]&&(e=e.substring(1)),m.push(e)}else m.push("/"+(0,i.escapeStringRegexp)(d));t&&a&&a[3]&&m.push((0,i.escapeStringRegexp)(a[3]))}return{namedParameterizedRoute:m.join(""),routeKeys:f}}function m(e,r){var t,n,o;let i=f(e,r.prefixRouteKeys,null!=(t=r.includeSuffix)&&t,null!=(n=r.includePrefix)&&n,null!=(o=r.backreferenceDuplicateKeys)&&o),a=i.namedParameterizedRoute;return r.excludeOptionalTrailingSlash||(a+="(?:/)?"),{...u(e,r),namedRegex:"^"+a+"$",routeKeys:i.routeKeys}}function g(e,r){let{parameterizedRoute:t}=d(e,!1,!1),{catchAll:n=!0}=r;if("/"===t)return{namedRegex:"^/"+(n?".*":"")+"$"};let{namedParameterizedRoute:o}=f(e,!1,!1,!1,!1);return{namedRegex:"^"+o+(n?"(?:(/.*)?)":"")+"$"}}},4722:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{normalizeAppPath:function(){return i},normalizeRscURL:function(){return a}});let n=t(5531),o=t(5499);function i(e){return(0,n.ensureLeadingSlash)(e.split("/").reduce((e,r,t,n)=>!r||(0,o.isGroupSegment)(r)||"@"===r[0]||("page"===r||"route"===r)&&t===n.length-1?e:e+"/"+r,""))}function a(e){return e.replace(/\.rsc($|\?)/,"$1")}},4827:(e,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{DecodeError:function(){return m},MiddlewareNotFoundError:function(){return x},MissingStaticPage:function(){return b},NormalizeError:function(){return g},PageNotFoundError:function(){return h},SP:function(){return p},ST:function(){return f},WEB_VITALS:function(){return t},execOnce:function(){return n},getDisplayName:function(){return l},getLocationOrigin:function(){return a},getURL:function(){return s},isAbsoluteUrl:function(){return i},isResSent:function(){return c},loadGetInitialProps:function(){return u},normalizeRepeatedSlashes:function(){return d},stringifyError:function(){return v}});let t=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let r,t=!1;return function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];return t||(t=!0,r=e(...o)),r}}let o=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,i=e=>o.test(e);function a(){let{protocol:e,hostname:r,port:t}=window.location;return e+"//"+r+(t?":"+t:"")}function s(){let{href:e}=window.location,r=a();return e.substring(r.length)}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function c(e){return e.finished||e.headersSent}function d(e){let r=e.split("?");return r[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(r[1]?"?"+r.slice(1).join("?"):"")}async function u(e,r){let t=r.res||r.ctx&&r.ctx.res;if(!e.getInitialProps)return r.ctx&&r.Component?{pageProps:await u(r.Component,r.ctx)}:{};let n=await e.getInitialProps(r);if(t&&c(t))return n;if(!n)throw Object.defineProperty(Error('"'+l(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let p="undefined"!=typeof performance,f=p&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class m extends Error{}class g extends Error{}class h extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class b extends Error{constructor(e,r){super(),this.message="Failed to load static file for page: "+e+" "+r}}class x extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function v(e){return JSON.stringify({message:e.message,stack:e.stack})}},5296:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var n=t(5239),o=t(8088),i=t(8170),a=t.n(i),s=t(893),l={};for(let e in s)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>s[e]);t.d(r,l);let c={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,597)),"/Users/<USER>/code/aidc-t-selection/dsc/app/page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,6055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,8014)),"/Users/<USER>/code/aidc-t-selection/dsc/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,6055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Users/<USER>/code/aidc-t-selection/dsc/app/page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},5362:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var r={};(()=>{function e(e,r){void 0===r&&(r={});for(var t=function(e){for(var r=[],t=0;t<e.length;){var n=e[t];if("*"===n||"+"===n||"?"===n){r.push({type:"MODIFIER",index:t,value:e[t++]});continue}if("\\"===n){r.push({type:"ESCAPED_CHAR",index:t++,value:e[t++]});continue}if("{"===n){r.push({type:"OPEN",index:t,value:e[t++]});continue}if("}"===n){r.push({type:"CLOSE",index:t,value:e[t++]});continue}if(":"===n){for(var o="",i=t+1;i<e.length;){var a=e.charCodeAt(i);if(a>=48&&a<=57||a>=65&&a<=90||a>=97&&a<=122||95===a){o+=e[i++];continue}break}if(!o)throw TypeError("Missing parameter name at "+t);r.push({type:"NAME",index:t,value:o}),t=i;continue}if("("===n){var s=1,l="",i=t+1;if("?"===e[i])throw TypeError('Pattern cannot start with "?" at '+i);for(;i<e.length;){if("\\"===e[i]){l+=e[i++]+e[i++];continue}if(")"===e[i]){if(0==--s){i++;break}}else if("("===e[i]&&(s++,"?"!==e[i+1]))throw TypeError("Capturing groups are not allowed at "+i);l+=e[i++]}if(s)throw TypeError("Unbalanced pattern at "+t);if(!l)throw TypeError("Missing pattern at "+t);r.push({type:"PATTERN",index:t,value:l}),t=i;continue}r.push({type:"CHAR",index:t,value:e[t++]})}return r.push({type:"END",index:t,value:""}),r}(e),n=r.prefixes,i=void 0===n?"./":n,a="[^"+o(r.delimiter||"/#?")+"]+?",s=[],l=0,c=0,d="",u=function(e){if(c<t.length&&t[c].type===e)return t[c++].value},p=function(e){var r=u(e);if(void 0!==r)return r;var n=t[c];throw TypeError("Unexpected "+n.type+" at "+n.index+", expected "+e)},f=function(){for(var e,r="";e=u("CHAR")||u("ESCAPED_CHAR");)r+=e;return r};c<t.length;){var m=u("CHAR"),g=u("NAME"),h=u("PATTERN");if(g||h){var b=m||"";-1===i.indexOf(b)&&(d+=b,b=""),d&&(s.push(d),d=""),s.push({name:g||l++,prefix:b,suffix:"",pattern:h||a,modifier:u("MODIFIER")||""});continue}var x=m||u("ESCAPED_CHAR");if(x){d+=x;continue}if(d&&(s.push(d),d=""),u("OPEN")){var b=f(),v=u("NAME")||"",y=u("PATTERN")||"",w=f();p("CLOSE"),s.push({name:v||(y?l++:""),pattern:v&&!y?a:y,prefix:b,suffix:w,modifier:u("MODIFIER")||""});continue}p("END")}return s}function t(e,r){void 0===r&&(r={});var t=i(r),n=r.encode,o=void 0===n?function(e){return e}:n,a=r.validate,s=void 0===a||a,l=e.map(function(e){if("object"==typeof e)return RegExp("^(?:"+e.pattern+")$",t)});return function(r){for(var t="",n=0;n<e.length;n++){var i=e[n];if("string"==typeof i){t+=i;continue}var a=r?r[i.name]:void 0,c="?"===i.modifier||"*"===i.modifier,d="*"===i.modifier||"+"===i.modifier;if(Array.isArray(a)){if(!d)throw TypeError('Expected "'+i.name+'" to not repeat, but got an array');if(0===a.length){if(c)continue;throw TypeError('Expected "'+i.name+'" to not be empty')}for(var u=0;u<a.length;u++){var p=o(a[u],i);if(s&&!l[n].test(p))throw TypeError('Expected all "'+i.name+'" to match "'+i.pattern+'", but got "'+p+'"');t+=i.prefix+p+i.suffix}continue}if("string"==typeof a||"number"==typeof a){var p=o(String(a),i);if(s&&!l[n].test(p))throw TypeError('Expected "'+i.name+'" to match "'+i.pattern+'", but got "'+p+'"');t+=i.prefix+p+i.suffix;continue}if(!c){var f=d?"an array":"a string";throw TypeError('Expected "'+i.name+'" to be '+f)}}return t}}function n(e,r,t){void 0===t&&(t={});var n=t.decode,o=void 0===n?function(e){return e}:n;return function(t){var n=e.exec(t);if(!n)return!1;for(var i=n[0],a=n.index,s=Object.create(null),l=1;l<n.length;l++)!function(e){if(void 0!==n[e]){var t=r[e-1];"*"===t.modifier||"+"===t.modifier?s[t.name]=n[e].split(t.prefix+t.suffix).map(function(e){return o(e,t)}):s[t.name]=o(n[e],t)}}(l);return{path:i,index:a,params:s}}}function o(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function i(e){return e&&e.sensitive?"":"i"}function a(e,r,t){void 0===t&&(t={});for(var n=t.strict,a=void 0!==n&&n,s=t.start,l=t.end,c=t.encode,d=void 0===c?function(e){return e}:c,u="["+o(t.endsWith||"")+"]|$",p="["+o(t.delimiter||"/#?")+"]",f=void 0===s||s?"^":"",m=0;m<e.length;m++){var g=e[m];if("string"==typeof g)f+=o(d(g));else{var h=o(d(g.prefix)),b=o(d(g.suffix));if(g.pattern)if(r&&r.push(g),h||b)if("+"===g.modifier||"*"===g.modifier){var x="*"===g.modifier?"?":"";f+="(?:"+h+"((?:"+g.pattern+")(?:"+b+h+"(?:"+g.pattern+"))*)"+b+")"+x}else f+="(?:"+h+"("+g.pattern+")"+b+")"+g.modifier;else f+="("+g.pattern+")"+g.modifier;else f+="(?:"+h+b+")"+g.modifier}}if(void 0===l||l)a||(f+=p+"?"),f+=t.endsWith?"(?="+u+")":"$";else{var v=e[e.length-1],y="string"==typeof v?p.indexOf(v[v.length-1])>-1:void 0===v;a||(f+="(?:"+p+"(?="+u+"))?"),y||(f+="(?="+p+"|"+u+")")}return new RegExp(f,i(t))}function s(r,t,n){if(r instanceof RegExp){if(!t)return r;var o=r.source.match(/\((?!\?)/g);if(o)for(var l=0;l<o.length;l++)t.push({name:l,prefix:"",suffix:"",modifier:"",pattern:""});return r}return Array.isArray(r)?RegExp("(?:"+r.map(function(e){return s(e,t,n).source}).join("|")+")",i(n)):a(e(r,n),t,n)}Object.defineProperty(r,"__esModule",{value:!0}),r.parse=e,r.compile=function(r,n){return t(e(r,n),n)},r.tokensToFunction=t,r.match=function(e,r){var t=[];return n(s(e,t,r),t,r)},r.regexpToFunction=n,r.tokensToRegexp=a,r.pathToRegexp=s})(),e.exports=r})()},5376:()=>{},5526:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{compileNonPath:function(){return d},matchHas:function(){return c},parseDestination:function(){return u},prepareDestination:function(){return p}});let n=t(5362),o=t(3293),i=t(6759),a=t(1437),s=t(8212);function l(e){return e.replace(/__ESC_COLON_/gi,":")}function c(e,r,t,n){void 0===t&&(t=[]),void 0===n&&(n=[]);let o={},i=t=>{let n,i=t.key;switch(t.type){case"header":i=i.toLowerCase(),n=e.headers[i];break;case"cookie":n="cookies"in e?e.cookies[t.key]:(0,s.getCookieParser)(e.headers)()[t.key];break;case"query":n=r[i];break;case"host":{let{host:r}=(null==e?void 0:e.headers)||{};n=null==r?void 0:r.split(":",1)[0].toLowerCase()}}if(!t.value&&n)return o[function(e){let r="";for(let t=0;t<e.length;t++){let n=e.charCodeAt(t);(n>64&&n<91||n>96&&n<123)&&(r+=e[t])}return r}(i)]=n,!0;if(n){let e=RegExp("^"+t.value+"$"),r=Array.isArray(n)?n.slice(-1)[0].match(e):n.match(e);if(r)return Array.isArray(r)&&(r.groups?Object.keys(r.groups).forEach(e=>{o[e]=r.groups[e]}):"host"===t.type&&r[0]&&(o.host=r[0])),!0}return!1};return!(!t.every(e=>i(e))||n.some(e=>i(e)))&&o}function d(e,r){if(!e.includes(":"))return e;for(let t of Object.keys(r))e.includes(":"+t)&&(e=e.replace(RegExp(":"+t+"\\*","g"),":"+t+"--ESCAPED_PARAM_ASTERISKS").replace(RegExp(":"+t+"\\?","g"),":"+t+"--ESCAPED_PARAM_QUESTION").replace(RegExp(":"+t+"\\+","g"),":"+t+"--ESCAPED_PARAM_PLUS").replace(RegExp(":"+t+"(?!\\w)","g"),"--ESCAPED_PARAM_COLON"+t));return e=e.replace(/(:|\*|\?|\+|\(|\)|\{|\})/g,"\\$1").replace(/--ESCAPED_PARAM_PLUS/g,"+").replace(/--ESCAPED_PARAM_COLON/g,":").replace(/--ESCAPED_PARAM_QUESTION/g,"?").replace(/--ESCAPED_PARAM_ASTERISKS/g,"*"),(0,n.compile)("/"+e,{validate:!1})(r).slice(1)}function u(e){let r=e.destination;for(let t of Object.keys({...e.params,...e.query}))t&&(r=r.replace(RegExp(":"+(0,o.escapeStringRegexp)(t),"g"),"__ESC_COLON_"+t));let t=(0,i.parseUrl)(r),n=t.pathname;n&&(n=l(n));let a=t.href;a&&(a=l(a));let s=t.hostname;s&&(s=l(s));let c=t.hash;return c&&(c=l(c)),{...t,pathname:n,hostname:s,href:a,hash:c}}function p(e){let r,t,o=Object.assign({},e.query),i=u(e),{hostname:s,query:c}=i,p=i.pathname;i.hash&&(p=""+p+i.hash);let f=[],m=[];for(let e of((0,n.pathToRegexp)(p,m),m))f.push(e.name);if(s){let e=[];for(let r of((0,n.pathToRegexp)(s,e),e))f.push(r.name)}let g=(0,n.compile)(p,{validate:!1});for(let[t,o]of(s&&(r=(0,n.compile)(s,{validate:!1})),Object.entries(c)))Array.isArray(o)?c[t]=o.map(r=>d(l(r),e.params)):"string"==typeof o&&(c[t]=d(l(o),e.params));let h=Object.keys(e.params).filter(e=>"nextInternalLocale"!==e);if(e.appendParamsToQuery&&!h.some(e=>f.includes(e)))for(let r of h)r in c||(c[r]=e.params[r]);if((0,a.isInterceptionRouteAppPath)(p))for(let r of p.split("/")){let t=a.INTERCEPTION_ROUTE_MARKERS.find(e=>r.startsWith(e));if(t){"(..)(..)"===t?(e.params["0"]="(..)",e.params["1"]="(..)"):e.params["0"]=t;break}}try{let[n,o]=(t=g(e.params)).split("#",2);r&&(i.hostname=r(e.params)),i.pathname=n,i.hash=(o?"#":"")+(o||""),delete i.search}catch(e){if(e.message.match(/Expected .*? to not repeat, but got an array/))throw Object.defineProperty(Error("To use a multi-match in the destination you must add `*` at the end of the param name to signify it should repeat. https://nextjs.org/docs/messages/invalid-multi-match"),"__NEXT_ERROR_CODE",{value:"E329",enumerable:!1,configurable:!0});throw e}return i.query={...o,...i.query},{newUrl:t,destQuery:c,parsedDestination:i}}},5531:(e,r)=>{"use strict";function t(e){return e.startsWith("/")?e:"/"+e}Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"ensureLeadingSlash",{enumerable:!0,get:function(){return t}})},6055:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});var n=t(1658);let o=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},6341:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{getPreviouslyRevalidatedTags:function(){return b},getUtils:function(){return h},interpolateDynamicPath:function(){return m},normalizeDynamicRouteParams:function(){return g},normalizeVercelUrl:function(){return f}});let n=t(9551),o=t(1959),i=t(2437),a=t(4396),s=t(8034),l=t(5526),c=t(2887),d=t(4722),u=t(6143),p=t(7912);function f(e,r,t){let o=(0,n.parse)(e.url,!0);for(let e of(delete o.search,Object.keys(o.query))){let n=e!==u.NEXT_QUERY_PARAM_PREFIX&&e.startsWith(u.NEXT_QUERY_PARAM_PREFIX),i=e!==u.NEXT_INTERCEPTION_MARKER_PREFIX&&e.startsWith(u.NEXT_INTERCEPTION_MARKER_PREFIX);(n||i||r.includes(e)||t&&Object.keys(t.groups).includes(e))&&delete o.query[e]}e.url=(0,n.format)(o)}function m(e,r,t){if(!t)return e;for(let n of Object.keys(t.groups)){let o,{optional:i,repeat:a}=t.groups[n],s=`[${a?"...":""}${n}]`;i&&(s=`[${s}]`);let l=r[n];o=Array.isArray(l)?l.map(e=>e&&encodeURIComponent(e)).join("/"):l?encodeURIComponent(l):"",e=e.replaceAll(s,o)}return e}function g(e,r,t,n){let o={};for(let i of Object.keys(r.groups)){let a=e[i];"string"==typeof a?a=(0,d.normalizeRscURL)(a):Array.isArray(a)&&(a=a.map(d.normalizeRscURL));let s=t[i],l=r.groups[i].optional;if((Array.isArray(s)?s.some(e=>Array.isArray(a)?a.some(r=>r.includes(e)):null==a?void 0:a.includes(e)):null==a?void 0:a.includes(s))||void 0===a&&!(l&&n))return{params:{},hasValidParams:!1};l&&(!a||Array.isArray(a)&&1===a.length&&("index"===a[0]||a[0]===`[[...${i}]]`))&&(a=void 0,delete e[i]),a&&"string"==typeof a&&r.groups[i].repeat&&(a=a.split("/")),a&&(o[i]=a)}return{params:o,hasValidParams:!0}}function h({page:e,i18n:r,basePath:t,rewrites:n,pageIsDynamic:d,trailingSlash:u,caseSensitive:h}){let b,x,v;return d&&(b=(0,a.getNamedRouteRegex)(e,{prefixRouteKeys:!1}),v=(x=(0,s.getRouteMatcher)(b))(e)),{handleRewrites:function(a,s){let p={},f=s.pathname,m=n=>{let c=(0,i.getPathMatch)(n.source+(u?"(/)?":""),{removeUnnamedParams:!0,strict:!0,sensitive:!!h});if(!s.pathname)return!1;let m=c(s.pathname);if((n.has||n.missing)&&m){let e=(0,l.matchHas)(a,s.query,n.has,n.missing);e?Object.assign(m,e):m=!1}if(m){let{parsedDestination:i,destQuery:a}=(0,l.prepareDestination)({appendParamsToQuery:!0,destination:n.destination,params:m,query:s.query});if(i.protocol)return!0;if(Object.assign(p,a,m),Object.assign(s.query,i.query),delete i.query,Object.assign(s,i),!(f=s.pathname))return!1;if(t&&(f=f.replace(RegExp(`^${t}`),"")||"/"),r){let e=(0,o.normalizeLocalePath)(f,r.locales);f=e.pathname,s.query.nextInternalLocale=e.detectedLocale||m.nextInternalLocale}if(f===e)return!0;if(d&&x){let e=x(f);if(e)return s.query={...s.query,...e},!0}}return!1};for(let e of n.beforeFiles||[])m(e);if(f!==e){let r=!1;for(let e of n.afterFiles||[])if(r=m(e))break;if(!r&&!(()=>{let r=(0,c.removeTrailingSlash)(f||"");return r===(0,c.removeTrailingSlash)(e)||(null==x?void 0:x(r))})()){for(let e of n.fallback||[])if(r=m(e))break}}return p},defaultRouteRegex:b,dynamicRouteMatcher:x,defaultRouteMatches:v,getParamsFromRouteMatches:function(e){if(!b)return null;let{groups:r,routeKeys:t}=b,n=(0,s.getRouteMatcher)({re:{exec:e=>{let n=Object.fromEntries(new URLSearchParams(e));for(let[e,r]of Object.entries(n)){let t=(0,p.normalizeNextQueryParam)(e);t&&(n[t]=r,delete n[e])}let o={};for(let e of Object.keys(t)){let i=t[e];if(!i)continue;let a=r[i],s=n[e];if(!a.optional&&!s)return null;o[a.pos]=s}return o}},groups:r})(e);return n||null},normalizeDynamicRouteParams:(e,r)=>b&&v?g(e,b,v,r):{params:{},hasValidParams:!1},normalizeVercelUrl:(e,r)=>f(e,r,b),interpolateDynamicPath:(e,r)=>m(e,r,b)}}function b(e,r){return"string"==typeof e[u.NEXT_CACHE_REVALIDATED_TAGS_HEADER]&&e[u.NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER]===r?e[u.NEXT_CACHE_REVALIDATED_TAGS_HEADER].split(","):[]}},6415:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var r={};(()=>{r.parse=function(r,t){if("string"!=typeof r)throw TypeError("argument str must be a string");for(var o={},i=r.split(n),a=(t||{}).decode||e,s=0;s<i.length;s++){var l=i[s],c=l.indexOf("=");if(!(c<0)){var d=l.substr(0,c).trim(),u=l.substr(++c,l.length).trim();'"'==u[0]&&(u=u.slice(1,-1)),void 0==o[d]&&(o[d]=function(e,r){try{return r(e)}catch(r){return e}}(u,a))}}return o},r.serialize=function(e,r,n){var i=n||{},a=i.encode||t;if("function"!=typeof a)throw TypeError("option encode is invalid");if(!o.test(e))throw TypeError("argument name is invalid");var s=a(r);if(s&&!o.test(s))throw TypeError("argument val is invalid");var l=e+"="+s;if(null!=i.maxAge){var c=i.maxAge-0;if(isNaN(c)||!isFinite(c))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(c)}if(i.domain){if(!o.test(i.domain))throw TypeError("option domain is invalid");l+="; Domain="+i.domain}if(i.path){if(!o.test(i.path))throw TypeError("option path is invalid");l+="; Path="+i.path}if(i.expires){if("function"!=typeof i.expires.toUTCString)throw TypeError("option expires is invalid");l+="; Expires="+i.expires.toUTCString()}if(i.httpOnly&&(l+="; HttpOnly"),i.secure&&(l+="; Secure"),i.sameSite)switch("string"==typeof i.sameSite?i.sameSite.toLowerCase():i.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var e=decodeURIComponent,t=encodeURIComponent,n=/; */,o=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=r})()},6759:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"parseUrl",{enumerable:!0,get:function(){return i}});let n=t(2785),o=t(3736);function i(e){if(e.startsWith("/"))return(0,o.parseRelativeUrl)(e);let r=new URL(e);return{hash:r.hash,hostname:r.hostname,href:r.href,pathname:r.pathname,port:r.port,protocol:r.protocol,query:(0,n.searchParamsToUrlQuery)(r.searchParams),search:r.search}}},7011:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>eI});var n=t(687),o=t(3210);let i=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,t)=>t?t.toUpperCase():r.toLowerCase()),s=e=>{let r=a(e);return r.charAt(0).toUpperCase()+r.slice(1)},l=(...e)=>e.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim(),c=e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let u=(0,o.forwardRef)(({color:e="currentColor",size:r=24,strokeWidth:t=2,absoluteStrokeWidth:n,className:i="",children:a,iconNode:s,...u},p)=>(0,o.createElement)("svg",{ref:p,...d,width:r,height:r,stroke:e,strokeWidth:n?24*Number(t)/Number(r):t,className:l("lucide",i),...!a&&!c(u)&&{"aria-hidden":"true"},...u},[...s.map(([e,r])=>(0,o.createElement)(e,r)),...Array.isArray(a)?a:[a]])),p=((e,r)=>{let t=(0,o.forwardRef)(({className:t,...n},a)=>(0,o.createElement)(u,{ref:a,iconNode:r,className:l(`lucide-${i(s(e))}`,`lucide-${e}`,t),...n}));return t.displayName=s(e),t})("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]);function f(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}var m=function(e){let r=function(e){let r=o.forwardRef((e,r)=>{let{children:t,...n}=e;if(o.isValidElement(t)){var i;let e,a,s=(i=t,(a=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(a=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),l=function(e,r){let t={...r};for(let n in r){let o=e[n],i=r[n];/^on[A-Z]/.test(n)?o&&i?t[n]=(...e)=>{let r=i(...e);return o(...e),r}:o&&(t[n]=o):"style"===n?t[n]={...o,...i}:"className"===n&&(t[n]=[o,i].filter(Boolean).join(" "))}return{...e,...t}}(n,t.props);return t.type!==o.Fragment&&(l.ref=r?function(...e){return r=>{let t=!1,n=e.map(e=>{let n=f(e,r);return t||"function"!=typeof n||(t=!0),n});if(t)return()=>{for(let r=0;r<n.length;r++){let t=n[r];"function"==typeof t?t():f(e[r],null)}}}}(r,s):s),o.cloneElement(t,l)}return o.Children.count(t)>1?o.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),t=o.forwardRef((e,t)=>{let{children:i,...a}=e,s=o.Children.toArray(i),l=s.find(h);if(l){let e=l.props.children,i=s.map(r=>r!==l?r:o.Children.count(e)>1?o.Children.only(null):o.isValidElement(e)?e.props.children:null);return(0,n.jsx)(r,{...a,ref:t,children:o.isValidElement(e)?o.cloneElement(e,void 0,i):null})}return(0,n.jsx)(r,{...a,ref:t,children:i})});return t.displayName=`${e}.Slot`,t}("Slot"),g=Symbol("radix.slottable");function h(e){return o.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===g}function b(){for(var e,r,t=0,n="",o=arguments.length;t<o;t++)(e=arguments[t])&&(r=function e(r){var t,n,o="";if("string"==typeof r||"number"==typeof r)o+=r;else if("object"==typeof r)if(Array.isArray(r)){var i=r.length;for(t=0;t<i;t++)r[t]&&(n=e(r[t]))&&(o&&(o+=" "),o+=n)}else for(n in r)r[n]&&(o&&(o+=" "),o+=n);return o}(e))&&(n&&(n+=" "),n+=r);return n}let x=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,v=(e,r)=>t=>{var n;if((null==r?void 0:r.variants)==null)return b(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:o,defaultVariants:i}=r,a=Object.keys(o).map(e=>{let r=null==t?void 0:t[e],n=null==i?void 0:i[e];if(null===r)return null;let a=x(r)||x(n);return o[e][a]}),s=t&&Object.entries(t).reduce((e,r)=>{let[t,n]=r;return void 0===n||(e[t]=n),e},{});return b(e,a,null==r||null==(n=r.compoundVariants)?void 0:n.reduce((e,r)=>{let{class:t,className:n,...o}=r;return Object.entries(o).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...i,...s}[r]):({...i,...s})[r]===t})?[...e,t,n]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)},y=e=>{let r=P(e),{conflictingClassGroups:t,conflictingClassGroupModifiers:n}=e;return{getClassGroupId:e=>{let t=e.split("-");return""===t[0]&&1!==t.length&&t.shift(),w(t,r)||E(e)},getConflictingClassGroupIds:(e,r)=>{let o=t[e]||[];return r&&n[e]?[...o,...n[e]]:o}}},w=(e,r)=>{if(0===e.length)return r.classGroupId;let t=e[0],n=r.nextPart.get(t),o=n?w(e.slice(1),n):void 0;if(o)return o;if(0===r.validators.length)return;let i=e.join("-");return r.validators.find(({validator:e})=>e(i))?.classGroupId},k=/^\[(.+)\]$/,E=e=>{if(k.test(e)){let r=k.exec(e)[1],t=r?.substring(0,r.indexOf(":"));if(t)return"arbitrary.."+t}},P=e=>{let{theme:r,classGroups:t}=e,n={nextPart:new Map,validators:[]};for(let e in t)R(t[e],n,e,r);return n},R=(e,r,t,n)=>{e.forEach(e=>{if("string"==typeof e){(""===e?r:j(r,e)).classGroupId=t;return}if("function"==typeof e)return _(e)?void R(e(n),r,t,n):void r.validators.push({validator:e,classGroupId:t});Object.entries(e).forEach(([e,o])=>{R(o,j(r,e),t,n)})})},j=(e,r)=>{let t=e;return r.split("-").forEach(e=>{t.nextPart.has(e)||t.nextPart.set(e,{nextPart:new Map,validators:[]}),t=t.nextPart.get(e)}),t},_=e=>e.isThemeGetter,N=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let r=0,t=new Map,n=new Map,o=(o,i)=>{t.set(o,i),++r>e&&(r=0,n=t,t=new Map)};return{get(e){let r=t.get(e);return void 0!==r?r:void 0!==(r=n.get(e))?(o(e,r),r):void 0},set(e,r){t.has(e)?t.set(e,r):o(e,r)}}},A=e=>{let{prefix:r,experimentalParseClassName:t}=e,n=e=>{let r,t=[],n=0,o=0,i=0;for(let a=0;a<e.length;a++){let s=e[a];if(0===n&&0===o){if(":"===s){t.push(e.slice(i,a)),i=a+1;continue}if("/"===s){r=a;continue}}"["===s?n++:"]"===s?n--:"("===s?o++:")"===s&&o--}let a=0===t.length?e:e.substring(i),s=O(a);return{modifiers:t,hasImportantModifier:s!==a,baseClassName:s,maybePostfixModifierPosition:r&&r>i?r-i:void 0}};if(r){let e=r+":",t=n;n=r=>r.startsWith(e)?t(r.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:r,maybePostfixModifierPosition:void 0}}if(t){let e=n;n=r=>t({className:r,parseClassName:e})}return n},O=e=>e.endsWith("!")?e.substring(0,e.length-1):e.startsWith("!")?e.substring(1):e,S=e=>{let r=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let t=[],n=[];return e.forEach(e=>{"["===e[0]||r[e]?(t.push(...n.sort(),e),n=[]):n.push(e)}),t.push(...n.sort()),t}},T=e=>({cache:N(e.cacheSize),parseClassName:A(e),sortModifiers:S(e),...y(e)}),C=/\s+/,M=(e,r)=>{let{parseClassName:t,getClassGroupId:n,getConflictingClassGroupIds:o,sortModifiers:i}=r,a=[],s=e.trim().split(C),l="";for(let e=s.length-1;e>=0;e-=1){let r=s[e],{isExternal:c,modifiers:d,hasImportantModifier:u,baseClassName:p,maybePostfixModifierPosition:f}=t(r);if(c){l=r+(l.length>0?" "+l:l);continue}let m=!!f,g=n(m?p.substring(0,f):p);if(!g){if(!m||!(g=n(p))){l=r+(l.length>0?" "+l:l);continue}m=!1}let h=i(d).join(":"),b=u?h+"!":h,x=b+g;if(a.includes(x))continue;a.push(x);let v=o(g,m);for(let e=0;e<v.length;++e){let r=v[e];a.push(b+r)}l=r+(l.length>0?" "+l:l)}return l};function I(){let e,r,t=0,n="";for(;t<arguments.length;)(e=arguments[t++])&&(r=z(e))&&(n&&(n+=" "),n+=r);return n}let z=e=>{let r;if("string"==typeof e)return e;let t="";for(let n=0;n<e.length;n++)e[n]&&(r=z(e[n]))&&(t&&(t+=" "),t+=r);return t},$=e=>{let r=r=>r[e]||[];return r.isThemeGetter=!0,r},D=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,U=/^\((?:(\w[\w-]*):)?(.+)\)$/i,L=/^\d+\/\d+$/,W=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,q=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,G=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,F=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,X=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,H=e=>L.test(e),Z=e=>!!e&&!Number.isNaN(Number(e)),K=e=>!!e&&Number.isInteger(Number(e)),V=e=>e.endsWith("%")&&Z(e.slice(0,-1)),Q=e=>W.test(e),B=()=>!0,J=e=>q.test(e)&&!G.test(e),Y=()=>!1,ee=e=>F.test(e),er=e=>X.test(e),et=e=>!eo(e)&&!ed(e),en=e=>eb(e,ew,Y),eo=e=>D.test(e),ei=e=>eb(e,ek,J),ea=e=>eb(e,eE,Z),es=e=>eb(e,ev,Y),el=e=>eb(e,ey,er),ec=e=>eb(e,eR,ee),ed=e=>U.test(e),eu=e=>ex(e,ek),ep=e=>ex(e,eP),ef=e=>ex(e,ev),em=e=>ex(e,ew),eg=e=>ex(e,ey),eh=e=>ex(e,eR,!0),eb=(e,r,t)=>{let n=D.exec(e);return!!n&&(n[1]?r(n[1]):t(n[2]))},ex=(e,r,t=!1)=>{let n=U.exec(e);return!!n&&(n[1]?r(n[1]):t)},ev=e=>"position"===e||"percentage"===e,ey=e=>"image"===e||"url"===e,ew=e=>"length"===e||"size"===e||"bg-size"===e,ek=e=>"length"===e,eE=e=>"number"===e,eP=e=>"family-name"===e,eR=e=>"shadow"===e;Symbol.toStringTag;let ej=function(e,...r){let t,n,o,i=function(s){return n=(t=T(r.reduce((e,r)=>r(e),e()))).cache.get,o=t.cache.set,i=a,a(s)};function a(e){let r=n(e);if(r)return r;let i=M(e,t);return o(e,i),i}return function(){return i(I.apply(null,arguments))}}(()=>{let e=$("color"),r=$("font"),t=$("text"),n=$("font-weight"),o=$("tracking"),i=$("leading"),a=$("breakpoint"),s=$("container"),l=$("spacing"),c=$("radius"),d=$("shadow"),u=$("inset-shadow"),p=$("text-shadow"),f=$("drop-shadow"),m=$("blur"),g=$("perspective"),h=$("aspect"),b=$("ease"),x=$("animate"),v=()=>["auto","avoid","all","avoid-page","page","left","right","column"],y=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],w=()=>[...y(),ed,eo],k=()=>["auto","hidden","clip","visible","scroll"],E=()=>["auto","contain","none"],P=()=>[ed,eo,l],R=()=>[H,"full","auto",...P()],j=()=>[K,"none","subgrid",ed,eo],_=()=>["auto",{span:["full",K,ed,eo]},K,ed,eo],N=()=>[K,"auto",ed,eo],A=()=>["auto","min","max","fr",ed,eo],O=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],S=()=>["start","end","center","stretch","center-safe","end-safe"],T=()=>["auto",...P()],C=()=>[H,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...P()],M=()=>[e,ed,eo],I=()=>[...y(),ef,es,{position:[ed,eo]}],z=()=>["no-repeat",{repeat:["","x","y","space","round"]}],D=()=>["auto","cover","contain",em,en,{size:[ed,eo]}],U=()=>[V,eu,ei],L=()=>["","none","full",c,ed,eo],W=()=>["",Z,eu,ei],q=()=>["solid","dashed","dotted","double"],G=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],F=()=>[Z,V,ef,es],X=()=>["","none",m,ed,eo],J=()=>["none",Z,ed,eo],Y=()=>["none",Z,ed,eo],ee=()=>[Z,ed,eo],er=()=>[H,"full",...P()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[Q],breakpoint:[Q],color:[B],container:[Q],"drop-shadow":[Q],ease:["in","out","in-out"],font:[et],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[Q],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[Q],shadow:[Q],spacing:["px",Z],text:[Q],"text-shadow":[Q],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",H,eo,ed,h]}],container:["container"],columns:[{columns:[Z,eo,ed,s]}],"break-after":[{"break-after":v()}],"break-before":[{"break-before":v()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:w()}],overflow:[{overflow:k()}],"overflow-x":[{"overflow-x":k()}],"overflow-y":[{"overflow-y":k()}],overscroll:[{overscroll:E()}],"overscroll-x":[{"overscroll-x":E()}],"overscroll-y":[{"overscroll-y":E()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:R()}],"inset-x":[{"inset-x":R()}],"inset-y":[{"inset-y":R()}],start:[{start:R()}],end:[{end:R()}],top:[{top:R()}],right:[{right:R()}],bottom:[{bottom:R()}],left:[{left:R()}],visibility:["visible","invisible","collapse"],z:[{z:[K,"auto",ed,eo]}],basis:[{basis:[H,"full","auto",s,...P()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[Z,H,"auto","initial","none",eo]}],grow:[{grow:["",Z,ed,eo]}],shrink:[{shrink:["",Z,ed,eo]}],order:[{order:[K,"first","last","none",ed,eo]}],"grid-cols":[{"grid-cols":j()}],"col-start-end":[{col:_()}],"col-start":[{"col-start":N()}],"col-end":[{"col-end":N()}],"grid-rows":[{"grid-rows":j()}],"row-start-end":[{row:_()}],"row-start":[{"row-start":N()}],"row-end":[{"row-end":N()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":A()}],"auto-rows":[{"auto-rows":A()}],gap:[{gap:P()}],"gap-x":[{"gap-x":P()}],"gap-y":[{"gap-y":P()}],"justify-content":[{justify:[...O(),"normal"]}],"justify-items":[{"justify-items":[...S(),"normal"]}],"justify-self":[{"justify-self":["auto",...S()]}],"align-content":[{content:["normal",...O()]}],"align-items":[{items:[...S(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...S(),{baseline:["","last"]}]}],"place-content":[{"place-content":O()}],"place-items":[{"place-items":[...S(),"baseline"]}],"place-self":[{"place-self":["auto",...S()]}],p:[{p:P()}],px:[{px:P()}],py:[{py:P()}],ps:[{ps:P()}],pe:[{pe:P()}],pt:[{pt:P()}],pr:[{pr:P()}],pb:[{pb:P()}],pl:[{pl:P()}],m:[{m:T()}],mx:[{mx:T()}],my:[{my:T()}],ms:[{ms:T()}],me:[{me:T()}],mt:[{mt:T()}],mr:[{mr:T()}],mb:[{mb:T()}],ml:[{ml:T()}],"space-x":[{"space-x":P()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":P()}],"space-y-reverse":["space-y-reverse"],size:[{size:C()}],w:[{w:[s,"screen",...C()]}],"min-w":[{"min-w":[s,"screen","none",...C()]}],"max-w":[{"max-w":[s,"screen","none","prose",{screen:[a]},...C()]}],h:[{h:["screen","lh",...C()]}],"min-h":[{"min-h":["screen","lh","none",...C()]}],"max-h":[{"max-h":["screen","lh",...C()]}],"font-size":[{text:["base",t,eu,ei]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[n,ed,ea]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",V,eo]}],"font-family":[{font:[ep,eo,r]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[o,ed,eo]}],"line-clamp":[{"line-clamp":[Z,"none",ed,ea]}],leading:[{leading:[i,...P()]}],"list-image":[{"list-image":["none",ed,eo]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",ed,eo]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:M()}],"text-color":[{text:M()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...q(),"wavy"]}],"text-decoration-thickness":[{decoration:[Z,"from-font","auto",ed,ei]}],"text-decoration-color":[{decoration:M()}],"underline-offset":[{"underline-offset":[Z,"auto",ed,eo]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:P()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",ed,eo]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",ed,eo]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:I()}],"bg-repeat":[{bg:z()}],"bg-size":[{bg:D()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},K,ed,eo],radial:["",ed,eo],conic:[K,ed,eo]},eg,el]}],"bg-color":[{bg:M()}],"gradient-from-pos":[{from:U()}],"gradient-via-pos":[{via:U()}],"gradient-to-pos":[{to:U()}],"gradient-from":[{from:M()}],"gradient-via":[{via:M()}],"gradient-to":[{to:M()}],rounded:[{rounded:L()}],"rounded-s":[{"rounded-s":L()}],"rounded-e":[{"rounded-e":L()}],"rounded-t":[{"rounded-t":L()}],"rounded-r":[{"rounded-r":L()}],"rounded-b":[{"rounded-b":L()}],"rounded-l":[{"rounded-l":L()}],"rounded-ss":[{"rounded-ss":L()}],"rounded-se":[{"rounded-se":L()}],"rounded-ee":[{"rounded-ee":L()}],"rounded-es":[{"rounded-es":L()}],"rounded-tl":[{"rounded-tl":L()}],"rounded-tr":[{"rounded-tr":L()}],"rounded-br":[{"rounded-br":L()}],"rounded-bl":[{"rounded-bl":L()}],"border-w":[{border:W()}],"border-w-x":[{"border-x":W()}],"border-w-y":[{"border-y":W()}],"border-w-s":[{"border-s":W()}],"border-w-e":[{"border-e":W()}],"border-w-t":[{"border-t":W()}],"border-w-r":[{"border-r":W()}],"border-w-b":[{"border-b":W()}],"border-w-l":[{"border-l":W()}],"divide-x":[{"divide-x":W()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":W()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...q(),"hidden","none"]}],"divide-style":[{divide:[...q(),"hidden","none"]}],"border-color":[{border:M()}],"border-color-x":[{"border-x":M()}],"border-color-y":[{"border-y":M()}],"border-color-s":[{"border-s":M()}],"border-color-e":[{"border-e":M()}],"border-color-t":[{"border-t":M()}],"border-color-r":[{"border-r":M()}],"border-color-b":[{"border-b":M()}],"border-color-l":[{"border-l":M()}],"divide-color":[{divide:M()}],"outline-style":[{outline:[...q(),"none","hidden"]}],"outline-offset":[{"outline-offset":[Z,ed,eo]}],"outline-w":[{outline:["",Z,eu,ei]}],"outline-color":[{outline:M()}],shadow:[{shadow:["","none",d,eh,ec]}],"shadow-color":[{shadow:M()}],"inset-shadow":[{"inset-shadow":["none",u,eh,ec]}],"inset-shadow-color":[{"inset-shadow":M()}],"ring-w":[{ring:W()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:M()}],"ring-offset-w":[{"ring-offset":[Z,ei]}],"ring-offset-color":[{"ring-offset":M()}],"inset-ring-w":[{"inset-ring":W()}],"inset-ring-color":[{"inset-ring":M()}],"text-shadow":[{"text-shadow":["none",p,eh,ec]}],"text-shadow-color":[{"text-shadow":M()}],opacity:[{opacity:[Z,ed,eo]}],"mix-blend":[{"mix-blend":[...G(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":G()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[Z]}],"mask-image-linear-from-pos":[{"mask-linear-from":F()}],"mask-image-linear-to-pos":[{"mask-linear-to":F()}],"mask-image-linear-from-color":[{"mask-linear-from":M()}],"mask-image-linear-to-color":[{"mask-linear-to":M()}],"mask-image-t-from-pos":[{"mask-t-from":F()}],"mask-image-t-to-pos":[{"mask-t-to":F()}],"mask-image-t-from-color":[{"mask-t-from":M()}],"mask-image-t-to-color":[{"mask-t-to":M()}],"mask-image-r-from-pos":[{"mask-r-from":F()}],"mask-image-r-to-pos":[{"mask-r-to":F()}],"mask-image-r-from-color":[{"mask-r-from":M()}],"mask-image-r-to-color":[{"mask-r-to":M()}],"mask-image-b-from-pos":[{"mask-b-from":F()}],"mask-image-b-to-pos":[{"mask-b-to":F()}],"mask-image-b-from-color":[{"mask-b-from":M()}],"mask-image-b-to-color":[{"mask-b-to":M()}],"mask-image-l-from-pos":[{"mask-l-from":F()}],"mask-image-l-to-pos":[{"mask-l-to":F()}],"mask-image-l-from-color":[{"mask-l-from":M()}],"mask-image-l-to-color":[{"mask-l-to":M()}],"mask-image-x-from-pos":[{"mask-x-from":F()}],"mask-image-x-to-pos":[{"mask-x-to":F()}],"mask-image-x-from-color":[{"mask-x-from":M()}],"mask-image-x-to-color":[{"mask-x-to":M()}],"mask-image-y-from-pos":[{"mask-y-from":F()}],"mask-image-y-to-pos":[{"mask-y-to":F()}],"mask-image-y-from-color":[{"mask-y-from":M()}],"mask-image-y-to-color":[{"mask-y-to":M()}],"mask-image-radial":[{"mask-radial":[ed,eo]}],"mask-image-radial-from-pos":[{"mask-radial-from":F()}],"mask-image-radial-to-pos":[{"mask-radial-to":F()}],"mask-image-radial-from-color":[{"mask-radial-from":M()}],"mask-image-radial-to-color":[{"mask-radial-to":M()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":y()}],"mask-image-conic-pos":[{"mask-conic":[Z]}],"mask-image-conic-from-pos":[{"mask-conic-from":F()}],"mask-image-conic-to-pos":[{"mask-conic-to":F()}],"mask-image-conic-from-color":[{"mask-conic-from":M()}],"mask-image-conic-to-color":[{"mask-conic-to":M()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:I()}],"mask-repeat":[{mask:z()}],"mask-size":[{mask:D()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",ed,eo]}],filter:[{filter:["","none",ed,eo]}],blur:[{blur:X()}],brightness:[{brightness:[Z,ed,eo]}],contrast:[{contrast:[Z,ed,eo]}],"drop-shadow":[{"drop-shadow":["","none",f,eh,ec]}],"drop-shadow-color":[{"drop-shadow":M()}],grayscale:[{grayscale:["",Z,ed,eo]}],"hue-rotate":[{"hue-rotate":[Z,ed,eo]}],invert:[{invert:["",Z,ed,eo]}],saturate:[{saturate:[Z,ed,eo]}],sepia:[{sepia:["",Z,ed,eo]}],"backdrop-filter":[{"backdrop-filter":["","none",ed,eo]}],"backdrop-blur":[{"backdrop-blur":X()}],"backdrop-brightness":[{"backdrop-brightness":[Z,ed,eo]}],"backdrop-contrast":[{"backdrop-contrast":[Z,ed,eo]}],"backdrop-grayscale":[{"backdrop-grayscale":["",Z,ed,eo]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[Z,ed,eo]}],"backdrop-invert":[{"backdrop-invert":["",Z,ed,eo]}],"backdrop-opacity":[{"backdrop-opacity":[Z,ed,eo]}],"backdrop-saturate":[{"backdrop-saturate":[Z,ed,eo]}],"backdrop-sepia":[{"backdrop-sepia":["",Z,ed,eo]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":P()}],"border-spacing-x":[{"border-spacing-x":P()}],"border-spacing-y":[{"border-spacing-y":P()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",ed,eo]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[Z,"initial",ed,eo]}],ease:[{ease:["linear","initial",b,ed,eo]}],delay:[{delay:[Z,ed,eo]}],animate:[{animate:["none",x,ed,eo]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[g,ed,eo]}],"perspective-origin":[{"perspective-origin":w()}],rotate:[{rotate:J()}],"rotate-x":[{"rotate-x":J()}],"rotate-y":[{"rotate-y":J()}],"rotate-z":[{"rotate-z":J()}],scale:[{scale:Y()}],"scale-x":[{"scale-x":Y()}],"scale-y":[{"scale-y":Y()}],"scale-z":[{"scale-z":Y()}],"scale-3d":["scale-3d"],skew:[{skew:ee()}],"skew-x":[{"skew-x":ee()}],"skew-y":[{"skew-y":ee()}],transform:[{transform:[ed,eo,"","none","gpu","cpu"]}],"transform-origin":[{origin:w()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:er()}],"translate-x":[{"translate-x":er()}],"translate-y":[{"translate-y":er()}],"translate-z":[{"translate-z":er()}],"translate-none":["translate-none"],accent:[{accent:M()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:M()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",ed,eo]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":P()}],"scroll-mx":[{"scroll-mx":P()}],"scroll-my":[{"scroll-my":P()}],"scroll-ms":[{"scroll-ms":P()}],"scroll-me":[{"scroll-me":P()}],"scroll-mt":[{"scroll-mt":P()}],"scroll-mr":[{"scroll-mr":P()}],"scroll-mb":[{"scroll-mb":P()}],"scroll-ml":[{"scroll-ml":P()}],"scroll-p":[{"scroll-p":P()}],"scroll-px":[{"scroll-px":P()}],"scroll-py":[{"scroll-py":P()}],"scroll-ps":[{"scroll-ps":P()}],"scroll-pe":[{"scroll-pe":P()}],"scroll-pt":[{"scroll-pt":P()}],"scroll-pr":[{"scroll-pr":P()}],"scroll-pb":[{"scroll-pb":P()}],"scroll-pl":[{"scroll-pl":P()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",ed,eo]}],fill:[{fill:["none",...M()]}],"stroke-w":[{stroke:[Z,eu,ei,ea]}],stroke:[{stroke:["none",...M()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}});function e_(...e){return ej(b(e))}let eN=v("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function eA({className:e,variant:r,size:t,asChild:o=!1,...i}){return(0,n.jsx)(o?m:"button",{"data-slot":"button",className:e_(eN({variant:r,size:t,className:e})),...i})}function eO({className:e,type:r,...t}){return(0,n.jsx)("input",{type:r,"data-slot":"input",className:e_("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...t})}function eS({className:e,...r}){return(0,n.jsx)("div",{"data-slot":"card",className:e_("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...r})}function eT({className:e,...r}){return(0,n.jsx)("div",{"data-slot":"card-content",className:e_("px-6",e),...r})}let eC=v("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function eM({className:e,variant:r,asChild:t=!1,...o}){return(0,n.jsx)(t?m:"span",{"data-slot":"badge",className:e_(eC({variant:r}),e),...o})}function eI(){let[e,r]=(0,o.useState)(""),[t,i]=(0,o.useState)([]),[a,s]=(0,o.useState)(!1),[l,c]=(0,o.useState)(!1),d=async r=>{if(r.preventDefault(),e.trim()){s(!0),c(!0);try{let r=await fetch("https://pre-api.dscopilot.ai/selection/querySelectionPage",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({keyWorld:e,pageNum:1,pageSize:20,productPoolLevel:1})}),t=await r.json();t.success&&t.module?.productDTOPageInfo?.dataList?i(t.module.productDTOPageInfo.dataList):i([])}catch(e){console.error("Search error:",e),i([])}finally{s(!1)}}},u=e=>e?`$${e}`:"Price not available",f=e=>{if(!e)return"New";let r=parseInt(e);return r>=1e4?`${Math.floor(r/1e3)}K+`:r>=1e3?`${Math.floor(r/100)/10}K+`:r>=100?`${10*Math.floor(r/10)}+`:r>=10?`${10*Math.floor(r/10)}+`:`${r}+`},m=e=>{if(!e)return"New";let r=parseInt(e);return r>=1e3?`${Math.floor(r/100)/10}K+`:r>=100||r>=10?`${10*Math.floor(r/10)}+`:`${r}+`};return(0,n.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,n.jsx)("div",{className:`transition-all duration-500 ${l?"py-6 border-b":"py-20"}`,children:(0,n.jsxs)("div",{className:"max-w-2xl mx-auto px-4",children:[(0,n.jsxs)("div",{className:`text-center mb-8 transition-all duration-500 ${l?"mb-4":""}`,children:[(0,n.jsx)("h1",{className:`font-bold text-gray-800 transition-all duration-500 ${l?"text-2xl":"text-5xl mb-4"}`,children:"Product Search"}),!l&&(0,n.jsx)("p",{className:"text-gray-600 text-lg",children:"Discover trending products with sales data insights"})]}),(0,n.jsx)("form",{onSubmit:d,className:"relative",children:(0,n.jsxs)("div",{className:"relative flex items-center",children:[(0,n.jsx)(eO,{type:"text",value:e,onChange:e=>r(e.target.value),placeholder:"Search for products...",className:"w-full h-12 text-lg pl-4 pr-20 rounded-full border-2 border-gray-200 focus:border-blue-500 focus:ring-0",disabled:a}),(0,n.jsx)(eA,{type:"submit",disabled:a||!e.trim(),className:"absolute right-1 h-10 px-6 rounded-full bg-blue-600 hover:bg-blue-700 disabled:opacity-50",children:a?(0,n.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}):(0,n.jsx)(p,{className:"w-4 h-4"})})]})})]})}),l&&(0,n.jsx)("div",{className:"max-w-7xl mx-auto px-4 py-8",children:a?(0,n.jsxs)("div",{className:"text-center py-12",children:[(0,n.jsx)("div",{className:"w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,n.jsx)("p",{className:"text-gray-600",children:"Searching for products..."})]}):t.length>0?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"mb-6",children:(0,n.jsxs)("p",{className:"text-gray-600",children:["Found ",t.length,' products for "',e,'"']})}),(0,n.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:t.map(e=>(0,n.jsxs)(eS,{className:"overflow-hidden hover:shadow-lg transition-shadow",children:[(0,n.jsxs)("div",{className:"aspect-square bg-gray-100 relative",children:[e.productMainUrl?(0,n.jsx)("img",{src:e.productMainUrl,alt:e.title,className:"w-full h-full object-cover",onError:e=>{e.target.src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzlmYTZiMiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPk5vIEltYWdlPC90ZXh0Pjwvc3ZnPg=="}}):(0,n.jsx)("div",{className:"w-full h-full flex items-center justify-center text-gray-400",children:"No Image"}),(0,n.jsxs)("div",{className:"absolute top-2 left-2 flex flex-col gap-1",children:[e.exclusivePrice&&(0,n.jsx)(eM,{variant:"destructive",className:"text-xs",children:"Exclusive Price"}),e.starProduct&&(0,n.jsx)(eM,{variant:"secondary",className:"text-xs",children:"⭐ 4+ Star"}),e.qualitySupplier&&(0,n.jsx)(eM,{variant:"outline",className:"text-xs bg-white",children:"Quality Supplier"})]})]}),(0,n.jsxs)(eT,{className:"p-4",children:[(0,n.jsx)("h3",{className:"font-medium text-sm line-clamp-2 mb-2 min-h-[2.5rem]",children:e.title}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)("span",{className:"text-lg font-bold text-green-600",children:u(e.price)}),e.dsDiscount&&e.dsDiscount>0&&(0,n.jsxs)(eM,{variant:"destructive",className:"text-xs",children:["-",Math.round(100*e.dsDiscount),"%"]})]}),(0,n.jsxs)("div",{className:"text-xs text-gray-600 space-y-1",children:[(0,n.jsxs)("div",{className:"flex justify-between",children:[(0,n.jsx)("span",{children:"Total Orders"}),(0,n.jsx)("span",{className:"font-medium text-green-600",children:f(e.orderTotal)})]}),(0,n.jsxs)("div",{className:"flex justify-between",children:[(0,n.jsx)("span",{children:"Last 14 Days Orders"}),(0,n.jsx)("span",{className:"font-medium text-blue-600",children:m(e.fortnightOrder)})]})]}),(0,n.jsxs)("div",{className:"flex items-center gap-1 mt-2",children:[(0,n.jsx)("div",{className:"flex-1 h-1 bg-gray-200 rounded-full overflow-hidden",children:(0,n.jsx)("div",{className:"h-full bg-gradient-to-r from-green-400 to-green-600 rounded-full transition-all duration-300",style:{width:`${Math.min(100,parseInt(e.fortnightOrder||"0")/Math.max(parseInt(e.orderTotal||"1"),1)*1e3)}%`}})}),(0,n.jsx)("span",{className:"text-xs text-gray-500",children:"Trend"})]})]})]})]},e.itemId))})]}):(0,n.jsxs)("div",{className:"text-center py-12",children:[(0,n.jsx)("div",{className:"text-gray-400 mb-4",children:(0,n.jsx)(p,{className:"w-16 h-16 mx-auto mb-4 opacity-50"})}),(0,n.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No products found"}),(0,n.jsx)("p",{className:"text-gray-600",children:"Try searching with different keywords or check your spelling."})]})})]})}},7232:()=>{},8014:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>c,metadata:()=>l});var n=t(7413),o=t(6389),i=t.n(o),a=t(1189),s=t.n(a);t(2704);let l={title:"Create Next App",description:"Generated by create next app"};function c({children:e}){return(0,n.jsx)("html",{lang:"en",children:(0,n.jsx)("body",{className:`${i().variable} ${s().variable} antialiased`,children:e})})}},8034:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"getRouteMatcher",{enumerable:!0,get:function(){return o}});let n=t(4827);function o(e){let{re:r,groups:t}=e;return e=>{let o=r.exec(e);if(!o)return!1;let i=e=>{try{return decodeURIComponent(e)}catch(e){throw Object.defineProperty(new n.DecodeError("failed to decode param"),"__NEXT_ERROR_CODE",{value:"E528",enumerable:!1,configurable:!0})}},a={};for(let[e,r]of Object.entries(t)){let t=o[r.pos];void 0!==t&&(r.repeat?a[e]=t.split("/").map(e=>i(e)):a[e]=i(t))}return a}}},8212:(e,r,t)=>{"use strict";function n(e){return function(){let{cookie:r}=e;if(!r)return{};let{parse:n}=t(6415);return n(Array.isArray(r)?r.join("; "):r)}}Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"getCookieParser",{enumerable:!0,get:function(){return n}})},8304:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{DEFAULT_METADATA_ROUTE_EXTENSIONS:function(){return s},STATIC_METADATA_IMAGES:function(){return a},getExtensionRegexString:function(){return l},isMetadataPage:function(){return u},isMetadataRoute:function(){return p},isMetadataRouteFile:function(){return c},isStaticMetadataRoute:function(){return d}});let n=t(2958),o=t(4722),i=t(554),a={icon:{filename:"icon",extensions:["ico","jpg","jpeg","png","svg"]},apple:{filename:"apple-icon",extensions:["jpg","jpeg","png"]},favicon:{filename:"favicon",extensions:["ico"]},openGraph:{filename:"opengraph-image",extensions:["jpg","jpeg","png","gif"]},twitter:{filename:"twitter-image",extensions:["jpg","jpeg","png","gif"]}},s=["js","jsx","ts","tsx"],l=(e,r)=>r&&0!==r.length?`(?:\\.(${e.join("|")})|(\\.(${r.join("|")})))`:`(\\.(?:${e.join("|")}))`;function c(e,r,t){let o=(t?"":"?")+"$",i=`\\d?${t?"":"(-\\w{6})?"}`,s=[RegExp(`^[\\\\/]robots${l(r.concat("txt"),null)}${o}`),RegExp(`^[\\\\/]manifest${l(r.concat("webmanifest","json"),null)}${o}`),RegExp("^[\\\\/]favicon\\.ico$"),RegExp(`[\\\\/]sitemap${l(["xml"],r)}${o}`),RegExp(`[\\\\/]${a.icon.filename}${i}${l(a.icon.extensions,r)}${o}`),RegExp(`[\\\\/]${a.apple.filename}${i}${l(a.apple.extensions,r)}${o}`),RegExp(`[\\\\/]${a.openGraph.filename}${i}${l(a.openGraph.extensions,r)}${o}`),RegExp(`[\\\\/]${a.twitter.filename}${i}${l(a.twitter.extensions,r)}${o}`)],c=(0,n.normalizePathSep)(e);return s.some(e=>e.test(c))}function d(e){let r=e.replace(/\/route$/,"");return(0,i.isAppRouteRoute)(e)&&c(r,[],!0)&&"/robots.txt"!==r&&"/manifest.webmanifest"!==r&&!r.endsWith("/sitemap.xml")}function u(e){return!(0,i.isAppRouteRoute)(e)&&c(e,[],!1)}function p(e){let r=(0,o.normalizeAppPath)(e).replace(/^\/?app\//,"").replace("/[__metadata_id__]","").replace(/\/route$/,"");return"/"!==r[0]&&(r="/"+r),(0,i.isAppRouteRoute)(e)&&c(r,[],!1)}},8399:(e,r,t)=>{Promise.resolve().then(t.bind(t,7011))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var r=require("../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[447,985],()=>t(5296));module.exports=n})();