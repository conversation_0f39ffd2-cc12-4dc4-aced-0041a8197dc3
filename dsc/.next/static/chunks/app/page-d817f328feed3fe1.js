(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{6601:(e,r,t)=>{Promise.resolve().then(t.bind(t,8303))},8303:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>f});var a=t(5155),s=t(2115),i=t(8200),n=t(4624),l=t(2085),d=t(2596),o=t(9688);function c(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,o.QP)((0,d.$)(r))}let u=(0,l.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function x(e){let{className:r,variant:t,size:s,asChild:i=!1,...l}=e,d=i?n.DX:"button";return(0,a.jsx)(d,{"data-slot":"button",className:c(u({variant:t,size:s,className:r})),...l})}function g(e){let{className:r,type:t,...s}=e;return(0,a.jsx)("input",{type:t,"data-slot":"input",className:c("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",r),...s})}function m(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card",className:c("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",r),...t})}function h(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:c("px-6",r),...t})}let v=(0,l.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function p(e){let{className:r,variant:t,asChild:s=!1,...i}=e,l=s?n.DX:"span";return(0,a.jsx)(l,{"data-slot":"badge",className:c(v({variant:t}),r),...i})}function f(){let[e,r]=(0,s.useState)(""),[t,n]=(0,s.useState)([]),[l,d]=(0,s.useState)(!1),[o,c]=(0,s.useState)(!1),u=async r=>{if(r.preventDefault(),e.trim()){d(!0),c(!0);try{var t,a;let r=await fetch("https://pre-api.dscopilot.ai/selection/querySelectionPage",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({keyWorld:e,pageNum:1,pageSize:20,productPoolLevel:1})}),s=await r.json();s.success&&(null==(a=s.module)||null==(t=a.productDTOPageInfo)?void 0:t.dataList)?n(s.module.productDTOPageInfo.dataList):n([])}catch(e){console.error("Search error:",e),n([])}finally{d(!1)}}},v=e=>e?"$".concat(e):"Price not available",f=e=>{if(!e)return"New";let r=parseInt(e);return r>=1e4?"".concat(Math.floor(r/1e3),"K+"):r>=1e3?"".concat(Math.floor(r/100)/10,"K+"):r>=100?"".concat(10*Math.floor(r/10),"+"):r>=10?"".concat(10*Math.floor(r/10),"+"):"".concat(r,"+")},b=e=>{if(!e)return"New";let r=parseInt(e);return r>=1e3?"".concat(Math.floor(r/100)/10,"K+"):r>=100||r>=10?"".concat(10*Math.floor(r/10),"+"):"".concat(r,"+")};return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)("div",{className:"transition-all duration-500 ".concat(o?"py-6 border-b":"py-20"),children:(0,a.jsxs)("div",{className:"max-w-2xl mx-auto px-4",children:[(0,a.jsxs)("div",{className:"text-center mb-8 transition-all duration-500 ".concat(o?"mb-4":""),children:[(0,a.jsx)("h1",{className:"font-bold text-gray-800 transition-all duration-500 ".concat(o?"text-2xl":"text-5xl mb-4"),children:"Product Search"}),!o&&(0,a.jsx)("p",{className:"text-gray-600 text-lg",children:"Discover trending products with sales data insights"})]}),(0,a.jsx)("form",{onSubmit:u,className:"relative",children:(0,a.jsxs)("div",{className:"relative flex items-center",children:[(0,a.jsx)(g,{type:"text",value:e,onChange:e=>r(e.target.value),placeholder:"Search for products...",className:"w-full h-12 text-lg pl-4 pr-20 rounded-full border-2 border-gray-200 focus:border-blue-500 focus:ring-0",disabled:l}),(0,a.jsx)(x,{type:"submit",disabled:l||!e.trim(),className:"absolute right-1 h-10 px-6 rounded-full bg-blue-600 hover:bg-blue-700 disabled:opacity-50",children:l?(0,a.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}):(0,a.jsx)(i.A,{className:"w-4 h-4"})})]})})]})}),o&&(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 py-8",children:l?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Searching for products..."})]}):t.length>0?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsxs)("p",{className:"text-gray-600",children:["Found ",t.length,' products for "',e,'"']})}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:t.map(e=>(0,a.jsxs)(m,{className:"overflow-hidden hover:shadow-lg transition-shadow",children:[(0,a.jsxs)("div",{className:"aspect-square bg-gray-100 relative",children:[e.productMainUrl?(0,a.jsx)("img",{src:e.productMainUrl,alt:e.title,className:"w-full h-full object-cover",onError:e=>{e.target.src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzlmYTZiMiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPk5vIEltYWdlPC90ZXh0Pjwvc3ZnPg=="}}):(0,a.jsx)("div",{className:"w-full h-full flex items-center justify-center text-gray-400",children:"No Image"}),(0,a.jsxs)("div",{className:"absolute top-2 left-2 flex flex-col gap-1",children:[e.exclusivePrice&&(0,a.jsx)(p,{variant:"destructive",className:"text-xs",children:"Exclusive Price"}),e.starProduct&&(0,a.jsx)(p,{variant:"secondary",className:"text-xs",children:"⭐ 4+ Star"}),e.qualitySupplier&&(0,a.jsx)(p,{variant:"outline",className:"text-xs bg-white",children:"Quality Supplier"})]})]}),(0,a.jsxs)(h,{className:"p-4",children:[(0,a.jsx)("h3",{className:"font-medium text-sm line-clamp-2 mb-2 min-h-[2.5rem]",children:e.title}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-lg font-bold text-green-600",children:v(e.price)}),e.dsDiscount&&e.dsDiscount>0&&(0,a.jsxs)(p,{variant:"destructive",className:"text-xs",children:["-",Math.round(100*e.dsDiscount),"%"]})]}),(0,a.jsxs)("div",{className:"text-xs text-gray-600 space-y-1",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Total Orders"}),(0,a.jsx)("span",{className:"font-medium text-green-600",children:f(e.orderTotal)})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Last 14 Days Orders"}),(0,a.jsx)("span",{className:"font-medium text-blue-600",children:b(e.fortnightOrder)})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1 mt-2",children:[(0,a.jsx)("div",{className:"flex-1 h-1 bg-gray-200 rounded-full overflow-hidden",children:(0,a.jsx)("div",{className:"h-full bg-gradient-to-r from-green-400 to-green-600 rounded-full transition-all duration-300",style:{width:"".concat(Math.min(100,parseInt(e.fortnightOrder||"0")/Math.max(parseInt(e.orderTotal||"1"),1)*1e3),"%")}})}),(0,a.jsx)("span",{className:"text-xs text-gray-500",children:"Trend"})]})]})]})]},e.itemId))})]}):(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"text-gray-400 mb-4",children:(0,a.jsx)(i.A,{className:"w-16 h-16 mx-auto mb-4 opacity-50"})}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No products found"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Try searching with different keywords or check your spelling."})]})})]})}}},e=>{var r=r=>e(e.s=r);e.O(0,[879,441,684,358],()=>r(6601)),_N_E=e.O()}]);