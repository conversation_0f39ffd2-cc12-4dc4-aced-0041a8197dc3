project.name=aidc-t-selection
server.port=7001
management.server.port=7002
# management.endpoints.web.exposure.include=*
spring.profiles.active=testing
# \u7C7B\u76EE\u670D\u52A1
ae.category.appName=know-you-commodity
ae.category.password=F#pce4XJ!9sl0j4s
ae.category.appGroup=group-ae
# AE \u4E3B\u7AD9\u641C\u7D22
ae.search.url=https://tppwork.taobao.com/us44/recommend
#ae.picture.search.url=https://tppwork.taobao.com/sg52/recommend
ae.picture.search.url=https://tppwork.taobao.com/de81/recommend

spring.hsf.group=HSF
spring.hsf.version=1.0.0.daily
# ons\u914D\u7F6E\uFF0C\u8BE6\u89C1 http://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/spring-boot-ons
spring.ons.producer.producer-id=aidc-t-selection
spring.ons.producer.mq-type=METAQ

spring.ons.producer.producer-vpc-ap-sourceeast-id=aidc-t-selection-vpc-ap-sourceeast

spring.ons.consumer.consumer-id=aidc-t-selection
spring.ons.consumer.consume-thread-nums=8
spring.ons.consumer.mq-type=METAQ

redis.instance-id=r-8vbdd5e5184f2454
redis.endpoint=r-8vbdd5e5184f2454.redis.zhangbei.rds.aliyuncs.com

#daily:1.0.0.daily,production:1.0.0, staging:1.0.0.pre
tpp.version=1.0.0

tpp.main.site.search.appid=21006
tpp.ai.selection.search.appid=36806
tpp.ai.selection.search.url=https://tppwork.taobao.com/llm_sg52/recommend
tpp.mainsite.search.url=https://tppwork.taobao.com/de81/recommend
tpp.mainsite2.search.url=https://tppwork.taobao.com/de81/recommend
tpp.aiSelect.search.url=https://tppwork.taobao.com/llm_sg52/aigc
tpp.main.site2.search.appid=37160
tpp.same.style.search.appid=34928
tpp.picture.same.style.search.appid=19587
#banner tpp service
tpp.banner.search.url=https://tppwork.taobao.com/sg52/recommend
tpp.banner.search.appid=37897
#AI business same image search
tpp.same.image.search.appid=37927
tpp.same.image.search.migrate.appid=39373
tpp.same.image.search.diagnosis.appid=39372
tpp.same.selection.search.appid=38221
tpp.search.recommendations.keyword.appid=19652


tpp.vipserver.domain=aigc.tpp.taobao.com.vipserver_pre

spring.servlet.multipart.max-request-size=20MB
spring.servlet.multipart.max-file-size=20MB

tbsession.filter.enabled=true
tbsession.filter.url-patterns = /*
tbsession.filter.name=sessionFilter
tbsession.filter.session-config-group= lazada-buyer-session_AI_GLOBAL
tbsession.filter.filter-intercept-url-excludes=/checkpreload.htm,/status.taobao,/taobao.status,/mock/*,/,/querySiteMapRecommendProduct,/index,/weeklyNewspaper/*,/bucWeeklyNewspaper/*,/feedBackWeeklyNewspaper/*,/app/aefashion/login/*,/mock/**,/shopify/oauth/*,/shopify/webhook/*,\
  /shopify/private/oauth/*,/shopify/private/webhook/*,/test/mainSiteSearch,/test/*,/v3/api-docs,/plugin/*,/.well-known/ai-plugin.json,/ws/*,\
  /image/product,/alternative/search,/alternative/concurrent,/api/v2/*,/debug/task/*,/migration/plugin/products_optimize,\
  /doc.html,/swagger-resources,/v3/api-docs/swagger-config,/swagger-ui.html,/swagger-ui/index.html,/**/*.js,/**/*.css,/**/*.png,/**/*.ico,/**/*.json,/doc.html#/**,\
  /selection/queryAEKeywordList,/selection/productSaleTrend,/api/user/setCookie,/testDiagnosis/test/executeCompare,/testDiagnosis/test/getCompareResult,/testProductMigrate/test/getProductById,/api/biz/report,/testUserGrowth/**

tbsession.authorization.enabled=true
tbsession.authorization.order=2
#tbsession.authorization.include-paths=/api/member/*,/api/store/*,/api/supplier/*
tbsession.authorization.exclude-paths=/checkpreload.htm,/status.taobao,/taobao.status,/mock/*,/,/querySiteMapRecommendProduct,/index,/weeklyNewspaper/*,/bucWeeklyNewspaper/*,/feedBackWeeklyNewspaper/*,/app/aefashion/login/*,/mock/**,/shopify/oauth/*,/shopify/webhook/*,\
  /shopify/private/oauth/*,/shopify/private/webhook/*,/api/email/debug/*,/selection_mock/mainSiteSearch,/api/ic/products,/api/ic/product,/v3/api-docs,/.well-known/ai-plugin.json,/plugin/*,/ws/*,\
  /image/product,/alternative/search,/test/*,/alternative/concurrent,/sync,/api/v2/*,/debug/task/*,/migration/plugin/products_optimize,\
  /doc.html,/swagger-resources,/v3/api-docs/swagger-config,/swagger-ui.html,/swagger-ui/index.html,/**/*.js,/**/*.css,/**/*.png,/**/*.ico,/**/*.json,/doc.html#/**,\
  /selection/queryAEKeywordList,/selection/productSaleTrend,/selection/querySelectionPage,/api/user/setCookie,/desensitizationSelection/productDetail,/desensitizationSelection/querySelectionPage,/desensitizationSelection/querySelectionPageByCategory,/testDiagnosis/test/executeCompare,/testDiagnosis/test/getCompareResult,/testProductMigrate/test/getProductById,/api/biz/report,/testUserGrowth/**
tbsession.authorization.login-url=https://pre-www.dscopilot.ai/app/aefashion/login/login?redirectURL=
tbsession.authorization.interceptor-class=com.aidc.copilot.web.interceptor.TsAuthorizationInterceptor

ic.inventory.version=2.0.0

# AE \u5546\u54C1\u6D88\u606F\u8DE8\u5355\u5143\u914D\u7F6E
AE_COMMODITY_MQ_UNIT_TAG=aliyun-region-vpc-ap-southeast-1-pre
AE_UNIT_TAG=aliyun-region-vpc-ap-southeast-1-pre
US_AE_UNIT_TAG=rg-us-east-pre
DE_AE_UNIT_TAG=aliyun-vpc-de-pre
RU_AE_UNIT_TAG=rg-ru-pre

user.read.version=1.0.0_AI_GLOBAL
ae.user.read.version=1.0.0

#AE OAuth token \u62E6\u622A\u767D\u540D\u5355
ae.token.access.include-paths=/api/order/list

# AE product \u4FE1\u606F\u7F13\u5B58\u65F6\u95F4
ae.product.redis.cache.time=120

# swagger\u5F00\u542F\u589E\u5F3A
knife4j.enable=true
# swagger\u5F00\u542F\u589E\u5F3A\uFF1A\u662F\u5426\u4E3A\u751F\u4EA7\u73AF\u5883
knife4j.production=false

# \u65E5\u5FD7\u914D\u7F6E
logging.config=classpath:logback-spring-local.xml

## \u5F02\u6B65\u7EBF\u7A0B\u6C60\u914D\u7F6E
spring.task.execution.pool.core-size=20
spring.task.execution.pool.max-size=50
# \u4EFB\u52A1\u961F\u5217\u7684\u5BB9\u91CF
spring.task.execution.pool.queue-capacity=50
# \u7EBF\u7A0B\u6C60\u7684\u524D\u7F00\u540D\u79F0
spring.task.execution.thread-name-prefix=ts-publish-to-shopify-task-
# \u975E\u6838\u5FC3\u7EBF\u7A0B\u7684\u5B58\u6D3B\u65F6\u95F4
spring.task.execution.pool.keep-alive=60

ae.item.url.prefix = https://www.aliexpress.com/item/
gpt.plugin.des.dataId = plugin.des.config
gpt.plugin.domain.dataId = plugin.domain.config
gpt.plugin.des.groupId = aidc-t-selection

gpt.plugin.comparison.dataId = plugin.comparison.config
gpt.plugin.comparison.groupId = aidc-t-selection

#server.tomcat.threads.max=200
server.tomcat.connection-timeout=300000
#server.tomcat.socket.buffer-size=8192

#email
# https://aliyuque.antfin.com/sellerapp/bdqo7f/zdpxlp#tPSRT
#?????smtp??
spring.mail.host=smtp.edgeshop.ai
spring.mail.port=465
#??????
spring.mail.username=<EMAIL>
spring.mail.password=shopedgeA1-7
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.smtp.starttls.required=true
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.connectiontimeout=5000
spring.mail.properties.mail.smtp.timeout=5000
spring.mail.properties.mail.smtp.writetimeout=5000
#SSL????
spring.mail.properties.mail.smtps.ssl.trust=*
spring.mail.properties.mail.smtps.ssl.checkserveridentity=true
spring.mail.properties.mail.smtp.socketFactory.port=465
spring.mail.properties.mail.smtp.socketFactory.class=javax.net.ssl.SSLSocketFactory
spring.mail.properties.mail.smtp.socketFactory.fallback=false

tb.global.open.platform.url = https://api.taobao.global
tb.global.open.platform.appKey = 500828
tb.global.open.platform.appSecret = 8GaTrsZGubXWzKwEtFA6brVlWumK5r4e
tb.global.open.platform.token = 50000400a03MIt71111cdfdjEpxOSEDdIvhmuAxPc8cEU4OUtcIIvLzFiqJNe


affiliate.link.appkey = 501382
affiliate.link.appSecret = CjRNgCU7iEOM9853fbEVoK5eKEt9geX4
affiliate.link.url= https://api-sg.aliexpress.com
affiliate.link.apiName = aliexpress.affiliate.link.generate

# lindorm\u5F15\u64CE\u63A5\u5165
lindorm.connection.url= *********************************************************************
lindorm.connection.username=aidcTSelection
lindorm.connection.password=nWcy9CzXmd
lindorm.connection.namespace=aidcTSelection


# AlgorithmImage 1OSS Resource Config
algorithm_image_oss_service_endpoint=oss-ap-southeast-1.aliyuncs.com
algorithm_image_oss_service_bucket_name=t-selection-algorithms-image
algorithm_image_oss_ak=LTAI5tSD2toHzKtoE9qPuaqs
algorithm_image_oss_sk=******************************

data.compliance.endpoint=legal-fortress.alibaba-inc.com


current.env.active=local

COMMISSION_CALLBACK_URL = https://pre-commission.ai.alibaba-inc.com/commission/v1/share/callback

##taihang
taihang.ak=261c818860f4275682a8
taihang.sk=51ff1afa3c8ca0d4181568b20c564763

# AE \u9009\u54C1\u5546\u8BE6\u8868\u6570\u636E\u670D\u52A1
selection.detail.data.table=adi_ae_ai_itm_copilot_selection_d_ex

# AE \u9009\u54C1\u5546\u8BE6\u540C\u6B3E\u5546\u54C1\u8868
selection.same.data.table=adi_ae_ai_itm_copilot_cluster_d_ex

DEFAULT.COMMON.SHOP.DOMAIN=.oneshop.common.shopify.com

spring.buc.login-env=daily
spring.buc.enabled=false


logging.level.org.springframework.web=DEBUG