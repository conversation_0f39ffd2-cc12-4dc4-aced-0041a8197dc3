package com.aidc.copilot.web.adapter;

import com.google.common.collect.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.util.Set;

/**
 * @Author: smy
 * @Date: 2023/5/15 10:20 PM
 */
@Order(1)
@Component
@WebFilter(urlPatterns = "/*", filterName = "corsConfig",asyncSupported = true)
public class CorsFilter implements Filter {
    private static final String ORIGIN = "Origin";
    private static final String OPTIONS = "OPTIONS";

    public static String CURR_SYSTEM_PARAM;
    public static final String PROD_ENV = "prod";

    @Autowired
    public void setCurrSystemParam(@Value("${current.env.active}") String thisSystemParam) {
        CorsFilter.CURR_SYSTEM_PARAM = thisSystemParam;
    }

    private static final Set<String> whiteList = Sets.newHashSet("https://pre-www.dscopilot.ai",
        "https://www.dscopilot.ai", "https://pre-dscopilot-m.taibang.cn", "https://chat.openai.com",
        "https://pre.dscopilot.ai","https://pre.dscopilot.ai:3000", "https://www.amazon.com", "https://dscopilot.edgeshop.ai",
        "https://www.edgeshop.ai", "https://pre-www.edgeshop.ai", "https://pre-app.dscopilot.ai", "https://pre-copilot-edm.alibaba-inc.com",
        "https://app.dscopilot.ai", "https://dsc-aidc-fe.vercel.app", "http://localhost:3000");

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain filterChain)
        throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest)request;
        HttpServletResponse httpResponse = (HttpServletResponse)response;
        String origin = httpRequest.getHeader(ORIGIN);
        String path = httpRequest.getServletPath();

        if (PROD_ENV.equals(CURR_SYSTEM_PARAM)) {
            boolean support = whiteList.contains(origin) ||
                (origin != null && origin.startsWith("chrome-extension"));

            if (!support) {
                filterChain.doFilter(request, response);
                return;
            }
        }

        httpResponse.setCharacterEncoding("UTF-8");
        httpResponse.setContentType("application/json; charset=utf-8");
        httpResponse.setHeader("Access-Control-Allow-Origin", origin);
        httpResponse.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
        httpResponse.setHeader("Access-Control-Max-Age", "3600");

        httpResponse.setHeader("Access-Control-Allow-Headers",
            "Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With, cache-control, Pragma, "
                + "openai-conversation-id, Openai-Ephemeral-User-Id, EagleEye-TraceID, EagleEye-SessionID,"
                + "EagleEye-pAppName,traffic");
        httpResponse.setHeader("Access-Control-Allow-Credentials", "true");

        if (OPTIONS.equalsIgnoreCase(httpRequest.getMethod())) {
            httpResponse.setStatus(HttpServletResponse.SC_OK);
        } else {
            filterChain.doFilter(httpRequest, httpResponse);
        }
    }

    @Override
    public void destroy() {
    }
}
