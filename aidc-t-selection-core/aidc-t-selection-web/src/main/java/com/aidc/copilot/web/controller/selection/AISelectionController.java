package com.aidc.copilot.web.controller.selection;

import com.aidc.copilot.dal.selection.dataobject.RecommendPrompt;
import com.aidc.copilot.dal.selection.mapper.RecommendPromptMapper;
import com.aidc.copilot.framework.commodity.InventoryService;
import com.aidc.copilot.framework.common.PagingBean;
import com.aidc.copilot.framework.common.Result;
import com.aidc.copilot.framework.enums.OrderingRuleEnum;
import com.aidc.copilot.framework.exception.AEOpenPlatformException;
import com.aidc.copilot.framework.exception.SubscriptionFeatureUsageException;
import com.aidc.copilot.framework.exception.TSelectionException;
import com.aidc.copilot.framework.exception.TSelectionExceptionEnum;
import com.aidc.copilot.framework.ic.AeOpenService;
import com.aidc.copilot.framework.ic.IcService;
import com.aidc.copilot.framework.openai.service.OpenAiBusinessService;
import com.aidc.copilot.framework.rate.GlobalRateService;
import com.aidc.copilot.framework.switchconfig.SwitchConfig;
import com.aidc.copilot.framework.tpp.AISelectionTppService;
import com.aidc.copilot.framework.tpp.param.SelectionPageParam;
import com.aidc.copilot.framework.utils.AISelectionFeedbackLogUtils;
import com.aidc.copilot.framework.utils.OrderDataUtils;
import com.aidc.copilot.framework.utils.ResultUtils;
import com.aidc.copilot.service.commodity.dto.LogisticNewTemplateRequestDTO;
import com.aidc.copilot.service.email.EdmAfterSometimeTriggerService;
import com.aidc.copilot.service.email.dto.EdmAfterSometimeTaskRequest;
import com.aidc.copilot.service.enums.SubscriptionEmailTypeEnum;
import com.aidc.copilot.service.product.response.SelectionProductLogisticTemplateResponse;
import com.aidc.copilot.service.selection.AISessionService;
import com.aidc.copilot.service.selection.SelectionDataService;
import com.aidc.copilot.service.selection.SelectionSearchService;
import com.aidc.copilot.service.selection.dto.*;
import com.aidc.copilot.framework.tpp.param.MainSiteSearchParam;
import com.aidc.copilot.service.selection.enums.ProductPoolLevelEnum;
import com.aidc.copilot.service.selection.factory.SelectionProductDetailDTOBuilder;
import com.aidc.copilot.service.selection.factory.SelectionSameProductDetailDTOBuilder;
import com.aidc.copilot.service.selection.impl.SelectionDataDTO;
import com.aidc.copilot.service.selection.utils.RequestLimiterUtil;
import com.aidc.copilot.service.settings.domain.Supplier;
import com.aidc.copilot.service.settings.domain.dto.UserPreferenceDTO;
import com.aidc.copilot.service.settings.service.MemberService;
import com.aidc.copilot.service.settings.service.SupplierService;
import com.aidc.copilot.service.subscription.builder.FeatureUsageFailDTOBuilder;
import com.aidc.copilot.service.subscription.enums.DscFeatureTypeEnum;
import com.aidc.copilot.service.subscription.enums.DscPlanEnum;
import com.aidc.copilot.web.controller.bizreport.BizReportRequest;
import com.aidc.copilot.web.controller.selection.dto.RecommendPromptDTO;
import com.aidc.copilot.web.controller.selection.dto.RecommendPromptProductDTO;
import com.aidc.copilot.web.interceptor.TsContext;
import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.boot.monitor.annotation.Monitor;
import com.alibaba.copilot.boot.monitor.annotation.Monitor.Layer;
import com.alibaba.copilot.boot.monitor.annotation.Monitor.Level;
import com.alibaba.copilot.boot.monitor.annotation.MonitorDenoiser;
import com.alibaba.copilot.enabler.client.subscription.dto.DscSubscriptionInfoDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.UseSubscriptionFeatureDTO;
import com.alibaba.copilot.enabler.client.subscription.enums.FeatureUseType;
import com.alibaba.copilot.enabler.client.subscription.facade.DscSubscriptionHsfApi;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.global.ic.dto.scenario.query.ProductQueryResultDTO;
import com.alibaba.global.ic.dto.scenario.query.SkuQueryResultDTO;
import com.alibaba.global.inventory.api.response.merchant.InvQuerySkuResultDTO;
import com.alibaba.global.money.Money;
import com.alibaba.taihang.model.PageInfo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.swagger.v3.oas.annotations.tags.Tag;
import com.global.iop.util.ApiException;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: smy
 * @Date: 2023/5/8 2:46 PM
 *
 * ai 选品
 */
@RestController
@RequestMapping(value = "/selection")
@Tag(name = "selection")
@Slf4j
public class AISelectionController {

    @Resource
    private AISessionService aiSessionService;
    @Resource
    private SelectionSearchService selectionSearchService;
    @Resource
    private RecommendPromptMapper recommendPromptMapper;
    @Resource
    private MemberService memberService;
    @Resource
    private SupplierService supplierService;

    @Resource
    private AISelectionTppService aiSelectionTppService;
    @Resource
    private SelectionDataService selectionDataService;
    @Resource
    private RequestLimiterUtil requestLimiterUtil;

    @Resource
    private IcService icService;

    @Resource
    private InventoryService inventoryService;

    @Resource
    private AeOpenService aeOpenService;

    @Resource
    private GlobalRateService globalRateService;

    @Resource
    private DscSubscriptionHsfApi dscSubscriptionHsfApi;

    @Resource
    private EdmAfterSometimeTriggerService edmAfterSometimeTriggerService;

    private final static ThreadPoolExecutor freightExecutor = new ThreadPoolExecutor(500, 1000, 50, TimeUnit.SECONDS,
        new LinkedBlockingQueue<>(), new ThreadFactoryBuilder().setNameFormat("ai-selection-freightExecutor").build());

    private final static ThreadPoolExecutor supplierExecutor = new ThreadPoolExecutor(150, 200, 50, TimeUnit.SECONDS,
        new LinkedBlockingQueue<>(), new ThreadFactoryBuilder().setNameFormat("ai-selection-supplierExecutor").build());


    @GetMapping(value = "/get_recommend_prompt")
    @Monitor(name = "获取推荐prompt", level = Monitor.Level.P0, layer = Monitor.Layer.WEB)
    public List<RecommendPromptDTO> getRecommendPrompt() {
        LambdaQueryWrapper<RecommendPrompt> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RecommendPrompt::getIsDeleted, 0);
        List<RecommendPrompt> list = recommendPromptMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        List<RecommendPromptDTO> result = new ArrayList<>();
        list.forEach(recommendPrompt -> {
            RecommendPromptDTO dto = new RecommendPromptDTO();
            BeanUtils.copyProperties(recommendPrompt, dto);
            dto.setUserName(dto.getUserId());
            dto.setRecommendPromptProductDTOList(JSONArray.parseArray(recommendPrompt.getKeysProductJson(),RecommendPromptProductDTO.class));
            result.add(dto);
        });
        return result;
    }

    @PostMapping(value = "/get_recommend_commodity")
    @Monitor(name = "获取首屏商品列表", level = Monitor.Level.P0, layer = Monitor.Layer.WEB)
    public MainSiteSelectionDTO getRecommendCommodity(@RequestBody MainSiteSearchParam searchParam) {
        supple(searchParam);
        MainSiteSelectionDTO selectionDTO = selectionSearchService.mainSiteSearch(searchParam, true);
        return selectionDTO;
    }

    @GetMapping(value = "/get_shipping_checkbox")
    @Monitor(name = "获取首屏物流选项", level = Monitor.Level.P0, layer = Monitor.Layer.WEB)
    public LogisticsMethodDTO getLogisticsMethod(@RequestParam(name = "countryCode") String countryCode) {
        return selectionSearchService.getLogisticsMethod(countryCode);
    }

    @GetMapping(value = "/get_search_checkbox")
    @Monitor(name = "获取首屏筛选项", level = Monitor.Level.P0, layer = Monitor.Layer.WEB)
    public SearchMethodDTO getSearchLabelAndMethod(@RequestParam(name = "countryCode") String countryCode) {
        return selectionSearchService.getSearchLabelAndMethod(countryCode);
    }

    @PostMapping(value = "/refine")
    public AISelectionDTO refine(@RequestParam(required = false) String sessionId,
        @RequestParam(required = false) Integer index,
        @RequestBody MainSiteSearchParam searchParam) throws Exception {
        AISelectionDTO aiSelectionDto =new AISelectionDTO();
        if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isBlank(sessionId)){
            if (org.apache.commons.lang.StringUtils.isBlank(searchParam.getQ())) {
                return aiSelectionDto;
            }
            searchParam.setQ(searchParam.getQ());
        } else {
            AISessionDTO aiSessionDTO = aiSessionService.get(sessionId);
            if (aiSessionDTO == null) {
                return aiSelectionDto;
            }
            searchParam.setQ(aiSessionDTO.getKeywords().get(index).getKeyword());
        }
        MainSiteSelectionDTO selectionDTO = selectionSearchService.mainSiteSearch(searchParam, true);
        aiSelectionDto.setTag(searchParam.getQ());
        aiSelectionDto.setIndex(index);
        aiSelectionDto.setSelectionDTO(selectionDTO);
        List<CommodityDTO> commodityDtoList = selectionDTO.getItemList().getData();
        if (!CollectionUtils.isEmpty(selectionDTO.getItemList().getData())){
            List<String> productIds = commodityDtoList.stream()
                .map(commodityDto -> String.valueOf(commodityDto.getProductId()))
                .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(productIds)){
                SelectionPageParam selectionPageParam = new SelectionPageParam();
                selectionPageParam.setItemIdList(productIds);
                selectionPageParam.setOrderingRule(OrderingRuleEnum.ORDERS.getCode());
                selectionPageParam.setSortOrder("DESC");
                List<SelectionProductDTO> selectionProductDtoList = selectionSearchService.querySelectionPage(selectionPageParam).getDataList();
                aiSelectionDto.setSelectionProductDtoList(selectionProductDtoList);
            }
        }
        return aiSelectionDto;
    }

    @GetMapping(value = "/getAIkeyWordProduct")
    @Monitor(name = "获取AI推荐商品", level = Monitor.Level.P0, layer = Monitor.Layer.WEB)
    public List<AISelectionProductDTO> getAIkeyWordProduct(@RequestParam(name = "keyWord") String keyWord) {
        List<String> search = aiSelectionTppService.search(keyWord);
        if (CollectionUtils.isEmpty(search)||search.get(0).contains("Sorry")){
            return Collections.emptyList();
        }

        List<CompletableFuture<AISelectionProductDTO>> futures = search.stream()
            .map(key -> CompletableFuture.supplyAsync(() -> {
                AISelectionProductDTO aiSelectionProductDto = new AISelectionProductDTO();
                MainSiteSearchParam searchParam = new MainSiteSearchParam();
                supple(searchParam);
                PagingBean pagingBean = new PagingBean();
                pagingBean.setPageIndex(1);
                pagingBean.setPageSize(1);
                searchParam.setPagingBean(pagingBean);
                searchParam.setQ(key);
                MainSiteSelectionDTO mainSiteSelectionDTO = selectionSearchService.mainSiteSearch(searchParam, true);
                aiSelectionProductDto.setAiKeyWord(key);
                aiSelectionProductDto.setMainSiteSelectionDto(mainSiteSelectionDTO);
                return aiSelectionProductDto;
            })).collect(Collectors.toList());

        List<AISelectionProductDTO> aiSelectionProductDtoList = futures.stream()
            .map(CompletableFuture::join)
            .collect(Collectors.toList());

        return aiSelectionProductDtoList;
    }

    @PostMapping(value = "/report")
    @Monitor(name = "首页曝光数据埋点上报", level = Level.P0, layer = Layer.WEB)
    public void report(@RequestBody BizReportRequest request) {
        if (Objects.isNull(request) || com.aidc.copilot.framework.utils.CollectionUtils.isEmpty(request.getInfoList())) {
            return;
        }
        for (BizReportRequest.ReportInfo info : request.getInfoList()) {
            log.info("aiExpInfoLogs:{} {}", TsContext.getMemberId(), com.alibaba.fastjson.JSON.toJSONString(info));
            AISelectionFeedbackLogUtils.aiExpInfoLogs(TsContext.getMemberId(), com.alibaba.fastjson.JSON.toJSONString(info));
        }
    }


    @PostMapping(value = "/detailReport")
    @Monitor(name = "商详数据埋点上报", level = Level.P0, layer = Layer.WEB)
    public void detailReport(@RequestBody BizReportRequest request) {
        if (Objects.isNull(request) || com.aidc.copilot.framework.utils.CollectionUtils.isEmpty(request.getInfoList())) {
            return;
        }
        for (BizReportRequest.ReportInfo info : request.getInfoList()) {
            log.info("aiDetailReportLogs:{} {}", TsContext.getMemberId(), com.alibaba.fastjson.JSON.toJSONString(info));
            AISelectionFeedbackLogUtils.aiDetailReportLogs(TsContext.getMemberId(), com.alibaba.fastjson.JSON.toJSONString(info));
        }
    }

    @GetMapping(value = "/queryAEKeywordList")
    @Monitor(name = "获取搜索推荐词", level = Monitor.Level.P0, layer = Monitor.Layer.WEB)
    public SingleResult<List<String>> queryAEKeywordList(@RequestParam(name = "keyWord") String keyWord,
        @RequestParam(name = "shipToCountry") String shipToCountry) {
        List<String> keywordList = aiSelectionTppService.queryAEKeywordList(keyWord, shipToCountry);
        return SingleResult.buildSuccess(keywordList);
    }

    @PostMapping(value = "/querySelectionPage")
    @Monitor(name = "数据搜索选品分页", level = Level.P0, layer = Layer.WEB)
    public Result<SelectionProductPageDTO> querySelectionPage(@RequestBody SelectionPageParam selectionPageParam)
        throws Exception {
        // 权限校验已移除，允许未登录用户访问
        SelectionProductPageDTO selectionProductPageDto = new SelectionProductPageDTO();
        
        Long userId = null;
        try {
            // 尝试获取用户ID，但不强制要求登录
            userId = TsContext.getMemberId();
        } catch (Exception e) {
            // 如果获取用户ID失败，设置为null，继续执行
            userId = null;
        }
        selectionProductPageDto.setNextPoolLevel(selectionPageParam.getProductPoolLevel());
        selectionPageParam.setNotSecondCategoryList(Collections.singletonList("200001508"));
        PageInfo<SelectionProductDTO> selectionProductDtoPageInfo = selectionSearchService.querySelectionPage(selectionPageParam);
        selectionProductPageDto.setProductDTOPageInfo(selectionProductDtoPageInfo);
        if(selectionProductDtoPageInfo == null){
            selectionProductDtoPageInfo = new PageInfo<>();
            selectionProductDtoPageInfo.setDataList(new ArrayList<>());
        }
        if (selectionProductDtoPageInfo.getDataList() != null && !selectionProductDtoPageInfo.getDataList().isEmpty()) {
            if (selectionProductDtoPageInfo.getDataList().size() < selectionPageParam.getPageSize()
                && org.apache.commons.lang3.StringUtils.isBlank(selectionPageParam.getAiExpressUrl())
                && Objects.nonNull(selectionPageParam.getProductPoolLevel())) {
                Long nextPoolLevel = ProductPoolLevelEnum.getNextPoolLevel(selectionPageParam.getProductPoolLevel());
                selectionProductPageDto.setNextPoolLevel(nextPoolLevel);
                if (Objects.nonNull(selectionPageParam.getBusinessPerspectiveOption())) {
                    selectionProductPageDto.setNextPoolLevel(ProductPoolLevelEnum.TWO_PRODUCT_LEVEL.getNextPoolLevel());
                }
            }
            if (org.apache.commons.lang3.StringUtils.isBlank(selectionPageParam.getKeyWorld())){
                List<SelectionProductDTO> distinctData = new ArrayList<>(selectionProductDtoPageInfo.getDataList().stream()
                    .collect(Collectors.toMap(SelectionProductDTO::getGroupId, Function.identity(), (oldValue, newValue) -> oldValue, LinkedHashMap::new))
                    .values());
                selectionProductDtoPageInfo.setDataList(distinctData);
                selectionProductPageDto.setProductDTOPageInfo(selectionProductDtoPageInfo);
            }
        }
        int size = Optional.ofNullable(selectionProductDtoPageInfo.getDataList()).map(List::size).orElse(0);
        AISelectionFeedbackLogUtils.aiQuerySelectionPageLogs(userId, JSON.toJSONString(selectionPageParam), String.valueOf(size));
        return ResultUtils.createSuccessRes(selectionProductPageDto);
    }

    @RequestMapping(value = "/productDetailInfo", method = RequestMethod.GET)
    @MonitorDenoiser(exceptionType = {"TSelectionException"})
    @Monitor(name = "选品商品详情", level = Monitor.Level.P0, layer = Monitor.Layer.WEB)
    public SingleResult<SelectionProductDetailDTO> productDetailInfo(@RequestParam("productId") Long productId, @RequestParam(value = "shipToCountry", defaultValue = "US") String shipToCountry)
        throws ApiException {
        long memberId = TsContext.getMemberId();
        SelectionProductDetailDTOBuilder builder = new SelectionProductDetailDTOBuilder();
        ProductQueryResultDTO icProduct = icService.getProductById(productId);
        if (icProduct == null) {
            throw new TSelectionException(TSelectionExceptionEnum.CHANNEL_PRODUCT_INFO_NOT_EXITS);
        }
        Long queryDataProductId = productId;
        if (productId.toString().startsWith("3")) {
            Optional<String> mainProductId = icProduct.getFeature("main_product_id");
            queryDataProductId = mainProductId.map(Long::valueOf).orElse(productId);
        }
        SelectionDataDTO selectionProductDetailDTO = selectionDataService.queryDataByItemId(queryDataProductId);
        if (selectionProductDetailDTO == null) {
            return SingleResult.buildSuccess(null);
        }
        builder.setSelectionDataDTO(selectionProductDetailDTO);
        builder.setIcProduct(icProduct);
        SelectionProductDetailDTO productDetailDTO = builder.build();

        // 查询商品库存
        Long invTotal = 0L;
        List<InvQuerySkuResultDTO> invQuerySkuList = inventoryService.batchQueryInventory(icProduct);
        if (!CollectionUtils.isEmpty(invQuerySkuList)) {
            for (InvQuerySkuResultDTO inv : invQuerySkuList) {
                invTotal += inv.getSellableQuantity();
            }
            productDetailDTO.setStock(OrderDataUtils.desensitizeOrderTotal(invTotal));
        }

        // 计算运费
        String shipFromCountryCode = null;
        String shipfromCntyIds = selectionProductDetailDTO.getShipfromCntyIds();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(shipfromCntyIds)) {
            shipFromCountryCode = shipfromCntyIds;
        }
        List<Long> skuIds = icProduct.getSkuList().stream().map(SkuQueryResultDTO::getSkuId).collect(Collectors.toList());
        List<SelectionProductLogisticTemplateResponse> freightResults = new CopyOnWriteArrayList<>();
        if (!CollectionUtils.isEmpty(skuIds)) {
            List<AEOpenPlatformException> exceptions = new CopyOnWriteArrayList<>();

            // 分批并发计算物流运费
            final String shipFrom = shipFromCountryCode;
            List<List<Long>> partition = com.google.common.collect.Lists.partition(skuIds, 200);
            for (List<Long> list : partition) {
                List<CompletableFuture<Void>> futures = list.stream().map(skuId -> CompletableFuture.runAsync(() -> {
                        String param = com.alibaba.fastjson.JSON.toJSONString(LogisticNewTemplateRequestDTO.builder()
                            .productId(productId)
                            .productNum(1)
                            .sendGoodsCountryCode(shipFrom)
                            .countryCode(shipToCountry)
                            .skuId(skuId)
                            .build());
                        freightResults.addAll(getFreightResult(memberId, param, exceptions));
                    }, freightExecutor))
                    .collect(Collectors.toList());
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            }

            if (!CollectionUtils.isEmpty(exceptions)) {
                throw exceptions.get(0);
            }
            if (!CollectionUtils.isEmpty(freightResults)) {
                // 排序取运费最便宜的
                freightResults.sort(Comparator.comparing(SelectionProductLogisticTemplateResponse::getCent));
                productDetailDTO.setFee(freightResults.get(0).getShowCost());
                if(freightResults.get(0).getShowCost().equals("$0.00")){
                    productDetailDTO.setFee("Free");
                }
            } else {
                productDetailDTO.setFee("Free");
            }
        }
        AISelectionFeedbackLogUtils.aiProductDetailInfoLogs(TsContext.getMemberId(), String.valueOf(productId), shipToCountry);
        return SingleResult.buildSuccess(productDetailDTO);
    }

    /**
     * 计算物流
     *
     * @param merchantId
     * @param param
     */
    private List<SelectionProductLogisticTemplateResponse> getFreightResult(Long merchantId, String param, List<AEOpenPlatformException> exceptions) {
        if (merchantId == null) {
            throw new TSelectionException(TSelectionExceptionEnum.LOGIN_STATUS_FAIL);
        }
        // 构建参数
        List<SelectionProductLogisticTemplateResponse> resultList = new ArrayList<>();
        try {
            String memberAccessToken = null;
            //判断当前用户是否ae token过期，若过期使用公共账号 <EMAIL>      liangmeng03
            if (memberService.isMemberAccessToken(merchantId)) {
                memberAccessToken = memberService.getMemberAccessToken(merchantId);
            } else {
                memberAccessToken = memberService.getMemberAccessToken(2100005124002L);
            }
            if (org.apache.commons.lang3.StringUtils.isBlank(memberAccessToken)){
                return resultList;
            }
            String result = aeOpenService.getFreight(param, memberAccessToken);
            if (org.apache.commons.lang3.StringUtils.isBlank(result)) {
                return resultList;
            }
            com.alibaba.fastjson2.JSONObject jsonObject = JSON.parseObject(result).getJSONObject("result");
            if (jsonObject == null) {
                return resultList;
            }
            if (!jsonObject.getBooleanValue("success")) {
                log.error("getFreight error, result: {}", result);
                return resultList;
            }

            JSONArray jsonArray = jsonObject.getJSONArray("aeop_freight_calculate_result_for_buyer_dtolist");
            if (!CollectionUtils.isEmpty(jsonArray)) {
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject logisticTemplate = jsonArray.getJSONObject(i);
                    // 分 - 单位的价格
                    Long cent = logisticTemplate.getJSONObject("freight").getLong("cent");
                    String currencyCode = logisticTemplate.getJSONObject("freight").getString("currency_code");
                    String serviceName = String.valueOf(logisticTemplate.get("service_name"));
                    Boolean trackingAvailable = logisticTemplate.getBooleanValue("tracking_available");

                    String amount = "";
                    if (!"USD".equals(currencyCode)) {
                        Result<Money> usdCent = globalRateService.getPrice(currencyCode, "USD", new BigDecimal(cent));
                        // 确保返回结果及金额不为null
                        if (usdCent != null && usdCent.getData() != null && usdCent.getData().getAmount() != null) {
                            // 如果所有值都不为null，则执行除法运算
                            amount = usdCent.getData().getAmount().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP).toString();
                        } else {
                            // 如果返回的结果为null或金额为null，则跳过当前循环迭代
                            continue;
                        }
                    } else {
                        // 如果货币代码为"USD"，则不需要转换
                        amount = new BigDecimal(cent).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP).toString();
                    }
                    resultList.add(SelectionProductLogisticTemplateResponse.builder()
                        .estimatedDeliveryTime(logisticTemplate.getString("service_name"))
                        .shippingMethod(logisticTemplate.getString("estimated_delivery_time"))
                        .cent(cent)
                        .showCost(new BigDecimal("0").equals(new BigDecimal(amount)) ? "Free" : "$" + amount)
                        .currency(currencyCode)
                        .serviceName(serviceName)
                        .trackingAvailable(trackingAvailable)
                        .currencyCode("USD").build());
                }
            }
        } catch (ApiException e) {
            log.error("AISelectionController#getFreightResult error: {}", e.getMessage(), e);
            //如果运费调用失败，返回为空即可
            return resultList;
        } catch (AEOpenPlatformException e) {
            log.warn(TSelectionExceptionEnum.OPEN_PALTFOEM_TOKEN_EXPIRE_ERROR.getErrorMessage());
            exceptions.add(e);
        }
        // 结果排序
        return resultList;
    }

    @RequestMapping(value = "/sameProductsInfo", method = RequestMethod.GET)
    @Monitor(name = "同簇商品信息", level = Monitor.Level.P0, layer = Monitor.Layer.WEB)
    @MonitorDenoiser(errCode = {"12000"})
    public SingleResult<SelectionSameProductInfoDTO> sameProductsInfo(@RequestParam("groupId") Long groupId,
                                                                      @RequestParam("shipToCountry") String shipToCountry) {
        SelectionSameProductInfoDTO buildResult = new SelectionSameProductInfoDTO();
        Long featureId = null;
        Long userId = TsContext.getMemberId();
        try {
            // 扣权益
            UseSubscriptionFeatureDTO featureLimitDTO = new UseSubscriptionFeatureDTO();
            featureLimitDTO.setUserId(userId);
            featureLimitDTO.setAppCode("DS_COPILOT");
            featureLimitDTO.setFeatureType(DscFeatureTypeEnum.PRODUCT_INSIGHTDETAILS.name());
            featureLimitDTO.setFeatureName(DscFeatureTypeEnum.PRODUCT_INSIGHTDETAILS.getServiceDescription());
            featureLimitDTO.setUseType(FeatureUseType.USE);
            SingleResult<DscSubscriptionInfoDTO> feature = dscSubscriptionHsfApi.useSubscriptionFeature(featureLimitDTO);
            if (feature.isSuccess() && feature.getData() != null) {
                DscSubscriptionInfoDTO dscSubscriptionInfoDTO = new DscSubscriptionInfoDTO();
                if (org.apache.commons.lang3.StringUtils.isBlank(feature.getData().getFeatureName())) {
                    dscSubscriptionInfoDTO.setFeatureName(DscFeatureTypeEnum.PRODUCT_INSIGHTDETAILS.getServiceDescription());
                } else {
                    dscSubscriptionInfoDTO = feature.getData();
                    featureId = dscSubscriptionInfoDTO.getFeatureId();
                }

                FeatureUsageFailDTOBuilder builder = new FeatureUsageFailDTOBuilder();
                builder.setPlanName(DscPlanEnum.getShowPlanNameByPlanName(dscSubscriptionInfoDTO.getPlanName()));
                builder.setFeatureName(dscSubscriptionInfoDTO.getFeatureName());
                builder.setFeatureId(dscSubscriptionInfoDTO.getFeatureId());
                throw new SubscriptionFeatureUsageException(TSelectionExceptionEnum.SUBSCRIPTION_USAGE_FAIL, builder.build());
            }

            SelectionSameProductDetailDTOBuilder builder = new SelectionSameProductDetailDTOBuilder();
            SelectionSameProductDTO selectionSameProductDTO = selectionDataService.querySameProductsByGroupId(groupId);
            if (selectionSameProductDTO == null) {
                return SingleResult.buildSuccess(null);
            }
            builder.setSelectionSameProductDTO(selectionSameProductDTO);
            buildResult = builder.build();
        } catch (Exception e) {
            if (e instanceof SubscriptionFeatureUsageException) {
                throw e;
            }
            if (featureId != null) {
                dscSubscriptionHsfApi.returnSubscriptionFeature(userId, featureId, DscFeatureTypeEnum.PRODUCT_INSIGHTDETAILS.name());
            }
            throw new RuntimeException(e);
        }

        return SingleResult.buildSuccess(buildResult);
    }



    /**
     * 推荐供应商服务
     * @param recommendedSuppliersDto
     * @return
     */
    @RequestMapping(value = "/getRecommendedSuppliers", method = RequestMethod.POST)
    @Monitor(name = "推荐供应商服务", level = Monitor.Level.P0, layer = Monitor.Layer.WEB)
    public List<SelectionSameProductInfoDTO.RecommendedSupplier> getRecommendedSuppliers(@RequestBody RecommendedSuppliersDTO recommendedSuppliersDto) {
        long memberId = TsContext.getMemberId();
        List<SelectionSameProductInfoDTO.RecommendedSupplier> recommendedSuppliers = new CopyOnWriteArrayList<>();
        List<CompletableFuture<Void>> futureList = recommendedSuppliersDto.getOriginalSuppliers().stream().map(recommendedSupplier ->
            CompletableFuture.runAsync(() -> {
                SelectionSameProductInfoDTO.RecommendedSupplier supplier = new SelectionSameProductInfoDTO.RecommendedSupplier();
                if (recommendedSupplier == null) {
                    return;
                }
                BeanUtils.copyProperties(recommendedSupplier, supplier);
                // 设置同款库存
                ProductQueryResultDTO icProduct = icService.getProductById(recommendedSupplier.getItemId());
                if (icProduct == null) {
                    return;
                }
                Long invTotal = 0L;
                List<InvQuerySkuResultDTO> invQuerySkuList = inventoryService.batchQueryInventory(icProduct);
                if (!CollectionUtils.isEmpty(invQuerySkuList)) {
                    for (InvQuerySkuResultDTO inv : invQuerySkuList) {
                        invTotal += inv.getSellableQuantity();
                    }
                    supplier.setInStock(OrderDataUtils.desensitizeOrderTotal(invTotal));
                }

                // 设置同款商品运费
                List<Long> skuIds = icProduct.getSkuList().stream().map(SkuQueryResultDTO::getSkuId).collect(Collectors.toList());
                List<SelectionProductLogisticTemplateResponse> freightResults = new CopyOnWriteArrayList<>();
                if (!CollectionUtils.isEmpty(skuIds)) {
                    List<AEOpenPlatformException> exceptions = new CopyOnWriteArrayList<>();

                    List<List<Long>> partition = com.google.common.collect.Lists.partition(skuIds, 200);
                    for (List<Long> list : partition) {
                        List<CompletableFuture<Void>> futures = list.stream().map(skuId -> CompletableFuture.runAsync(() -> {
                            String param = com.alibaba.fastjson.JSON.toJSONString(LogisticNewTemplateRequestDTO.builder()
                                .productId(recommendedSupplier.getItemId())
                                .productNum(1)
                                .countryCode(recommendedSuppliersDto.getShipToCountry())
                                .skuId(skuId)
                                .build());
                            freightResults.addAll(getFreightResult(memberId, param, exceptions));
                        }, freightExecutor)).collect(Collectors.toList());
                        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
                    }

                    if (!CollectionUtils.isEmpty(exceptions)) {
                        throw exceptions.get(0);
                    }
                    // 排序取运费最便宜的
                    if (!CollectionUtils.isEmpty(freightResults)) {
                        freightResults.sort(Comparator.comparing(SelectionProductLogisticTemplateResponse::getCent));
                        supplier.setShowShippingFee(freightResults.get(0).getShowCost());
                    } else {
                        supplier.setShowShippingFee("Free");
                    }
                }
                recommendedSuppliers.add(supplier);
            }, supplierExecutor)
        ).collect(Collectors.toList());
        CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();

        return recommendedSuppliers.stream()
            .sorted(Comparator.comparing(SelectionSameProductInfoDTO.RecommendedSupplier::getTimesOfBeingAdoptedValue)
                .thenComparing(SelectionSameProductInfoDTO.RecommendedSupplier::getStabilityValue).reversed())
            .limit(5)
            .collect(Collectors.toList());
    }

    @GetMapping(value = "/relatedProductRecommendation")
    @Monitor(name = "关联商品推荐", level = Monitor.Level.P0, layer = Monitor.Layer.WEB)
    public SingleResult<List<SelectionProductDTO>> relatedProductRecommendation(@RequestParam(required = false)  String itemId)
        throws Exception {
        return SingleResult.buildSuccess(selectionSearchService.relatedProductRecommendation(TsContext.getShopDomain(),itemId));
    }


    private void supple(MainSiteSearchParam searchParam) {
        UserPreferenceDTO userPreferenceDTO = memberService.queryUserPreference(TsContext.getMemberId());
        log.info(String.format("userPreference %s, country: %s", userPreferenceDTO,
            userPreferenceDTO.getUserPreferenceCountry()));
        if (userPreferenceDTO == null) {
            return;
        }
        if (StringUtils.hasText(userPreferenceDTO.getUserPreferenceCurrency())) {
            searchParam.set_currency(userPreferenceDTO.getUserPreferenceCurrency());
        }
        if (StringUtils.hasText(userPreferenceDTO.getUserPreferenceLanguage())) {
            searchParam.setLang(userPreferenceDTO.getUserPreferenceLanguage());
        }
        if (StringUtils.hasText(userPreferenceDTO.getUserPreferenceCountry())) {
            searchParam.setShpt_co(userPreferenceDTO.getUserPreferenceCountry());
        }
        Supplier supplier = supplierService.getSupplier(TsContext.getMemberId());
        if (supplier == null) {
            return;
        }
        searchParam.setUserMemberSeq(supplier.getSupplierId());
    }
}
