package com.aidc.copilot.framework.diamond;

import com.taobao.diamond.client.Diamond;
import com.taobao.diamond.manager.ManagerListener;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.util.Assert;

import java.util.concurrent.Executor;

/**
 * <AUTHOR>
 * @description 抽象 Diamond 监听, 用于业务配置
 * @email <EMAIL>
 * @date 2023/4/28
 */
@Data
@Slf4j
public abstract class AbstractConfigCenter<T> implements InitializingBean, ManagerListener {
    protected final String clazzSimpleName = getClass().getSimpleName();

    private static final String GROUP_ID = "aidc-t-selection";
    private static final Long TIME_OUT = 10000L;
    protected T data;

    /**
     * 获取配置数据
     *
     * @return
     */
    public T getData() {
        return data;
    }

    /**
     * 返回 null，使用默认的 executor
     *
     * @return
     */
    @Override
    public Executor getExecutor() {
        return null;
    }

    /**
     * 获取 Diamond dataId
     *
     * @return Diamond dataId
     */
    protected abstract String getDataId();

    /**
     * 获取 Diamond groupId
     *
     * @return Diamond 默认 groupId
     */
    protected String getGroupId() {
        return GROUP_ID;
    }

    /**
     * 获取 Diamond 超时时间
     *
     * @return Diamond 超时时间
     */
    protected long getTimeout() {
        return TIME_OUT;
    }

    /**
     * 将输入转为数据
     *
     * @param dataStr
     */
    protected abstract void compile(String dataStr);

    /**
     * 获取配置后启动监听
     *
     * @throws RuntimeException
     */
    @Override
    public void afterPropertiesSet() throws RuntimeException {
        try {
            Assert.notNull(getDataId(), "diamond dataId cannot be null...");
            String configInfo = Diamond.getConfig(getDataId(), GROUP_ID, TIME_OUT);
//            log.info("{}#afterPropertiesSet init configInfo. configInfo={}", clazzSimpleName, configInfo);
            compile(configInfo);
            Diamond.addListener(getDataId(), GROUP_ID, this);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void receiveConfigInfo(String configInfo) {
        compile(configInfo);
    }
}
