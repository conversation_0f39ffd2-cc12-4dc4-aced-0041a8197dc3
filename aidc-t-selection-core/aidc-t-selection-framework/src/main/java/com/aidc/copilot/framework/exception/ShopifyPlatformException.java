package com.aidc.copilot.framework.exception;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/11/29
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ShopifyPlatformException extends BaseException {
    private String data;

    public ShopifyPlatformException(String errorCode) {
        super(errorCode);
    }

    public ShopifyPlatformException(String errorCode, String message) {
        super(errorCode, message);
    }

    public ShopifyPlatformException(String errorCode, String message, Throwable throwable) {
        super(errorCode, message, throwable);
    }

    public ShopifyPlatformException(TSelectionExceptionEnum exceptionEnum) {
        super(exceptionEnum.getErrorCode(), exceptionEnum.getErrorMessage());
    }

    public ShopifyPlatformException(TSelectionExceptionEnum exceptionEnum, String data) {
        super(exceptionEnum.getErrorCode(), exceptionEnum.getErrorMessage());
        this.data = data;
    }
}
