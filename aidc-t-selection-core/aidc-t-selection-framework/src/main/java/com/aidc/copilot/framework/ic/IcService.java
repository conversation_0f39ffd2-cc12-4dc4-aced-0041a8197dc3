package com.aidc.copilot.framework.ic;

import com.aidc.copilot.framework.ic.dto.IcProductDTO;
import com.alibaba.global.ic.dto.scenario.query.ProductQueryResultDTO;
import com.alibaba.global.ic.dto.scenario.query.SkuQueryResultDTO;

import java.util.List;
import java.util.Locale;

/**
 * global-ic 商品服务
 *
 * @Author: mianyun.yt
 * @Date: 2023/4/28
 */
public interface IcService {

    /**
     * 根据 id 查询商品详情
     *
     * @param productId 商品id
     * @return 商品信息
     */
    ProductQueryResultDTO getProductById(Long productId);

    /**
     * 批量根据 id 查询商品详情。单次最多查询 20 条数据
     */
    List<ProductQueryResultDTO> getProductListByIds(List<Long> productIds);


    /**
     * 根据 id 查询商品详情(带redis缓存)
     *
     * @param productId 商品id
     * @return 商品信息
     */
    IcProductDTO getProductByIdWithCache(Long productId, Locale locale);


    /**
     * 批量根据 id 查询商品详情。单次最多查询 20 条数据
     */
    List<ProductQueryResultDTO> listProductByIds(List<Long> productIds);

    /**
     * 批量根据 id 查询商品详情（带缓存+多线程）
     */
    List<IcProductDTO> listProductByIdsWithCache(List<Long> productIds, Locale locale);

    /**
     * 根据 id 查询sku列表
     *
     * @param productId 商品id
     * @return sku 列表
     */
    List<SkuQueryResultDTO> getSkuList(Long productId);

}
