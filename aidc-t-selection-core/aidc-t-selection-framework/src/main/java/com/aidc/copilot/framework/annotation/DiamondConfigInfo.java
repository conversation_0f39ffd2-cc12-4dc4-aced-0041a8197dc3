package com.aidc.copilot.framework.annotation;

import com.aidc.copilot.framework.diamond.JsonConfigCenter;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Diamond配置注解, 参: {@link JsonConfigCenter}
 *
 * <AUTHOR>
 * @version 2023/6/14
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface DiamondConfigInfo {

    String DEFAULT_GROUP_ID = "aidc-t-selection";

    /**
     * @return 对应Diamond的dataId配置项
     */
    String dataId();

    /**
     * @return 对应Diamond的groupId配置项
     */
    String groupId() default DEFAULT_GROUP_ID;
}
