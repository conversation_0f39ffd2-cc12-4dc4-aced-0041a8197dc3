package com.aidc.copilot.framework.asynctask.worker;

import com.aidc.copilot.framework.asynctask.model.AsyncTask;
import com.alibaba.copilot.boot.basic.result.SingleResult;

/**
 * @desc: 异步任务 worker
 * @author: yixiao.cx
 * @create: 2023-06-26
 **/
public interface AsyncTaskWorker {
    /**
     * 任务类型
     * @return
     */
    String type();

    /**
     * 任务并行数量
     * @return
     */
    int capacity();

    /**
     * 执行任务
     * @param task
     * @return result
     */
    SingleResult<Object> execute(AsyncTask task);
}
