package com.aidc.copilot.framework.openai.config;

import com.alibaba.common.lang.StringUtil;

import com.aidc.copilot.framework.diamond.AbstractConfigCenter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @ClassName TemplateSearchKeyWordConfig
 * <AUTHOR>
 * @Date 2023/7/5 17:19
 */
@Component
@Slf4j
public class TemplateSearchKeyWordConfig extends AbstractConfigCenter<String> {

    private final String defaultTemplate = "As a Shopify title optimization specialist, your job is to distill the "
        + "most relevant or SEO-friendly keyword from a given product title. This keyword should accurately represent"
        + " the key attributes of the product and ideally match terms that potential customers are likely to use in "
        + "their searches. Note that you should extract a single keyword, which can be composed of 2 to 3 words max.\n"
        + "\n"
        + "Let's understand this with an example:\n"
        + "Title: 2023's New Arrivals: Men's Summer Casual Shoes\n"
        + "Extracted Keywords: Men's Casual Shoes\n"
        + "\n"
        + "Now, apply the same approach to the following title:\n"
        + "Title: ${Title}\n"
        + "Extracted Keywords:";

    @Override
    protected String getDataId() {
        return "com.aidc.copilot.searchKeyWord.template";
    }

    @Override
    protected void compile(String dataStr) {
        data = dataStr;
    }

    @Override
    public String getData() {
        if (StringUtil.isBlank(super.getData())) {
            return defaultTemplate;
        }
        return super.getData();
    }
}
