package com.aidc.copilot.framework.product.converter;


import com.aidc.copilot.dal.product.dataobject.ChannelSkuDO;
import com.aidc.copilot.framework.product.model.channel.ChannelSku;
import com.aidc.copilot.framework.product.model.channel.ChannelSkuAttributes;
import com.alibaba.copilot.boot.basic.factory.Converter;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

/**
 * @desc:
 * @author: yixiao.cx
 * @create: 2023-07-07
 **/
@Component
public class ChannelSkuConverter implements Converter<ChannelSkuDO, ChannelSku> {
    public static final Converter<ChannelSkuDO, ChannelSku> INSTANCE = new ChannelSkuConverter();

    private static final String[] ignoreProperties = new String[] {"attributes"};

    @Override
    public ChannelSku convertA2B(ChannelSkuDO skuDO) {
        if (skuDO == null) {
            return null;
        }
        ChannelSku sku = new ChannelSku();
        BeanUtils.copyProperties(skuDO, sku, ignoreProperties);
        sku.setAttributes(new ChannelSkuAttributes(skuDO.getAttributes()));
        return sku;
    }

    @Override
    public ChannelSkuDO convertB2A(ChannelSku sku) {
        if (sku == null) {
            return null;
        }
        ChannelSkuDO skuDO = new ChannelSkuDO();
        BeanUtils.copyProperties(sku, skuDO, ignoreProperties);
        skuDO.setAttributes(sku.getAttributes().toString());
        return skuDO;
    }
}
