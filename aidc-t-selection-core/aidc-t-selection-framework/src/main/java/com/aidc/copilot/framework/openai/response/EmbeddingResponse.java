package com.aidc.copilot.framework.openai.response;

import java.io.Serializable;
import java.util.List;

import com.aidc.copilot.framework.openai.model.Item;
import com.aidc.copilot.framework.openai.model.Usage;
import lombok.Data;

/**
 * 描述：
 *
 * <AUTHOR>
 *  2023-02-15
 */
@Data
public class EmbeddingResponse implements Serializable {

    private String object;
    private List<Item> data;
    private String model;
    private Usage usage;
}
