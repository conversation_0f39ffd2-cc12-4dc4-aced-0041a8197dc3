package com.aidc.copilot.framework.ic.impl;

import com.aidc.copilot.framework.ic.TaobaoOpenService;
import com.global.iop.api.IopClient;
import com.global.iop.api.IopClientImpl;
import com.global.iop.api.IopRequest;
import com.global.iop.api.IopResponse;
import com.global.iop.util.UrlConstants;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

/**
 * @Author: mianyun.yt
 * @Date: 2023/6/6
 */
@Service
public class TaobaoOpenServiceImpl implements TaobaoOpenService {

    @Value("${ae.open.key}")
    private String appKey;

    @Value("${ae.open.secret}")
    private String appSecret;

    @Value("${ae.open.url}")
    private String url;

    private IopClient client;

    @PostConstruct
    public void init() {
        // TODO ae 的 key 天淘海外不能用
        client = new IopClientImpl("https://api.taobao.global/rest", appKey, appSecret);
    }

    @Override
    public Object querySimilarProduct(String imageUrl, String token) {
        IopRequest request = new IopRequest();
        request.setApiName("/product/similar/get");
        request.addApiParameter("item_id", "600091498136190992");
        request.addApiParameter("url", "https://img.alicdn.com/bao/uploaded/i3/684174095/O1CN019WLF1n1g7YYWtonEu_!!0-item_pic.jpg");
        IopResponse response = null;
        try {
            response = client.execute(request, token);
            System.out.println(response.getGopResponseBody());
        } catch (Exception e) {
            e.printStackTrace();
        }

        return response;
    }
}
