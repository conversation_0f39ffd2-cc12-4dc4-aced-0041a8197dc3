//package com.aidc.copilot.framework.datasource;
//
//import com.baomidou.mybatisplus.core.MybatisConfiguration;
//import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
//import com.zaxxer.hikari.HikariConfig;
//import com.zaxxer.hikari.HikariDataSource;
//import lombok.Data;
//import org.apache.ibatis.session.SqlSessionFactory;
//import org.mybatis.spring.annotation.MapperScan;
//import org.springframework.beans.factory.annotation.Qualifier;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
//import org.springframework.jdbc.datasource.DataSourceTransactionManager;
//import org.springframework.jdbc.datasource.DriverManagerDataSource;
//
//import javax.sql.DataSource;
//import java.util.Properties;
//
//@Configuration
//@MapperScan(value = {"com.aidc.copilot.dal.lindorm.*.mapper"},
//        sqlSessionFactoryRef = "lindormSqlSessionFactory")
//@ConfigurationProperties(prefix = "lindorm.connection")
//@Data
//public class LindormDataSourceConfig {
//
//    private String url;
//    private String username;
//    private String password;
//    private String namespace;
//
//    @Bean(name = "lindormDataSource")
//    public DataSource dataSource() {
//        Properties properties = new Properties();
//        properties.put("user", username);
//        properties.put("password", password);
//        properties.put("database", namespace);
//        HikariConfig config = new HikariConfig();
//        config.setDriverClassName("com.alibaba.lindorm.lql.rpc.avatica.jdbc.LqlDriver");
//        config.setJdbcUrl(url);
//        config.setMinimumIdle(10);
//        config.setMaximumPoolSize(20);
//        config.setConnectionTestQuery("select * from heart_beat limit 1");
//        config.setDataSourceProperties(properties);
//        HikariDataSource dataSource = new HikariDataSource(config);
//        return dataSource;
//    }
//
//    @Bean(name = "lindormSqlSessionFactory")
//    public SqlSessionFactory sqlSessionFactory(@Qualifier("lindormDataSource") DataSource dataSource) throws Exception {
//        MybatisSqlSessionFactoryBean sqlSessionFactoryBean = new MybatisSqlSessionFactoryBean();
//        sqlSessionFactoryBean.setDataSource(dataSource);
//        MybatisConfiguration mybatisConfiguration = new MybatisConfiguration();
//        mybatisConfiguration.setMapUnderscoreToCamelCase(true);
//        sqlSessionFactoryBean.setConfiguration(mybatisConfiguration);
//        sqlSessionFactoryBean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/lindorm/*Mapper.xml"));
//        return sqlSessionFactoryBean.getObject();
//    }
//
//    @Bean(name = "lindormTransactionManager")
//    public DataSourceTransactionManager transactionManager() {
//        return new DataSourceTransactionManager(dataSource());
//    }
//}
