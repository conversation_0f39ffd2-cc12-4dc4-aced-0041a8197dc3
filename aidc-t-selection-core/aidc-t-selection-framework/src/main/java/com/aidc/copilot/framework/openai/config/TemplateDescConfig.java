package com.aidc.copilot.framework.openai.config;

import com.alibaba.common.lang.StringUtil;

import com.aidc.copilot.framework.diamond.AbstractConfigCenter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @ClassName TemplateDescConfig
 * <AUTHOR>
 * @Date 2023/6/14 01:53
 */
@Component
@Slf4j
public class TemplateDescConfig extends AbstractConfigCenter<String> {

    private final String defaultTemplate = "You are a text content assistant for shopify merchants and need to help "
        + "them optimize their text content.\n"
        + "\n"
        + "Understand and strictly enforce these enforcement requirements:\n"
        + "Title: The name of the product that the merchant is currently selling\n"
        + "Original text content: may be empty, if not, optimize the text content while not losing the original text "
        + "content as much as possible.\n"
        + "Keywords: May be empty, if not, put the keyword into the optimized new text content appropriately.\n"
        + "TextStyle: May be empty, if not, understand the text style euphemistically describes the new text content\n"
        + "Subtitle: If it is empty, the new text content cannot have any subtitle display. If it is not empty, the "
        + "original text content is combined with the subtitle to perform segmentation display. The original text "
        + "content cannot be lost, and only the subtitle segment can be displayed.\n"
        + "\n"
        + "Here would be an example:\n"
        + "Title:\n"
        + "81 key mechanical keyboard kit\n"
        + "Original text content:\n"
        + "81-key Mechanical Keyboard Kit RGB Backlit DIY Mechanical keyboard Aluminum alloy computer accessories for"
        + " desktop laptops\n"
        + "Features:\n"
        + "1. Material: Aluminum alloy shell, multimedia aluminum alloy knob. Note that the key cap is a photographic"
        + " prop, and the product does not contain a key cap.\n"
        + "Specifications:\n"
        + "Origin: Mainland China\n"
        + "Note: Due to different display and light effects\n"
        + "\n"
        + "Keywords: invincible、the world first\n"
        + "\n"
        + "TextStyle: Exaggerated\n"
        + "\n"
        + "Subtitle: Specifications\n"
        + "\n"
        + "New text content:\n"
        + "Specifications\n"
        + "81 key Mechanical Keyboard Kit RGB Backlit DIY Mechanical keyboard Aluminum alloy computer accessories for"
        + " desktop laptops with aluminum alloy housing and multimedia aluminum alloy knobs. Note that keycap is a "
        + "photographic prop, the product does not contain keycap, Mainland China. With 3000mAh large capacity "
        + "built-in battery, support Bluetooth compatible /2.4G wireless /Type-c cable separation connection. Our "
        + "product weight is only 2180 g, the size is about380 x200x60mm / 14.96 x7.87 x2.36 inch, very light. Please"
        + " feel free to buy.\n"
        + "\n"
        + "Title:\n"
        + "${title}\n"
        + "\n"
        + "Original text content:\n"
        + "${oldDesc}\n"
        + "\n"
        + "Keywords: ${keyWords}\n"
        + "\n"
        + "TextStyle: ${TextStyle}\n"
        + "\n"
        + "Subtitle: ${Subtitle}\n"
        + "\n"
        + "\n"
        + "New text content:";

    @Override
    protected String getDataId() {
        return "com.aidc.copilot.desc.template";
    }

    @Override
    protected void compile(String dataStr) {
        data = dataStr;
    }

    @Override
    public String getData() {
        if (StringUtil.isBlank(super.getData())) {
            return defaultTemplate;
        }
        return super.getData();
    }
}
