package com.aidc.copilot.framework.oss.image;

import com.aidc.copilot.framework.enums.FileExtEnum;

import java.io.InputStream;
import java.time.Duration;

/**
 * <AUTHOR>
 * @description OSS 客户端
 * @email <EMAIL>
 * @date 2023/6/10
 */
public interface ImagesOssClient {
    /**
     * 以图片url形式上传文件
     *
     * @param fileUri  文件 URI
     * @param fileName 文件名称
     * @param imageUrl 图片url
     * @param fileExt  文件扩展名
     * @return 资源路径
     */
    String putImage(String fileUri, String fileName, String imageUrl, FileExtEnum fileExt);

    /**
     * 以字节数组形式上传文件
     *
     * @param fileUri  文件 URI
     * @param fileName 文件名称
     * @param bytes    字节数组
     * @param fileExt  文件扩展名
     * @return 资源路径
     */
    String putImage(String fileUri, String fileName, byte[] bytes, FileExtEnum fileExt);

    /**
     * 以流形式上传文件
     *
     * @param fileUri     文件 URI
     * @param fileName    文件名称
     * @param inputStream 流
     * @param fileExt     文件扩展名
     * @return 资源路径
     */
    String putImage(String fileUri, String fileName, InputStream inputStream, FileExtEnum fileExt, Duration saveDuration);

    /**
     * 查询文件
     *
     * @param objectName 目标文件 key
     * @return
     */
    Object getImage(String objectName);
}
