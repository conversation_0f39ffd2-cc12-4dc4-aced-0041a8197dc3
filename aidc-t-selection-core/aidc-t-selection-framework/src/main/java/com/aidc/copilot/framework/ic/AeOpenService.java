package com.aidc.copilot.framework.ic;

import com.aidc.copilot.framework.ic.dto.FreightCalculateDTO;
import com.aidc.copilot.framework.ic.dto.FreightResult;
import com.aidc.copilot.framework.ic.dto.GenerateTokenDTO;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.global.iop.util.ApiException;

import java.io.IOException;
import java.util.Map;

/**
 * @Author: mianyun.yt
 * @Date: 2023/5/8
 */
public interface AeOpenService {

    /**
     * 获取 token
     */
    GenerateTokenDTO generateToken(String code) throws ApiException;

    /**
     * 刷新 token
     */
    GenerateTokenDTO refreshToken(String refreshToken) throws ApiException;

    /**
     * 计算运费
     */
    String calculateFreight(String param, String accessToken) throws ApiException;

    /**
     * 计算运费(2023-10-18 新版)
     */
    String getFreight(String param, String accessToken) throws ApiException;



    String getProduct(Long productId, String accessToken) throws ApiException;

    /**
     * 创建订单
     */
    String createOrder(String param, String token) throws ApiException;

    /**
     * 查询订单
     */
    String queryOrder(String param, String token) throws ApiException;

    /**
     * 查询查询信息
     * @param params
     * @param token
     * @return
     * @throws ApiException
     */
    String queryTrackingInfo(Map<String, String> params, String token) throws ApiException;
    /**
     * 生成联盟链
     * @param params
     * @return
     * @throws ApiException
     */
    Object  generateAffiliateLinks(Map<String, String> params) throws ApiException, IOException;

    /**
     * 支付授权
     */
    String payAuth(String param, String token) throws ApiException;

    /**
     * 支付订单分组
     */
    String payOrderGroup(String param, String token) throws ApiException;

    /**
     * 查询店铺名
     *
     * @param params
     * @param token
     * @return
     * @throws ApiException
     */
    String queryStoreName(Map<String, String> params, String token);

}
