package com.aidc.copilot.framework.openai.model;

import java.util.List;

import cn.hutool.core.util.RandomUtil;
import com.aidc.copilot.framework.openai.api.KeyStrategyFunction;

/**
 * 描述：
 *
 * <AUTHOR>
 * @since 2023-04-03
 */
public class KeyRandomStrategy implements KeyStrategyFunction<List<String>, String> {

    @Override
    public String apply(List<String> apiKeys) {
        return RandomUtil.randomEle(apiKeys);
    }
}
