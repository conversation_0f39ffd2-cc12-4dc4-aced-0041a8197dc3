package com.aidc.copilot.framework.feedback;


import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @ClassName WeeklyReportData
 * <AUTHOR>
 * @Date 2024/6/19 10:25
 */
@Data
@AllArgsConstructor
public class WeeklyReportData {
    /**
     * 关联链路id
     */
    private String trace;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 生成场景
     */
    private String generateScene;
    /**
     * 模版反馈类型
     */
    private String feedbackType;
    /**
     * 生成模型
     */
    private String feedbackModel;
    /**
     * 模版反馈类型
     */
    private String requestPrompt;
    /**
     * 生成数据
     */
    private String generateResult;

}
