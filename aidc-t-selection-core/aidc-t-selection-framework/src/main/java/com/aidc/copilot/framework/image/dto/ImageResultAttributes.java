package com.aidc.copilot.framework.image.dto;

import com.alibaba.copilot.boot.basic.data.Attributes;

/**
 * 图片结果Attributes
 */
public class ImageResultAttributes extends Attributes {

    /**
     * 执行任务的id
     */
    private static final String ATTR_TASK_ID = "taskId";

    /**
     * 失败原因
     */
    private static final String ATTR_FAIL_REASON = "failReason";

    public ImageResultAttributes(String json) {
        super(json);
    }

    public void setTaskId(Long taskId) {
        put(ATTR_TASK_ID, taskId);
    }

    public Long getTaskId() {
        return getAsLong(ATTR_TASK_ID);
    }

    public void setFailReason(String failReason) {
        put(ATTR_FAIL_REASON, failReason);
    }

    public String getFailReason() {
        return getAsString(ATTR_FAIL_REASON);
    }
}
