package com.aidc.copilot.framework.product.converter;

import com.aidc.copilot.dal.product.dataobject.ReleaseProductDO;
import com.aidc.copilot.framework.product.model.ProductImage;
import com.aidc.copilot.framework.product.model.release.ReleaseProduct;
import com.aidc.copilot.framework.product.model.release.ReleaseProductAttributes;
import com.aidc.copilot.framework.product.model.release.ReleaseProductStatus;
import com.alibaba.copilot.boot.basic.factory.Converter;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

/**
 * @desc:
 * @author: yixiao.cx
 * @create: 2023-07-07
 **/
@Component
public class ReleaseProductConverter implements Converter<ReleaseProductDO, ReleaseProduct> {
    public static final Converter<ReleaseProductDO, ReleaseProduct> INSTANCE = new ReleaseProductConverter();

    private static final String[] ignoreProperties = new String[] {"attributes", "status","images"};

    @Override
    public ReleaseProduct convertA2B(ReleaseProductDO releaseProductDO) {
        if (releaseProductDO == null) {
            return null;
        }
        ReleaseProduct product = new ReleaseProduct();
        BeanUtils.copyProperties(releaseProductDO, product, ignoreProperties);
        product.setImages(JSONObject.parseArray(releaseProductDO.getImages(), ProductImage.class));
        product.setStatus(ReleaseProductStatus.valueOf(releaseProductDO.getStatus()));
        product.setAttributes(new ReleaseProductAttributes(releaseProductDO.getAttributes()));
        return product;
    }

    @Override
    public ReleaseProductDO convertB2A(ReleaseProduct releaseProduct) {
        if (releaseProduct == null) {
            return null;
        }
        ReleaseProductDO productDO = new ReleaseProductDO();
        BeanUtils.copyProperties(releaseProduct, productDO, ignoreProperties);
        productDO.setImages(JSONArray.toJSONString(releaseProduct.getImages()));
        productDO.setStatus(releaseProduct.getStatus().name());
        productDO.setAttributes(releaseProduct.getAttributes().toString());
        return productDO;
    }
}
