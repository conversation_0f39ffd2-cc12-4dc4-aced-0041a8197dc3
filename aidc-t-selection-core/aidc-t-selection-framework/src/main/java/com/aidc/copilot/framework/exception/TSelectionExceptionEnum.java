package com.aidc.copilot.framework.exception;

import lombok.Getter;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/4/28
 */
@Getter
public enum TSelectionExceptionEnum {
    /**
     * 系统错误
     */
    PARAM_ERROR("0000", "参数错误"),

    /**
     * 账号相关错误
     */
    INVALID_ACCOUNT("1000", "无效账号, 请注册!"),
    LOGIN_STATUS_FAIL("1001", "账号未登录!"),
    SHOPIFY_NOT_BIND("1002", "please go to Shopify and link your store with Dropshipping Copilot"),

    /**
     * 商品相关错误
     */
    COMMODITY_NOT_EXIST_IN_IMPORT_LIST("2000", "The selected pool item does not exist"),
    COMMODITY_NOT_EXIST_IN_PLATFORM("2001", "Platform product does not exist"),
    PUBLISHED_EMPTY_COMMODITY("2002", "Empty items cannot be posted"),
    PUBLISHED_COMMODITY_TO_SHOPIFY_FAIL("2003", "Publishing an item to Shopify fails"),
    UNPUBLISH_COMMODITY_TO_SHOPIFY_FAIL("2004", "Shopify corresponding product removal failed"),
    DERLETE_COMMODITY_FAIL("2005", "Product deletion failed"),
    GET_SHOPIFY_LINK_FAIL("2006", "Failed to obtain product Shopify link"),
    PUBLISHED_SOME_COMMODITY_TO_SHOPIFY_FAIL("2007", "Publishing to Shopify fails for some items: "),
    GENERATE_PRODUCT_DESCRIPTION_FAIL("2008", "A product description wasn't created. Please try again."),
    GENERATE_PRODUCT_TITLE_FAIL("2009", "A product title wasn't created. Please try again."),
    GET_ORIGIN_IMAGE_URL_FAIL("2010","Failed to obtain the original image URL"),
    CHANNEL_PRODUCT_INFO_NOT_EXITS("2011","this channel product info is not exist"),
    CHANNEL_PRODUCT_SKU_NOT_EXITS("2012","this channel product skus is not exist"),
    KEYWORD_SEARCH_ERROR("2023","Please try again or change a keyword"),

    RELEASE_PRODUCT_SHOPIFY_NOT_EXIST("2024","the release product in shopify is not exist"),
    RELEASE_PRODUCT_NOT_EXIST("2025", "the releaseProduct not exist"),
    COMMODITYID_OF_CHANNELPRODUCT_NULL("2026", "the commodityId of channelProduct is null"),

    /**
     * 订单相关错误
     */
    FULFILLED_ORDER_STATUS_ILLEGAL("3000", "system has an exception, please try again"),
    NOT_SUPPORT_ACTION("3001", "system has an exception, please try again"),
    FULFILLED_PRODUCT_NOT_EXIST("3002", "Please specify the supplier's products"),
    FULFILLED_ORDER_LINE_NOT_EXIST("3003", "create supplier order error, please try again"),
    ORDER_EVENT_NOT_MATCH("3004", "system has an exception, please try again"),
    ORDER_HANDLER_NOT_EXIST("3005", "system has an exception, please try again"),
    FULFILLED_ORDER_NOT_EXIST("3006", "Please check the supplier's order"),
    FULFILLED_ORDER_CREATE_FAIL("3007", "The order interface is abnormal. Please try again"),
    CAN_NOT_FOUND_SHIP_METHOD("3008", "The supplier has no available shipping method at present"),
    ADDRESS_INVALIDATE("3009", "Please complete your shipping address"),
    CAN_NOT_FOUND_ITEM("3010", "Please check the supplier's products"),
    CAN_NOT_FOUND_SKU("3011", "Verify supplier SKUs"),
    SHIP_ADDRESS_NOT_SPECIFY("3012", "Please complete your shipping address"),
    SYNC_SHOPIFY_ORDER_SHIP_FAIL("3013", "system has an exception, please try again"),
    ORDER_NOT_EXIST("3014", "system has an exception, please try again"),
    SUPPLIER_NOT_EXIST("3015", "please bind supplier in 'Settings-Supplier' menu"),
    NOT_SPECIFY_FULFILLED_PRODUCT("3016", "Please specify the supplier's products"),
    NOT_SPECIFY_FULFILLED_SKU("3017", "Please complete the supplier's products"),
    NOT_SUPPORT_COUNTRY("3018", "The supplier cannot provide the country/region distribution method for the time being"),
    POST_CODE_ILLEGAL("3019", "invalid Post Code"),
    MOBILE_NO_ILLEGAL("3020", "invalid Tel"),
    SYSTEM_ERROR("3021", "The order interface is abnormal. Please try again"),
    TRACKING_INFO_NOT_UPDATE("3022", "The tracking information has not been updated yet or tracking unavailable"),
    PAY_AUTH_CODE_ERROR("3023", "The orders cannot merged pay"),
    PAY_ORDER_GROUP_ERROR("3024", "get order group error, please try again"),
    ORDER_MEMO_LENGTH_ERROR("3025","The order note is too long. Please shorten it and try again."),

    /**
     * 开放平台token相关
     */
    OPEN_PALTFOEM_TOKEN_EXPIRE_ERROR("4000", "AE open platform token has expired"),
    OPEN_PALTFOEM_ACCESS_CODE_ERROR("4001", "AE open platform code is empty"),
    OPEN_PALTFOEM_ACCESS_TOKEN_ERROR("4002", "AE open platform token acquisition failed"),

    /**
     * 重定向相关
     */
    OPEN_PALTFOEM_REDIRECT_ERROR("4003", "Jump to homepage exception"),

    /**
     * 选品相关
     */
    SELECTION_PARAM_ERROR("5000", "Please provide the AliExpress URL link to search for the same product."),
    SELECTION_UPLOAD_IMAGE_ERROR("5001", "Upload image failed, please try again!"),
    SELECTION_SAME_SEARCH_ERROR("5002", "same product search failed, please try again!"),
    SELECTION_IMAGE_TOO_LARGE("5003", "The uploaded image is too large. Please upload an image that is 400k or smaller and 800*800 or less！"),
    SELECTION_IMAGE_PARAM_ERROR("5004", "The image format is incorrect."),
    SELECTION_NOT_SUPPORT_FILTER_ITEMS("5005", "current shipping to country not support filter items."),

    LOGIN_EXPIRE_ERROR("6000", "session has expired"),

    /**
     * 订阅相关
     */
    SUBSCRIPTION_PLAN_NOT_EXIST("7000", "subscription plan does not exist, please try again!"),
    SUBSCRIPTION_EXIST_ANNUAL_PLAN("7001", "Since you already have an active annual plan, you can't subscribe to a monthly plan"),
    SUBSCRIPTION_NOT_ACTIVITY("7003", "params error, please try again!"),
    SUBSCRIPTION_SHOPIFY_CANCEL_FAIL("7004", "Subscription not canceled"),
    SUBSCRIPTION_SHOPIFY_CREATE_FAIL("7005", "You weren't subscribed. Please try again."),
    SUBSCRIPTION_EXIST_IN_OTHER_SHOP("7002", "Subscription made with another shop. Try again."),
    SUBSCRIPTION_NOT_SUPPORT("7006", "Dropshipping Copilot app is not officially available right now, you currently unable to upgrade the plan, Please stay tuned for updates！"),
    SUBSCRIPTION_NOT_SUPPORT_FOR_BETA("7007", "The beta version doesn't support upgrading plans. Please install the official Dropshipping Copilot app and try upgrading again."),

    /**
     * 权益相关
     */
    BENEFIT_OVER_LIMIT("8000", "You're almost out of optimizations.If you'd like to continue using optimization, please upgrade to the Advanced Plan."),
    BENEFIT_NOT_EXIST("8001", "权益不存在"),
    BENEFIT_USAGE_RECORD_NOT_EXIST("8002", "权益使用记录不存在"),
    BENEFIT_USAGE_RECORD_CANCELED("8003", "权益使用记录已回滚"),
    BENEFIT_USAGE_RECORD_CONFIRMED("8005", "权益使用记录已确认"),
    BENEFIT_USAGE_TCC_OP_FAIL("8004", "权益使用操作失败"),

    /**
     * 多供应商相关
     */
    LOCATION_SUPPLIER_EXIST_ERROR("9001","Location supplier already exists"),
    NOT_SET_AS_SUPPLIER_ERROR("9002","Please set this as a supplier for this location before selecting a default delivery service"),
    LOCATION_SUPPLIER_PRODUCT_EXIST_ERROR("9003","Location supplier product already exists"),
    LOCATION_SUPPLIER_NUMBER_ERROR("9004","The number of regional supplier cards cannot exceed 15"),

    SUPPLIER_INFO_NOT_EXIST("9005","Please navigate to [Account -> Settings -> Sourcing Accounts] to link your AliExpress account"),
    SUPPLIER_INFO_IS_NOT_EXIST("9006","the supplier info is not exist"),
    CHANNEL_PRODUCT_PRICE_FROM_AE_IS_NOT_EXIST("9007","the channel product price from ae is not exist"),


    /**
     * 任务相关
     */
    TASK_FOR_UNIQUE_KEY_NOT_EXISTS("10001", "the task is not exists"),

    TASK_IPS_EMPTY("10002","getIdleIp ips is empty"),

    /**
     * shopify 相关
     */
    SHOPIFY_STORE_SHOP_DOMAIN_IS_NOT_EXIST("11000", "the store shopDomain is not exit!"),
    SHOPIFY_STORE_UNAVAILABLE("11001", "the store is unavailable!"),

    /**
     * 订阅使用
     */
    SUBSCRIPTION_USAGE_FAIL("12000", "subscription usage fail！")
    ;


    private final String errorCode;
    private final String errorMessage;

    TSelectionExceptionEnum(String errorCode, String errorMessage) {
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
    }
}
