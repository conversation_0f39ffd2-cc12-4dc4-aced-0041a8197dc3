package com.aidc.copilot.framework.diagnosis;

import com.aidc.copilot.dal.diagnosis.dataobject.DiagnosisResultDO;
import com.aidc.copilot.dal.diagnosis.mapper.DiagnosisResultMapper;
import com.aidc.copilot.framework.diagnosis.converter.DiagnosisResultConverter;
import com.aidc.copilot.framework.diagnosis.dto.DiagnosisResultDTO;
import com.aidc.copilot.framework.diagnosis.request.DiagnosisResultQuery;
import com.aidc.copilot.framework.utils.CollectionUtils;
import com.alibaba.copilot.boot.basic.result.PageResult;
import com.alibaba.copilot.boot.monitor.annotation.Monitor;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 诊断结果repository
 */
@Repository
public class DiagnosisResultRepository {

    @Resource
    private DiagnosisResultMapper resultMapper;

    @Monitor(name = "创建或修改诊断结果", level = Monitor.Level.P1, layer = Monitor.Layer.REPOSITORY)
    public boolean createOrUpdate(DiagnosisResultDTO resultDTO) {
        DiagnosisResultDO diagnosisResultDO = DiagnosisResultConverter.INSTANCE.dtoToDO(resultDTO);
        diagnosisResultDO.setGmtModified(new Date());

        if (diagnosisResultDO.getId() != null) {
            int result = resultMapper.updateById(diagnosisResultDO);
            return result == 1L;
        }

        diagnosisResultDO.setGmtCreate(new Date());
        int result = resultMapper.insert(diagnosisResultDO);
        resultDTO.setId(diagnosisResultDO.getId());
        return result == 1L;
    }

    @Monitor(name = "分页获取诊断结果列表", level = Monitor.Level.P1, layer = Monitor.Layer.REPOSITORY)
    public PageResult<DiagnosisResultDTO> queryPageResult(DiagnosisResultQuery query, String shopDomain, Integer currentBatch) {
        Page<DiagnosisResultDO> queryPage = new Page<>(query.getPageNum(), query.getPageSize());

        QueryWrapper<DiagnosisResultDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("deleted", 0);
        queryWrapper.eq("shop_domain", shopDomain);
        queryWrapper.eq("batch", currentBatch);
        // 只查需要优化的诊断结果
        queryWrapper.eq("optimized", 1);
        queryWrapper.like(query.getSearchKey() != null, "title", query.getSearchKey());
        // 按照最大收益进行倒排
        queryWrapper.orderBy(true, false, "max_profit");

        Page<DiagnosisResultDO> resultDOPage = resultMapper.selectPage(queryPage, queryWrapper);
        if (CollectionUtils.isEmpty(resultDOPage.getRecords())) {
            return PageResult.buildSuccess(new ArrayList<>());
        }

        List<DiagnosisResultDTO> result = Lists.newArrayList();

        for (DiagnosisResultDO record : resultDOPage.getRecords()) {
            if (record == null) {
                continue;
            }

            result.add(DiagnosisResultConverter.INSTANCE.doToDto(record));
        }

        return PageResult.buildSuccess(result)
                .setPageNum(resultDOPage.getCurrent())
                .setPageSize(resultDOPage.getSize())
                .setTotal(resultDOPage.getTotal())
                .setTotalPage(resultDOPage.getPages());
    }

    @Monitor(name = "获取诊断结果列表", level = Monitor.Level.P1, layer = Monitor.Layer.REPOSITORY)
    public List<DiagnosisResultDTO> queryDiagnosisResult(Long itemId, String shopDomain) {

        QueryWrapper<DiagnosisResultDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("deleted", 0);
        queryWrapper.eq("shop_domain", shopDomain);
        queryWrapper.eq("item_id", itemId);
        queryWrapper.orderBy(true, false, "id");

        return DiagnosisResultConverter.INSTANCE.doToDtoList(resultMapper.selectList(queryWrapper));
    }

    @Monitor(name = "获取用户诊断次数", level = Monitor.Level.P1, layer = Monitor.Layer.REPOSITORY)
    public Long queryDiagnosisResultCount(Long userId) {
        QueryWrapper<DiagnosisResultDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("merchant_id", userId);
        queryWrapper.orderBy(true, false, "id");
        return resultMapper.selectCount(queryWrapper);
    }
}
