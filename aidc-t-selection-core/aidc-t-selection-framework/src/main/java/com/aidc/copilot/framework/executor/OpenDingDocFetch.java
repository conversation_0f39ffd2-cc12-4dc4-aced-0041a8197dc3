package com.aidc.copilot.framework.executor;

import com.aidc.copilot.framework.shopify.product.dto.Option;
import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.fastjson.JSON;
import com.aliyun.aliding20230426.models.GetDocContentTakIdHeaders;
import com.aliyun.aliding20230426.models.GetDocContentTakIdResponse;
import com.aliyun.aliding20230426.models.GetDocContentTakIdResponseBody;
import com.aliyun.tea.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Service
public class OpenDingDocFetch {
    public static com.aliyun.aliding20230426.Client createClient() throws Exception {
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config();
        // endpoint按如下设置
        config.endpoint = "aliding.aliyuncs.com";
        // 应用的accessKeyId
        config.setAccessKeyId("yFv91CzJOJCELBxxATIl");
        // 应用的accessKeySecret
        config.setAccessKeySecret("****************************************************************");
        return new com.aliyun.aliding20230426.Client(config);
    }

    public SingleResult<Long> getDingDocExportTaskId(String dingDocId, String empId) throws Exception{
        com.aliyun.aliding20230426.Client client = OpenDingDocFetch.createClient();
        com.aliyun.aliding20230426.models.GetDocContentTakIdHeaders getDocContentTakIdHeaders = new com.aliyun.aliding20230426.models.GetDocContentTakIdHeaders().setAccountContext(new GetDocContentTakIdHeaders.GetDocContentTakIdHeadersAccountContext().setAccountId(formatAccountId(empId == null ? "224021": empId)));
        com.aliyun.aliding20230426.models.GetDocContentTakIdRequest getDocContentTakIdRequest = new com.aliyun.aliding20230426.models.GetDocContentTakIdRequest().setDentryUuid(dingDocId);
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        try {
            // 复制代码运行请自行打印 API 的返回值
            GetDocContentTakIdResponse docContentTakIdWithOptions = client.getDocContentTakIdWithOptions(getDocContentTakIdRequest, getDocContentTakIdHeaders, runtime);
            log.info("getDocContentTakIdWithOptions success, result: {}", JSON.toJSONString(docContentTakIdWithOptions));
            Long taskId = Optional.ofNullable(docContentTakIdWithOptions.getBody()).map(GetDocContentTakIdResponseBody::getTaskId).orElse(null);
            return SingleResult.buildSuccess(taskId);
        } catch (TeaException error) {
            // 错误 message
            log.error("TeaException errMessage:" + error.getMessage());
            // 诊断地址
            log.error("TeaException errRecommend:" + error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
            return SingleResult.buildFailure(error.getMessage());
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 错误 message
            log.error("errMessage" + error.getMessage());
            // 诊断地址
            log.error("errRecommend" + error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
            return SingleResult.buildFailure(error.getMessage());
        }
    }

    private String formatAccountId(String accountId) {
        if (StringUtils.isBlank(accountId)) {
            return accountId;
        }
        accountId = accountId.trim();
        int length = accountId.length();
        if (length >= 6) {
            return accountId;
        }
        if (accountId.charAt(0) >= '0' && accountId.charAt(0) <= '9') {
            accountId = StringUtils.leftPad(accountId, 6, '0');
        }
        return accountId;
    }
}
