package com.aidc.copilot.framework.product.model.release;

import com.alibaba.copilot.boot.basic.data.Attributes;

/**
 * @desc:
 * @author: yixiao.cx
 * @create: 2023-07-10
 **/
public class ReleaseProductAttributes extends Attributes {
    public ReleaseProductAttributes(String json) {
        super(json);
    }

    private static final String ATTR_COMMODITY_ID = "commodityId";

    /**
     * 货币
     */
    private static final String ATTR_CURRENCY = "currency";
    /**
     * 语言
     */
    private static final String ATTR_LANG = "lang";
    /**
     * 国家
     */
    private static final String ATTR_COUNTRY = "country";

    /**
     * 从Shopify往回迁移
     */
    private static final String ATTR_MIGRATE_FROM_SHOPIFY = "migrateBackFromShopify";

    public void setCommodityId(Long id) {
        put(ATTR_COMMODITY_ID, id);
    }

    public Long getCommodityId() {
        return getAsLong(ATTR_COMMODITY_ID);
    }

    public void setCurrency(String currency) {
        put(ATTR_CURRENCY, currency);
    }

    public String getCurrency() {
        return getAsString(ATTR_CURRENCY);
    }

    public void setLang(String lang) {
        put(ATTR_LANG, lang);
    }

    public String getLang() {
        return getAsString(ATTR_LANG);
    }

    public void setCountry(String country) {
        put(ATTR_COUNTRY, country);
    }

    public String getCountry() {
        return getAsString(ATTR_COUNTRY);
    }

    public void setMigrateBackFromShopify(Boolean migrateBackFromShopify) {
        if (migrateBackFromShopify != null) {
            put(ATTR_MIGRATE_FROM_SHOPIFY, migrateBackFromShopify);
        }
    }
}
