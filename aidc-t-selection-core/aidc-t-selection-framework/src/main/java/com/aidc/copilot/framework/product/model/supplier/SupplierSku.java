package com.aidc.copilot.framework.product.model.supplier;

import com.alibaba.copilot.boot.basic.data.BaseObject;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * @desc:
 * @author: yixiao.cx
 * @create: 2023-07-07
 **/
@Setter
@Getter
public class SupplierSku extends BaseObject {

    /**
     * ID
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 商家ID
     */
    private Long merchantId;

    /**
     * 商家店铺
     */
    private String shopDomain;

    /**
     * 供应商产品ID
     */
    private Long supplierProductId;

    /**
     * 发布SKU ID
     */
    private Long releaseSkuId;

    /**
     * 来源SKU ID
     */
    private Long sourceSkuId;

    /**
     * 删除标记
     */
    private Boolean deleted;

    /**
     * 属性（JSON格式）
     */
    private SupplierSkuAttributes attributes = new SupplierSkuAttributes(null);
}
