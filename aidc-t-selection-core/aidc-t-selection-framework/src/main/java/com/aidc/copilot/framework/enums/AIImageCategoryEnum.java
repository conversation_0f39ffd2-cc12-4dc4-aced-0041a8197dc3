package com.aidc.copilot.framework.enums;

import com.alibaba.copilot.boot.basic.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * AI抠图图片类型
 */
@Getter
@AllArgsConstructor
public enum AIImageCategoryEnum implements IEnum<AIImageCategoryEnum> {

    ORIGIN("origin", "原图", "/originPic/"),
    IMAGE_CUT("imageCut", "抠图", "/imageCutPic/"),
    MASK_CUT("MASK_CUT", "有位置信息的抠图", "/maskCutPic/"),
    COMPOSE("COMPOSE", "合图", "/imageCompose/"),
    REMOVE_WATERMARK("removeWatermark", "去水印图", "/removeWatermarkPic/"),
    SUPER_RESOLUTION("superResolution", "超分图", "/superResolution/"),

    GENERATE_PIC("generatePic", "AI 算法生成图", "/generatePic/"),
    SUPPLIES_PIC("SUPPLIES_PIC", "SUPPLIES_PIC", "/suppliesPic/"),
    ADMIN_FILE("ADMIN_FILE", "ADMIN_FILE", "/adminFile/"),
    DEFAULT("DEFAULT", "DEFAULT", "/default/"),

    MODEL_SKIN_REJUVENATION("MODEL_SKIN_REJUVENATION", "模特换肤", "/modelSkinRejuvenation/"),
    ;

    public final String code;
    public final String name;
    public final String ossPath;
}