package com.aidc.copilot.framework.diamond;

import com.aidc.copilot.framework.annotation.DiamondConfigInfo;
import com.aidc.copilot.framework.diamond.updatemodel.UpdateModelConfig;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;

/**
 * <b>针对Json格式的Diamond配置做统一封装, 简化每个子类的冗余代码逻辑</b><br/>
 * 配合{@link DiamondConfigInfo}注解使用
 * <p>
 * 使用示例参: {@link UpdateModelConfig}
 *
 * <AUTHOR>
 * @version 2023/6/14
 */
@Slf4j
public class JsonConfigCenter<T> extends AbstractConfigCenter<T> {

    private final DiamondConfigInfo diamondConfigInfo;

    public JsonConfigCenter() {
        super();
        diamondConfigInfo = getClass().getAnnotation(DiamondConfigInfo.class);
    }

    @Override
    protected String getGroupId() {
        return diamondConfigInfo.groupId();
    }

    @Override
    protected String getDataId() {
        return diamondConfigInfo.dataId();
    }

    @Override
    protected void compile(String dataStr) {
        log.info("compile start: {}", dataStr);
        if (dataStr == null) {
            return;
        }

        Type genericType = ((ParameterizedType) getClass().getGenericSuperclass()).getActualTypeArguments()[0];
        try {
            data = JSON.parseObject(dataStr, genericType);
        } catch (JSONException e) {
            log.error("compile error", e);
        }
    }
}
