package com.aidc.copilot.framework.commodity;

import com.aidc.copilot.framework.commodity.dto.ItemPriceDTO;
import com.alibaba.global.ic.dto.scenario.query.ProductQueryResultDTO;
import com.aliexpress.price.open.model.result.ItemPriceResult;

import java.util.List;
import java.util.Locale;

/**
 * 价格服务
 * 参考文档：https://aliyuque.antfin.com/ae-marketing/price/xauchpftsg0ywpfc?singleDoc#v6ZPT
 */
public interface PriceCenterService {

    /**
     * @param productId
     * @param country
     * @param curreny
     * @param locale
     * @return
     */
    ItemPriceResult queryPrice(Long productId, String country, String curreny, Locale locale, Long buyerId);

    /**
     * @param product
     * @param country
     * @param curreny
     * @param locale
     * @return
     */
    ItemPriceResult queryPrice(ProductQueryResultDTO product, String country, String curreny, Locale locale, Long buyerId);

    /**
     * @param product
     * @param country
     * @param curreny
     * @param locale
     * @return
     */
    ItemPriceDTO queryDiscountPrice(ProductQueryResultDTO product, String country, String curreny, Locale locale, Long buyerId);

    /**
     *
     * @param products
     * @param country
     * @param curreny
     * @param locale
     * @param buyerId
     * @return
     */
    List<ItemPriceDTO> queryPriceByProducts(List<ProductQueryResultDTO> products,String country, String curreny, Locale locale, Long buyerId);

}
