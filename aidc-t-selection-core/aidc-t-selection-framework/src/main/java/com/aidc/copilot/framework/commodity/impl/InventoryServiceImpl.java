package com.aidc.copilot.framework.commodity.impl;

import com.aidc.copilot.framework.commodity.InventoryService;
import com.aidc.copilot.framework.utils.CommodityUtils;
import com.alibaba.ecommerce.module.Response;
import com.alibaba.fastjson2.JSON;
import com.alibaba.global.ic.dto.scenario.query.ProductQueryResultDTO;
import com.alibaba.global.ic.dto.scenario.query.SkuQueryResultDTO;
import com.alibaba.global.inventory.api.InventoryMerchantFacade;
import com.alibaba.global.inventory.api.request.InvSkuDTO;
import com.alibaba.global.inventory.api.request.channel.ChannelInvQuerySkuRequestDTO;
import com.alibaba.global.inventory.api.request.merchant.InvQuerySkuRequestDTO;
import com.alibaba.global.inventory.api.response.channel.ChannelInvQueryResultDTO;
import com.alibaba.global.inventory.api.response.channel.ChannelInvQuerySkuResultDTO;
import com.alibaba.global.inventory.api.response.merchant.InvQuerySkuResultDTO;
import com.alibaba.global.inventory.api.response.merchant.InvSellableStatusSkuResultDTO;
import com.alibaba.global.inventory.platform.api.InventoryChannelReadFacade;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * @Author: mianyun.yt
 * @Date: 2023/5/4
 */
@Service
@Slf4j
public class InventoryServiceImpl implements InventoryService {

    @Resource
    private InventoryMerchantFacade inventoryMerchantFacade;

    @Resource
    private InventoryChannelReadFacade inventoryChannelReadFacade;

    public ExecutorService commodityInvQueryExecutor;

    public InventoryServiceImpl() {
        commodityInvQueryExecutor = new ThreadPoolExecutor(10, 20, 0, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(), new ThreadFactoryBuilder().setNameFormat("commodity-inv-query-%d").build());
    }

    @Override
    public Long getInventoryBySkuId(Long sellerId, Long itemId, Long scItemId, Long skuId) {
        log.info("InventoryServiceImpl.getInventoryBySkuId, sellerId={}, itemId={}, scItemId={}, skuId={}", sellerId, itemId, scItemId, skuId);

        setTargetCluster();

        InvSkuDTO invSkuDTO = new InvSkuDTO();
        invSkuDTO.setSkuId(skuId);
        invSkuDTO.setItemId(itemId);
        invSkuDTO.setScItemId(scItemId);
        invSkuDTO.setSellerId(sellerId);

        InvQuerySkuRequestDTO query = new InvQuerySkuRequestDTO();
        query.setInvSkuDTO(invSkuDTO);

        List<InvQuerySkuRequestDTO> queryList = Collections.singletonList(query);
        log.info("InventoryServiceImpl.getInventoryBySkuId, queryList={}", JSON.toJSONString(queryList));
        Response<List<InvQuerySkuResultDTO>> response = inventoryMerchantFacade.batchQuery(queryList);
        log.info("InventoryServiceImpl.getInventoryBySkuId, response={}", JSON.toJSONString(response));

        if (response.isNotSuccess()) {
            log.error("查询 sku 库存失败, skuId：{}, 错误码：{}", skuId, response.getErrorCode());
            throw new RuntimeException(String.format("查询 sku 库存失败, skuId：%s, 错误码：%s", skuId, response.getErrorCode()));
        }
        List<InvQuerySkuResultDTO> inventoryList = response.getModule();

        if (CollectionUtils.isEmpty(inventoryList)) {
            log.warn("商品不存在 sku {}", skuId);
            throw new RuntimeException(String.format("商品不存在 sku %s", skuId));
        }

        for (InvQuerySkuResultDTO invQuerySkuResultDTO : inventoryList) {
            if (invQuerySkuResultDTO.getInvSkuDTO() != null && Objects.equals(invQuerySkuResultDTO.getInvSkuDTO().getSkuId(), skuId)) {
                return invQuerySkuResultDTO.getSellableQuantity();
            }
        }

        throw new RuntimeException(String.format("商品库存不存在 sku %s", skuId));
    }

    @Override
    public Long getChannelInventoryBySkuId(Long sellerId, Long itemId, Long scItemId, Long skuId) {
        log.info("InventoryServiceImpl.getChannelInventoryBySkuId, sellerId={}, itemId={}, scItemId={}, skuId={}", sellerId, itemId, scItemId, skuId);

        setTargetCluster();

        InvSkuDTO invSkuDTO = new InvSkuDTO();
        invSkuDTO.setSkuId(skuId);
        invSkuDTO.setItemId(itemId);
        invSkuDTO.setScItemId(scItemId);
        invSkuDTO.setSellerId(sellerId);

        ChannelInvQuerySkuRequestDTO channelInvQuerySkuRequestDTO = new ChannelInvQuerySkuRequestDTO();
        channelInvQuerySkuRequestDTO.setInvSkuDTO(invSkuDTO);
        channelInvQuerySkuRequestDTO.setQueryInvResource(true);
        List<ChannelInvQuerySkuRequestDTO> channelInvQuerySkuRequestDTOList = Collections.singletonList(channelInvQuerySkuRequestDTO);
        log.info("InventoryServiceImpl.getChannelInventoryBySkuId, channelInvQuerySkuRequestDTOList={}", JSON.toJSONString(channelInvQuerySkuRequestDTOList));
        Response<ChannelInvQueryResultDTO> batchQueryChannelInventoryResponse = inventoryChannelReadFacade.batchQueryChannelInventory(channelInvQuerySkuRequestDTOList);
        log.info("InventoryServiceImpl.getChannelInventoryBySkuId, batchQueryChannelInventoryResponse={}", JSON.toJSONString(batchQueryChannelInventoryResponse));

        if (batchQueryChannelInventoryResponse.isNotSuccess()) {
            log.error("查询 sku 库存失败, skuId：{}, 错误码：{}", skuId, batchQueryChannelInventoryResponse.getErrorCode());
            throw new RuntimeException(String.format("查询 sku 库存失败, skuId：%s, 错误码：%s", skuId, batchQueryChannelInventoryResponse.getErrorCode()));
        }

        ChannelInvQueryResultDTO channelInvQueryResultDTO = batchQueryChannelInventoryResponse.getModule();
        if (channelInvQueryResultDTO == null) {
            log.error("channelInvQueryResultDTO is null, 商品不存在 sku {}", skuId);
            throw new RuntimeException(String.format("channelInvQueryResultDTO is null, 商品不存在 sku %s", skuId));
        }

        List<ChannelInvQuerySkuResultDTO> channelInvQuerySkuResultDTOList = channelInvQueryResultDTO.getChannelInvQuerySkuResultDTOList();
        if (CollectionUtils.isEmpty(channelInvQuerySkuResultDTOList)) {
            log.error("channelInvQuerySkuResultDTOList is empty, 商品不存在 sku {}", skuId);
            throw new RuntimeException(String.format("channelInvQuerySkuResultDTOList is empty, 商品不存在 sku %s", skuId));
        }

        ChannelInvQuerySkuResultDTO channelInvQuerySkuResultDTO = channelInvQuerySkuResultDTOList.get(0);
        Long channelInventory = channelInvQuerySkuResultDTO.getDefaultChannelStock();
        if (channelInventory == null) {
            log.error("channelInventory is null, 商品不存在 sku {}", skuId);
            throw new RuntimeException(String.format("channelInventory is null, 商品不存在 sku %s", skuId));
        }
        log.info("InventoryServiceImpl.getChannelInventoryBySkuId, channelInventory={}", channelInventory);

        return channelInventory;
    }

    @Override
    public Long getInventoryBySkuId(ProductQueryResultDTO product, SkuQueryResultDTO sku) {
        return getInventoryBySkuId(product.getSellerQueryResultDTO().getId(), product.getProductId(), CommodityUtils.getScItemId(sku), sku.getSkuId());
    }

    @Override
    public List<InvQuerySkuResultDTO> batchQueryInventory(List<InvSkuDTO> invSkuDTOList) {

        setTargetCluster();

        List<InvQuerySkuRequestDTO> queryList = invSkuDTOList.stream().map(sku -> {
            InvQuerySkuRequestDTO query = new InvQuerySkuRequestDTO();
            query.setInvSkuDTO(sku);
            return query;
        }).collect(Collectors.toList());

        // sku 最多200
        List<List<InvQuerySkuRequestDTO>> invqSkus = Lists.partition(queryList, 50);

        CopyOnWriteArrayList<InvQuerySkuResultDTO> result = new CopyOnWriteArrayList<>();

        CountDownLatch countDownLatch = new CountDownLatch(invqSkus.size());

        invqSkus.forEach(inv -> {
            commodityInvQueryExecutor.execute(() -> {
                try {
                    Response<List<InvQuerySkuResultDTO>> invRes = inventoryMerchantFacade.batchQuery(inv);
                    if (invRes.isNotSuccess()) {
                        log.error("批量查询库存失败, skus：{}, 错误码：{}", JSON.toJSONString(inv.stream().map(InvQuerySkuRequestDTO::getInvSkuDTO).map(InvSkuDTO::getSkuId).collect(Collectors.toList())), invRes.getErrorCode());
                    } else {
                        result.addAll(invRes.getModule());
                    }
                } catch (Exception e) {
                    log.error("批量查询库存失败, skus：{}, 错误：{}", JSON.toJSONString(inv.stream().map(InvQuerySkuRequestDTO::getInvSkuDTO).map(InvSkuDTO::getSkuId).collect(Collectors.toList())), e.getMessage(), e);
                } finally {
                    countDownLatch.countDown();
                }
            });
        });
        try {
            countDownLatch.await(30, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            log.error("批量查询库存失败:{}", e.getMessage(), e);
        }
        return new ArrayList<>(result);
    }

    @Override
    public List<InvQuerySkuResultDTO> batchQueryInventory(ProductQueryResultDTO product) {
        if (product == null) {
            return new ArrayList<>();
        }
        List<SkuQueryResultDTO> skus = product.getSkuList();
        if (CollectionUtils.isEmpty(skus)) {
            return new ArrayList<>();
        }

        List<InvSkuDTO> invSkuDTOList = skus.stream().map(sku -> {
            InvSkuDTO dto = new InvSkuDTO();
            dto.setSkuId(sku.getSkuId());
            dto.setItemId(product.getProductId());
            dto.setScItemId(CommodityUtils.getScItemId(sku));
            dto.setSellerId(product.getSellerQueryResultDTO().getId());

            return dto;
        }).collect(Collectors.toList());

        return batchQueryInventory(invSkuDTOList);
    }

    InvSkuDTO buildInvSkuDTO(ProductQueryResultDTO product, SkuQueryResultDTO sku) {
        InvSkuDTO invSkuDTO = new InvSkuDTO();
        invSkuDTO.setSkuId(sku.getSkuId());
        invSkuDTO.setItemId(sku.getProductId());
        invSkuDTO.setScItemId(CommodityUtils.getScItemId(sku));
        invSkuDTO.setSellerId(product.getSellerQueryResultDTO().getId());
        return invSkuDTO;
    }

//    @Override
//    public List<InvSourcingSkuResultDTO> sourcing(ProductQueryResultDTO product, String divisionId) {
//        List<SkuQueryResultDTO> skus = product.getSkuList();
//        if (CollectionUtils.isEmpty(skus)) {
//            return new ArrayList<>();
//        }
//
//        InvSourcingRequestDTO request = new InvSourcingRequestDTO();
//        request.setDivisionIdList(Collections.singletonList(divisionId));
//        List<InvSourcingSkuRequestDTO> skuRequestList = skus.stream().map(sku -> {
//            InvSourcingSkuRequestDTO requestDTO = new InvSourcingSkuRequestDTO();
//
//            InvSkuDTO invSkuDTO = buildInvSkuDTO(product, sku);
//            requestDTO.setInvSkuDTO(invSkuDTO);
//
//            return requestDTO;
//        }).collect(Collectors.toList());
//        request.setInvSourcingGroupRequestDTOList(skuRequestList);
//
//        List<InvSourcingSkuResultDTO> resultDTOList = sourcing(Collections.singletonList(request));
//        if (CollectionUtils.isEmpty(resultDTOList)) {
//            return new ArrayList<>();
//        }
//        return resultDTOList;
//    }
//
//    @Override
//    public List<InvSourcingSkuResultDTO> sourcing(List<InvSourcingRequestDTO> requestDTOList) {
//
//        List<InvSourcingSkuResultDTO> result = new ArrayList<>();
//
//        // 文档：https://aliyuque.antfin.com/sirius/sev7ge/ll16dw
//        // 单次查询上限 20
//        List<List<InvSourcingRequestDTO>> requestList = Lists.partition(requestDTOList, 20);
//
//        for (List<InvSourcingRequestDTO> request : requestList) {
//            Response<InvSourcingResultDTO> response = inventorySourcingFacade.sourcing(request);
//            if (response.isSuccess()) {
//                result.addAll(response.getModule().getInvSourcingSkuResultDTOList());
//            } else {
//                throw new BaseException(response.getErrorCode().getKey(), response.getErrorCode().getDisplayMessage());
//            }
//        }
//
//        return result;
//    }

    // 指定调用单元
    void setTargetCluster() {
//        RequestCtxUtil.setTargetCluster("aliyun-region-vpc-ap-southeast-1-pre");
//        LandlordContext.setTenantSpecForNextInvoke(TenantId.ARISE_FR);
//        LandlordHSFUtils.setTenantIdForNextInvoke(TenantId.AE_GLOBAL.name());
//        LandlordUtils.setTenantSpec(TenantId.AE_GLOBAL.name());
    }
}
