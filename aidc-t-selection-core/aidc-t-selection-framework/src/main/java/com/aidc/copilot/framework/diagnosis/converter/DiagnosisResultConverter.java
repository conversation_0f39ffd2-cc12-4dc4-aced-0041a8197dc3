package com.aidc.copilot.framework.diagnosis.converter;

import com.aidc.copilot.dal.diagnosis.dataobject.DiagnosisResultDO;
import com.aidc.copilot.dal.diagnosis.dataobject.DiagnosisResultRecommendDO;
import com.aidc.copilot.framework.diagnosis.dto.DiagnosisResultAttributes;
import com.aidc.copilot.framework.diagnosis.dto.DiagnosisResultDTO;
import com.aidc.copilot.framework.diagnosis.dto.DiagnosisResultRecommendDTO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Mapper
public interface DiagnosisResultConverter {

    DiagnosisResultConverter INSTANCE = Mappers.getMapper(DiagnosisResultConverter.class);

    @Mapping(source = "recommendation", target = "recommendList", qualifiedByName = "jsonToRecommendList")
    @Mapping(source = "attributes", target = "attributes", qualifiedByName = "stringToAttributes")
    DiagnosisResultDTO doToDto(DiagnosisResultDO diagnosisResultDO);

    @Mapping(source = "recommendList", target = "recommendation", qualifiedByName = "recommendListToJson")
    @Mapping(target = "attributes", qualifiedByName = "attributesToString")
    DiagnosisResultDO dtoToDO(DiagnosisResultDTO diagnosisResultDTO);

    DiagnosisResultRecommendDTO recommendDoToDto(DiagnosisResultRecommendDO recommendDO);

    @InheritInverseConfiguration(name = "recommendDoToDto")
    DiagnosisResultRecommendDO recommendDtoToDo(DiagnosisResultRecommendDTO recommendDTO);

    @Named("jsonToRecommendList")
    default List<DiagnosisResultRecommendDTO> jsonToRecommendList(String json) {
        List<DiagnosisResultRecommendDO> list = JSON.parseObject(json, new TypeReference<List<DiagnosisResultRecommendDO>>() {
        });
        return list.stream().map(this::recommendDoToDto).collect(Collectors.toList());
    }

    @Named("recommendListToJson")
    default String recommendListToJson(List<DiagnosisResultRecommendDTO> recommendList) {
        if(recommendList == null){
            return JSON.toJSONString(new ArrayList<>());
        }
        List<DiagnosisResultRecommendDO> list = recommendList.stream().map(this::recommendDtoToDo).collect(Collectors.toList());
        return JSON.toJSONString(list);
    }

    @Named("stringToAttributes")
    default DiagnosisResultAttributes stringToAttributes(String attributes) {
        if (attributes == null) {
            return new DiagnosisResultAttributes(null);
        }
        return JSON.parseObject(attributes, DiagnosisResultAttributes.class);
    }

    @Named("attributesToString")
    default String attributesToString(DiagnosisResultAttributes attributes) {
        return attributes.toString();
    }

    List<DiagnosisResultDTO> doToDtoList(List<DiagnosisResultDO> doList);

    List<DiagnosisResultDO> dtoToDOList(List<DiagnosisResultDTO> dtoList);
}