package com.aidc.copilot.framework.email;

import com.aidc.copilot.framework.siteinbox.MsgRenderUtils;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;
import java.util.Arrays;

/**
 *
 * see https://help.aliyun.com/document_detail/29459.html?spm=a2c4g.29458.0.0.2207436cCNTBfN
 */

@Configuration
@Service
@Slf4j
public class EmailService {


    @Value("${spring.mail.username}")
    private String from;

    @Autowired
    private JavaMailSender sender;

    /**
     *  发送邮件
     * @param recipientEmailAddress  收件人邮箱地址
     * @param subject   邮件主题 标题
     * @param htmlMailBody   html格式的邮件内容
     * @return
     */

    public boolean  sendEmail(String recipientEmailAddress,String subject,String htmlMailBody) {
        log.warn("trace_sendEmail:{},{},{},{}",recipientEmailAddress,subject,htmlMailBody);

        MimeMessage message = sender.createMimeMessage();

        try {
            MimeMessageHelper helper = new MimeMessageHelper(message, false);
            helper.setFrom(from);
            helper.setTo(recipientEmailAddress);

            //helper.setReplyTo(from);

            helper.setSubject(subject);
            helper.setText(htmlMailBody,true);

            sender.send(message);

        } catch (MessagingException e) {
           log.error("error_send_email:{},{}",recipientEmailAddress,subject,e);
           return false;
        }

        return true;
    }

    /**
     * 发送邮件
     * @param recipientEmailAddress 收件人邮箱地址
     * @param subjectTemplate 邮件主题模版
     * @param htmlMailBodyTemplate  html格式的邮件内容模版
     * @param params   模组占位符参数
     * @return
     */
    public boolean   sendEmail(String recipientEmailAddress,String subjectTemplate,String htmlMailBodyTemplate,JSONObject params) {
        log.warn("trace_sendEmail_template:{},{},{},{}",recipientEmailAddress,subjectTemplate,htmlMailBodyTemplate,params);

        final String subject = MsgRenderUtils.render(subjectTemplate, params);

        final String body = MsgRenderUtils.render(htmlMailBodyTemplate, params);

        return sendEmail(recipientEmailAddress,subject,body);
    }





}
