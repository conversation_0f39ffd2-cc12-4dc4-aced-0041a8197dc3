package com.aidc.copilot.framework.diagnosis.dto;

import com.alibaba.copilot.boot.basic.data.Attributes;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 店铺诊断结果Attributes
 */
public class DiagnosisResultAttributes extends Attributes {

    /**
     * 购买数量
     */
    private static final String ATTR_PURCHASE_QTY = "purchaseQty";

    /**
     * AE商品id
     */
    private static final String ATTR_AE_ITEM_ID = "aeItemId";

    /**
     * AE商品id（转换后美国区的商品id）
     */
    private static final String ATTR_AE_MAIN_ITEM_ID = "aeMainItemId";

    /**
     * 失败原因
     */
    private static final String ATTR_FAIL_REASON = "failReason";

    /**
     * 图搜商品id列表（推荐品）
     */
    private static final String ATTR_IMAGE_SEARCH_ITEM_IDS = "imageSearchItemIds";

    /**
     * 同簇商品id列表（推荐品）
     */
    private static final String ATTR_SAME_GROUP_ITEM_IDS = "sameGroupItemIds";

    /**
     * 过滤后的商品id列表（推荐品）
     */
    private static final String ATTR_AFTER_FILTER_ITEM_IDS = "afterFilterItemIds";

    /**
     * 是否在cd时间内未进行诊断
     */
    private static final String ATTR_IN_CD_TIME_TAG = "inCDTimeTag";

    public DiagnosisResultAttributes(String json) {
        super(json);
    }

    public void setPurchaseQty(Long purchaseQty) {
        put(ATTR_PURCHASE_QTY, purchaseQty);
    }

    public Long getPurchaseQty() {
        return getAsLong(ATTR_PURCHASE_QTY);
    }

    public void setFailReason(String failReason) {
        put(ATTR_FAIL_REASON, failReason);
    }

    public String getFailReason() {
        return getAsString(ATTR_FAIL_REASON);
    }

    public void setAeItemId(Long aeItemId) {
        put(ATTR_AE_ITEM_ID, aeItemId);
    }

    public Long getAeItemId() {
        return getAsLong(ATTR_AE_ITEM_ID);
    }

    public void setAeMainItemId(Long aeMainItemId) {
        put(ATTR_AE_MAIN_ITEM_ID, aeMainItemId);
    }

    public Long getAeMainItemId() {
        return getAsLong(ATTR_AE_MAIN_ITEM_ID);
    }

    public void setImageSearchItemIds(List<Long> imageSearchItemIds) {
        String imageSearchItemIdStr = Optional.ofNullable(imageSearchItemIds)
                .map(list -> list.stream()
                        .map(Object::toString)
                        .collect(Collectors.joining(",")))
                .orElse(StringUtils.EMPTY);
        put(ATTR_IMAGE_SEARCH_ITEM_IDS, imageSearchItemIdStr);
    }

    public void setSameGroupItemIds(List<Long> sameGroupItemIds) {
        String sameGroupItemIdStr = Optional.ofNullable(sameGroupItemIds)
                .map(list -> list.stream()
                        .map(Object::toString)
                        .collect(Collectors.joining(",")))
                .orElse(StringUtils.EMPTY);
        put(ATTR_SAME_GROUP_ITEM_IDS, sameGroupItemIdStr);
    }

    public void setAfterFilterItemIds(List<Long> afterFilterItemIds) {
        String afterFilterItemIdStr = Optional.ofNullable(afterFilterItemIds)
                .map(list -> list.stream()
                        .map(Object::toString)
                        .collect(Collectors.joining(",")))
                .orElse(StringUtils.EMPTY);
        put(ATTR_AFTER_FILTER_ITEM_IDS, afterFilterItemIdStr);
    }

    public void setInCDTimeTag() {
        put(ATTR_IN_CD_TIME_TAG, true);
    }
}
