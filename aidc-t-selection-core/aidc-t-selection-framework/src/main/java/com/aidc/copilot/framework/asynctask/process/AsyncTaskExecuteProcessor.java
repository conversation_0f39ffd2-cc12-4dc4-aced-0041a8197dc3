package com.aidc.copilot.framework.asynctask.process;

import com.aidc.copilot.framework.asynctask.model.AsyncTask;
import com.aidc.copilot.framework.asynctask.model.AsyncTaskStatus;
import com.aidc.copilot.framework.asynctask.repository.AsyncTaskRepository;
import com.aidc.copilot.framework.asynctask.worker.AsyncTaskWorkerExecutor;
import com.alibaba.copilot.boot.basic.result.Result;
import com.alibaba.copilot.boot.monitor.annotation.Monitor;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.MapJobProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.taobao.eagleeye.EagleEye;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @desc: 异步任务处理器
 * @author: yixiao.cx
 * @create: 2023-06-26
 **/
@Slf4j
@Component
public class AsyncTaskExecuteProcessor extends MapJobProcessor {
    private static final int PAGE_NUM = 1;
    private static final int PAGE_SIZE = 100;
    private static final String PENDING_TASK = "PENDING_TASK";

    @Resource
    private AsyncTaskRepository asyncTaskRepository;
    @Resource
    private AsyncTaskWorkerExecutor asyncTaskWorkerExecutor;


    @Override
    @Monitor(name = "异步任务调度", level = Monitor.Level.P1, layer = Monitor.Layer.OTHER)
    public ProcessResult process(JobContext context) throws Exception {
        try {
            EagleEye.startTrace(null, this.getClass().getName(), EagleEye.TYPE_CUSTOM_MESSAGE_SUB);
            if (isRootTask(context)) {
                return processRootTask(context);
            }

            if (isPendingTask(context)) {
                return processPendingTask(context);
            }

            return new ProcessResult(false);
        } catch (Throwable e) {
            log.error("process error", e);
            return new ProcessResult(false);
        } finally {
            EagleEye.endTrace(null);
        }
    }

    private ProcessResult processRootTask(JobContext context) {
        List<AsyncTask> executableTasks = asyncTaskRepository.queryExecutableTasks(PAGE_NUM, PAGE_SIZE);
        if(CollectionUtils.isEmpty(executableTasks)) {
            return new ProcessResult(true);
        }

        log.info("processRootTask, executableTasks size={}", executableTasks.size());

        List<AsyncTask> pendingTasks = executableTasks.stream().filter(task -> {
            boolean ret = asyncTaskRepository.updateTaskStatus(task, AsyncTaskStatus.PENDING);
            log.info("update task status to pending, taskId={}, type={} ret={}", task.getId(), task.getType(), ret);
            return ret;
        }).collect(Collectors.toList());

        return map(pendingTasks, PENDING_TASK);
    }

    private ProcessResult processPendingTask(JobContext context) {
        AsyncTask task = (AsyncTask) context.getTask();
        log.info("start to execute pending task, taskId={}, type={}", task.getId(), task.getType());
        try {
            Result result = asyncTaskWorkerExecutor.execute(task);
            log.info("end to execute pending task, taskId={}, type={}, result={}", task.getId(), task.getType(), JSONObject.toJSONString(result));
            return new ProcessResult(result.getSuccess());
        } catch (Exception e) {
            log.error("execute pending task fail, taskId=" + task.getId(), e);
            return new ProcessResult(false);
        }
    }

    private boolean isPendingTask(JobContext context) {
        return PENDING_TASK.equals(context.getTaskName());
    }
}
