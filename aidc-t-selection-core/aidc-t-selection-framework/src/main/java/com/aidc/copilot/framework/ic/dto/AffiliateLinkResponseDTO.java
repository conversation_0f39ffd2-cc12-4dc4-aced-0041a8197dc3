package com.aidc.copilot.framework.ic.dto;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AffiliateLinkResponseDTO {
    private String code;
    private RespResult respResult;
    private String requestId;

    @Data
    public static class RespResult {
        private Result result;
        private String respCode;
        private String respMeg;

        @Data
        public static class Result {
            private String totalResultCount;
            private PromotionLinks promotionLinks;
            private String trackingId;

            @Data
            public static class PromotionLinks {
                private String promotionLink;
                private String sourceValue;
            }
        }
    }
}

