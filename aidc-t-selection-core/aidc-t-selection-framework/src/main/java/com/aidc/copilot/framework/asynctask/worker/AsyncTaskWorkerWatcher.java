package com.aidc.copilot.framework.asynctask.worker;

import com.aidc.copilot.framework.asynctask.model.AsyncTask;
import com.aidc.copilot.framework.asynctask.repository.AsyncTaskRepository;
import com.alibaba.copilot.boot.monitor.annotation.Monitor;
import com.alibaba.fastjson.JSONObject;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * @desc: 异步任务看守者
 * @author: yixiao.cx
 * @create: 2023-06-26
 **/
@Slf4j
@Component
public class AsyncTaskWorkerWatcher {
    /**
     * 心跳间隔(秒)
     */
    private static final int HEARTBEAT_INTERVAL = 5;
    /**
     * 守护线程
     */
    private static final ScheduledExecutorService DAEMON_EXECUTOR_SERVICE = new ScheduledThreadPoolExecutor(
        1,
        new ThreadFactoryBuilder().setNameFormat("AsyncTaskWorkerWatcher").build(),
        new ThreadPoolExecutor.AbortPolicy()
    );

    @Resource
    private AsyncTaskRepository asyncTaskRepository;

    /**
     * 看守的任务
     */
    private final Set<AsyncTask> watchTasks = new CopyOnWriteArraySet<>();

    /**
     * 启动看守线程
     */
    @PostConstruct
    public void startWatchDaemon() {
        log.info("AsyncTaskWorkerWatcher start...");

        DAEMON_EXECUTOR_SERVICE.scheduleAtFixedRate(() -> {
                try {
                    this.keepHeartbeat();
                } catch (Throwable throwable) {
                    log.error("keep heartbeat error", throwable);
                }
            },
            HEARTBEAT_INTERVAL,
            HEARTBEAT_INTERVAL,
            TimeUnit.SECONDS
        );
    }

    /**
     * 添加守护的任务
     * @param task
     */
    public void addWatchTask(AsyncTask task) {
        watchTasks.add(task);
        log.info("add watch task: {}", task.getId());
    }

    /**
     * 删除守护的任务
     * @param taskIds
     */
    public void delWatchTasks(List<Long> taskIds) {
        if(CollectionUtils.isEmpty(taskIds)) {
            return;
        }

        List<AsyncTask> delTask = watchTasks.stream()
            .filter(task -> taskIds.contains(task.getId()))
            .collect(Collectors.toList());
        delTask.forEach(watchTasks::remove);
        log.info("delete watch tasks: {}", taskIds);
    }

    @Monitor(name = "异步任务心跳", level = Monitor.Level.P1, layer = Monitor.Layer.OTHER)
    private void keepHeartbeat() {
        List<Long> delTaskIds = new ArrayList<>();
        watchTasks.forEach(task -> {
            boolean success = asyncTaskRepository.updateTaskHeartbeatDate(task.getId());
            if(!success) {
                delTaskIds.add(task.getId());
            }
            log.info("keep task heartbeat, taskId={}, success={}", task.getId(), success);
        });
        delWatchTasks(delTaskIds);
    }

}
