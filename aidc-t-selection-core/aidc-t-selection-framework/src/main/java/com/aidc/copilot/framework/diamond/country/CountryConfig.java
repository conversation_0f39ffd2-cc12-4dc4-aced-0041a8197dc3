package com.aidc.copilot.framework.diamond.country;

import com.aidc.copilot.framework.diamond.AbstractConfigCenter;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/4/28
 */
@Slf4j
@Component
public class CountryConfig extends AbstractConfigCenter<CountryConfigData> {
    protected final String clazzSimpleName = getClass().getSimpleName();
    private static final String DATA_ID = "com.aidc.copilot.country";

    private Map<String, String> name2CodeMap = new HashMap<>();

    private Map<String, String> code2NameMap = new HashMap<>();

    @Override
    protected String getDataId() {
        return DATA_ID;
    }

    @Override
    protected void compile(String dataStr) {
        //log.info("{}#receiveConfigInfo receive configInfo, configInfo={}", clazzSimpleName, dataStr);
        try {
            data = JSON.parseObject(dataStr, CountryConfigData.class);
        } catch (Exception e) {
            log.error("CountryConfig#receiveConfigInfo error", e);
        }
        this.initName2CodeMap();
        this.initCode2NameMap();
    }

    private void initName2CodeMap() {
        log.info("CountryConfig.initName2CodeMap, countryConfigData={}", JSON.toJSONString(this.getData()));
        CountryConfigData countryConfigData = this.getData();
        if (countryConfigData != null) {
            List<CountryConfigData.ConfigData> configDataList = countryConfigData.getCountryList();
            if (CollectionUtils.isNotEmpty(configDataList)) {
                name2CodeMap.clear();
                for (CountryConfigData.ConfigData configData : configDataList) {
                    name2CodeMap.put(configData.getText(), configData.getCode());
                }
            }
        }
    }

    private void initCode2NameMap() {
        log.info("CountryConfig.initCode2NameMap, countryConfigData={}", JSON.toJSONString(this.getData()));
        CountryConfigData countryConfigData = this.getData();
        if (countryConfigData != null) {
            List<CountryConfigData.ConfigData> configDataList = countryConfigData.getCountryList();
            if (CollectionUtils.isNotEmpty(configDataList)) {
                code2NameMap.clear();
                for (CountryConfigData.ConfigData configData : configDataList) {
                    code2NameMap.put(configData.getCode(), configData.getText());
                }
            }
        }
    }

    public String getCountryCode(String countryName) {
        if (MapUtils.isNotEmpty(name2CodeMap)) {
            log.info("CountryConfig.getCountryCode, if, name2CodeMap={}", JSON.toJSONString(name2CodeMap));
            return name2CodeMap.get(countryName);
        } else {
            CountryConfigData countryConfigData = this.getData();
            if (countryConfigData != null) {
                List<CountryConfigData.ConfigData> configDataList = countryConfigData.getCountryList();
                if (CollectionUtils.isNotEmpty(configDataList)) {
                    for (CountryConfigData.ConfigData configData : configDataList) {
                        name2CodeMap.put(configData.getText(), configData.getCode());
                    }
                }

                log.info("CountryConfig.getCountryCode, else, name2CodeMap={}", JSON.toJSONString(name2CodeMap));
                return name2CodeMap.get(countryName);
            } else {
                return null;
            }
        }
    }

    public String getCountryName(String countryCode) {
        if (MapUtils.isNotEmpty(code2NameMap)) {
            log.info("CountryConfig.getCountryName, if, code2NameMap={}", JSON.toJSONString(code2NameMap));
            return code2NameMap.get(countryCode);
        } else {
            CountryConfigData countryConfigData = this.getData();
            if (countryConfigData != null) {
                List<CountryConfigData.ConfigData> configDataList = countryConfigData.getCountryList();
                if (CollectionUtils.isNotEmpty(configDataList)) {
                    for (CountryConfigData.ConfigData configData : configDataList) {
                        code2NameMap.put(configData.getCode(), configData.getText());
                    }
                }

                log.info("CountryConfig.getCountryName, else, code2NameMap={}", JSON.toJSONString(code2NameMap));
                return code2NameMap.get(countryCode);
            } else {
                return null;
            }
        }
    }
}
