package com.aidc.copilot.framework.chain.ws;

import lombok.Getter;

import java.util.List;

/**
 * @desc:
 * @author: yixiao.cx
 * @create: 2023-06-08
 **/
public class WsSelectionContext {
    @Getter
    private WsSelectionOutput output = new WsSelectionOutput();

    private int recommendSummaryPollIndex = 0;
    private int intentionPollIndex = 0;
    private int optionPollIndex = 0;
    private int searchKeyPollIndex = 0;

    public String pollRecommendSummary() {
        if(output.getRecommendSummary() == null
            || recommendSummaryPollIndex >= output.getRecommendSummary().length()) {
            return null;
        }

        int start = recommendSummaryPollIndex;
        recommendSummaryPollIndex = output.getRecommendSummary().length();
        return output.getRecommendSummary().substring(start, recommendSummaryPollIndex);
    }

    public String pollIntention() {
        if(intentionPollIndex > 0 || output.getIntention() == null) {
            return null;
        }

        intentionPollIndex += 1;
        return output.getIntention();
    }

    public String pollOption() {
        if(output.getOptions().size() <= optionPollIndex) {
            return null;
        }
        return output.getOptions().get(optionPollIndex ++);
    }

    public List<String> pollOptions() {
        if(output.getOptions().size() <= optionPollIndex) {
            return null;
        }
        optionPollIndex = output.getOptions().size();
        return output.getOptions();
    }

    public WsSelectionOutput.SearchKey pollSearchKey() {
        if(output.getSearchKeys().size() <= searchKeyPollIndex) {
            return null;
        }
        return output.getSearchKeys().get(searchKeyPollIndex ++);
    }
}
