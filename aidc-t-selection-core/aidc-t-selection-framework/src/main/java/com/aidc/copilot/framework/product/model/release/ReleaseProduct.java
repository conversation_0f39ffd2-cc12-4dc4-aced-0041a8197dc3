package com.aidc.copilot.framework.product.model.release;

import com.aidc.copilot.framework.product.model.ProductImage;
import com.alibaba.copilot.boot.basic.data.BaseObject;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @desc:
 * @author: yixiao.cx
 * @create: 2023-07-07
 **/
@Setter
@Getter
public class ReleaseProduct extends BaseObject {
    /**
     * ID
     */
    private Long id;
    /**
     * 创建时间
     */
    private Date gmtCreate;
    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 商家ID
     */
    private Long merchantId;
    /**
     * 商家店铺
     */
    private String shopDomain;

    /**
     * 渠道（Import AE 等平台）商品 ID
     */
    private Long channelProductId;

    /**
     * 来源（AE 等平台）商品 ID
     */
    private Long sourceProductId;
    /**
     * 导入来源
     */
    private String sourcePlatform;
    /**
     * 发布到目标平台的商品 ID
     */
    private Long publishProductId;
    /**
     * 发布平台
     */
    private String publishPlatform;

    /**
     * 标题
     */
    private String title;
    /**
     * 规格
     */
    private String specification;
    /**
     * 描述
     */
    private String descriptionUrl;
    /**
     * 描述文本
     */
    private String descriptionTxt;
    /**
     * 图片集合
     */
    private List<ProductImage> images;

    /**
     * 删除标记
     */
    private Boolean deleted;

    /**
     * 发布状态
     */
    private ReleaseProductStatus status;

    /**
     * 商品SKU
     */
    List<ReleaseSku> skus = new ArrayList<>();

    /**
     * 属性（JSON格式）
     */
    private ReleaseProductAttributes attributes = new ReleaseProductAttributes(null);
}
