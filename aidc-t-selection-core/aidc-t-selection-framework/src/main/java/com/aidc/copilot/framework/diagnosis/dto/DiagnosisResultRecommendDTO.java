package com.aidc.copilot.framework.diagnosis.dto;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class DiagnosisResultRecommendDTO {

    /**
     * 商品id
     */
    private Long itemId;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 销售价格
     */
    private Long sellPrice;

    /**
     * 购买成本
     */
    private Long purchaseCost;

    /**
     * 物流成本
     */
    private Long logisticsCost;

    /**
     * 店铺开店时长
     */
    private Integer stability;

    /**
     * 收益
     */
    private Long profit;

    /**
     * 近6月订单数
     */
    private Long ordCnt6m;

    /**
     * 商品主图
     */
    private String mainImageUrl;
}
