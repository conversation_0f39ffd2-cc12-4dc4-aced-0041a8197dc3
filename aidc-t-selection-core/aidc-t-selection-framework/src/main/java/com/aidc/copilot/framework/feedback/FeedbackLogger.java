package com.aidc.copilot.framework.feedback;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class FeedbackLogger {

    private static final Logger feedbackLogger = LoggerFactory.getLogger("feedback-log");

    private static final Logger feedbackWeeklyReportDataLogger = LoggerFactory.getLogger("feedback-weekly-report-log");

    /**
     * @param scene  日志场景，比如ai选品场景
     * @param action 当前操作
     * @param userId 当前操作用户id
     * @param input  输入
     * @param output 输出
     */
    public static void logInfo(String scene, Long userId, String action, JSONObject input, JSONObject output) {
        feedbackLogger.info(JSON.toJSONString(new FeedbackData(scene, userId, action, input, output)));
    }

    /**
     * 周报日志回流通用方法
     *
     * @param trace
     */
    public static void logWeeklyReportDataInfo(String generateScene, String userId, String trace, String feedbackType, String feedbackModel, String requestPrompt,String generateResult) {
        feedbackWeeklyReportDataLogger.info(JSON.toJSONString(new WeeklyReportData(trace, userId, generateScene, feedbackType, feedbackModel,requestPrompt, generateResult)));
    }

}
