package com.aidc.copilot.framework.openai.response;

import java.io.Serializable;
import java.util.List;

import com.aidc.copilot.framework.openai.model.ChatChoice;
import com.aidc.copilot.framework.openai.model.Usage;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2023/5/30 21:19
 */
@Data
public class ChatCompletionResponse implements Serializable {
    private String id;
    private String object;
    private long created;
    private String model;
    private List<ChatChoice> choices;
    private Usage usage;
}
