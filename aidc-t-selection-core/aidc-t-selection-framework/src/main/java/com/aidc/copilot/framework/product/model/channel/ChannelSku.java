package com.aidc.copilot.framework.product.model.channel;

import com.alibaba.copilot.boot.basic.data.BaseObject;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @desc:
 * @author: yixiao.cx
 * @create: 2023-07-07
 **/
@Setter
@Getter
public class ChannelSku extends BaseObject {
    /**
     * ID
     */
    private Long id;
    /**
     * 创建时间
     */
    private Date gmtCreate;
    /**
     * 修改时间
     */
    private Date gmtModified;
    /**
     * 商家ID
     */
    private Long merchantId;

    /**
     * 渠道商品ID
     */
    private Long channelProductId;
    /**
     * 来源SKU ID
     */
    private Long sourceSkuId;
    /**
     * 成本
     */
    private BigDecimal originPrice;
    /**
     * 是否已发布
     */
    private Boolean released;
    /**
     * 删除标记
     */
    private Boolean deleted;
    /**
     * 属性（JSON格式）
     */
    private ChannelSkuAttributes attributes = new ChannelSkuAttributes(null);
}
