package com.aidc.copilot.framework.annotation;

import com.aidc.copilot.framework.enums.SyncPointEnum;
import com.aidc.copilot.framework.sync.AbstractSynchronizer;
import com.aidc.copilot.framework.sync.SyncRule;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @description 同步器注解
 * @email <EMAIL>
 * @date 2023/5/4
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface EnableSynchronized {
    /**
     * 同步器
     *
     * @return
     */
    Class<? extends AbstractSynchronizer> synchronizer();

    /**
     * 规则列表
     *
     * @return
     */
    Class<? extends SyncRule<?>>[] rules();

    /**
     * 同步点
     *
     * @return
     */
    SyncPointEnum syncPoint();

    /**
     * 是否使用拷贝入参
     *
     * @return
     */
    boolean copyInput() default true;
}
