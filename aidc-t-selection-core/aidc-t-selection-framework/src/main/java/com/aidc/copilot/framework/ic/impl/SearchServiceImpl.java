package com.aidc.copilot.framework.ic.impl;

import com.aidc.copilot.framework.ic.SearchService;
import com.aidc.copilot.framework.ic.dto.MainSiteSearchRes;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.net.URI;

/**
 * @Author: mianyun.yt
 * @Date: 2023/5/4
 */
@Service
@Slf4j
public class SearchServiceImpl implements SearchService {

    @Value("${ae.search.url}")
    private String aeSearchUrl;

    @Override
    public MainSiteSearchRes mainSiteSearch(String keywords, String userMemberSeq, String lang, String currency, String shipFromCountry, String shipToCountry, String state, String city, String pr, String bids, String attr, Long pageNo, Long pageSize) {

        try {
            HttpClient httpClient = HttpClientBuilder.create().build();

            URIBuilder uriBuilder = new URIBuilder(aeSearchUrl);
            uriBuilder.addParameter("_input_charset", "UTF-8");
            uriBuilder.addParameter("appVersion", "346");
            uriBuilder.addParameter("osf", "index");
            uriBuilder.addParameter("pageSize", pageSize.toString());
            uriBuilder.addParameter("deviceId", "YcFM6towGHIDAJv2BwwmELWe");
            uriBuilder.addParameter("clientType", "iphone");
            uriBuilder.addParameter("refine_conf", "0");
            uriBuilder.addParameter("shpf_co", shipFromCountry);
            uriBuilder.addParameter("shpt_co", shipToCountry);
            uriBuilder.addParameter("searchBizScene", "mainSearch");
            uriBuilder.addParameter("sversion", "3.8");
            uriBuilder.addParameter(" _state", state);
            uriBuilder.addParameter("_city", city);
            uriBuilder.addParameter("utdid", "YcFM6towGHIDAJv2BwwmELWe");
            uriBuilder.addParameter("page", pageNo.toString());
            uriBuilder.addParameter("_currency", currency);
            uriBuilder.addParameter("clientAppVersion", "346");
            uriBuilder.addParameter("device_model", "iPhone14%2C5");
            uriBuilder.addParameter("locale", "en_US");
            uriBuilder.addParameter("utd_id", "YcFM6towGHIDAJv2BwwmELWe");
            uriBuilder.addParameter("lang", lang);
            uriBuilder.addParameter("x-app-ver", "8.66.1.8940");
            uriBuilder.addParameter("os_version", "16.1.2");
            uriBuilder.addParameter("srp", "mainSearchSrp");
            uriBuilder.addParameter("q", keywords);
            uriBuilder.addParameter("appid", "999");
            uriBuilder.addParameter("userMemberSeq", userMemberSeq);
            uriBuilder.addParameter("bids", bids);
            uriBuilder.addParameter("attr", attr);
            uriBuilder.addParameter("pr", pr);

            URI uri = uriBuilder.build();

            HttpGet httpGet = new HttpGet(uri);

            HttpResponse httpResponse = httpClient.execute(httpGet);
            int responseCode = httpResponse.getStatusLine().getStatusCode();

            if (responseCode == 200) {
                String jsonResponse = EntityUtils.toString(httpResponse.getEntity(), "UTF-8");
                ObjectMapper objectMapper = new ObjectMapper();
                return objectMapper.readValue(jsonResponse, MainSiteSearchRes.class);
            } else {
                log.error("主搜请求失败，返回码：{}", responseCode);
            }
        } catch (Exception e) {

            log.error("主搜请求失败", e);
        }
        return null;
    }
}
