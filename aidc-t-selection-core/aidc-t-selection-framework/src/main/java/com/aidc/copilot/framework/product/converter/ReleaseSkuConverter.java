package com.aidc.copilot.framework.product.converter;

import com.aidc.copilot.dal.product.dataobject.ReleaseSkuDO;
import com.aidc.copilot.framework.product.model.release.ReleaseSku;
import com.aidc.copilot.framework.product.model.release.ReleaseSkuAttributes;
import com.alibaba.copilot.boot.basic.factory.Converter;
import org.springframework.beans.BeanUtils;

/**
 * @desc:
 * @author: yixiao.cx
 * @create: 2023-07-07
 **/
public class ReleaseSkuConverter implements Converter<ReleaseSkuDO, ReleaseSku> {
    public static final Converter<ReleaseSkuDO, ReleaseSku> INSTANCE = new ReleaseSkuConverter();

    private static final String[] ignoreProperties = new String[] {"attributes"};

    @Override
    public ReleaseSku convertA2B(ReleaseSkuDO releaseSkuDO) {
        if (releaseSkuDO == null) {
            return null;
        }
        ReleaseSku sku = new ReleaseSku();
        BeanUtils.copyProperties(releaseSkuDO, sku, ignoreProperties);
        sku.setAttributes(new ReleaseSkuAttributes(releaseSkuDO.getAttributes()));
        return sku;
    }

    @Override
    public ReleaseSkuDO convertB2A(ReleaseSku releaseSku) {
        if (releaseSku == null) {
            return null;
        }
        ReleaseSkuDO skuDO = new ReleaseSkuDO();
        BeanUtils.copyProperties(releaseSku, skuDO, ignoreProperties);
        skuDO.setAttributes(releaseSku.getAttributes().toString());
        return skuDO;
    }
}
