package com.aidc.copilot.framework.ic.dto;

import com.aidc.copilot.framework.shopify.product.dto.Option;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.LinkedList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MainSiteSearchRes4Gpt {
    /**
     * 商品标题
     */
    private String title;
    /**
     * 商品链接
     */
    private String link;
    /**
     * 商品价格
     */
    private String price;
    /**
     * 商品主图
     */
    private String mainImage;
    /**
     * 商品卖点
     */
    private List<String> sellingPoints;
    /**
     * 商品sku
     */
    private List<Option> skuOptions = new LinkedList<>();
    /**
     * 商品id
     */
    private Long productId;
}
