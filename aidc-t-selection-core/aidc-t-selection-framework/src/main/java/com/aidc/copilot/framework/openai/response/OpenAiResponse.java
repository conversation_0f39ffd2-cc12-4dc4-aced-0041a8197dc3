package com.aidc.copilot.framework.openai.response;

import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2023/5/30 21:19
 */
@Data
public class OpenAiResponse<T> implements Serializable {
    private String object;
    private List<T> data;
    private Error error;


    @Data
    public class Error {
        private String message;
        private String type;
        private String param;
        private String code;
    }
}
