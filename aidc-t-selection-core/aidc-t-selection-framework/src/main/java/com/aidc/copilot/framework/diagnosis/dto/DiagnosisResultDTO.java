package com.aidc.copilot.framework.diagnosis.dto;

import com.google.common.collect.Lists;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class DiagnosisResultDTO {

    private Long id;

    private Date gmtCreate;

    private Date gmtModified;

    private String shopDomain;

    private Integer batch = 1;

    private String merchantId;

    /**
     * Shopify商品id
     */
    private Long itemId;

    private String imageUrl;

    private String title;

    private Long sellPrice;

    private Long purchaseCost;

    private Long logisticsCost;

    /**
     * 收益
     */
    private Long profit;

    private List<DiagnosisResultRecommendDTO> recommendList = Lists.newArrayList();

    private String source;

    private boolean optimized;

    /**
     * 第一个推荐品的收益
     */
    private Long firstRecommendProfit;

    private boolean deleted;

    private DiagnosisResultAttributes attributes = new DiagnosisResultAttributes(null);

    public Long getProfit() {
        if (this.sellPrice == null || this.purchaseCost == null || this.logisticsCost == null) {
            return null;
        }

        this.profit = this.sellPrice - this.purchaseCost - this.logisticsCost;
        return this.profit;
    }
}
