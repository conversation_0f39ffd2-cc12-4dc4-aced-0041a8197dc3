package com.aidc.copilot.framework.openai.service.impl;

import com.aidc.copilot.framework.exception.BaseException;
import com.aidc.copilot.framework.openai.config.GenerateWeeklyNewspaperConfig;
import com.aidc.copilot.framework.openai.config.ParagraphTemplateConfig;
import com.aidc.copilot.framework.openai.config.RecommendPromptConfig;
import com.aidc.copilot.framework.openai.config.TemplateCommodityTitleConfig;
import com.aidc.copilot.framework.openai.config.TemplateDescConfig;
import com.aidc.copilot.framework.openai.config.TemplateSearchKeyWordConfig;
import com.aidc.copilot.framework.openai.config.TemplateSubHeadConfig;
import com.aidc.copilot.framework.openai.config.TemplateWeeklyNewspaperConfig;
import com.aidc.copilot.framework.openai.dto.RecommendPromptKey;
import com.aidc.copilot.framework.openai.service.OpenAiBusinessService;
import com.aidc.copilot.framework.openai.util.WeeklyNewspaperModelEnum;
import com.aidc.copilot.framework.utils.CollectionUtils;

import com.alibaba.copilot.boot.llm.openai.client.OpenAiBaseClient;
import com.alibaba.copilot.boot.llm.openai.constant.Models;
import com.alibaba.copilot.boot.llm.openai.constant.Models.GTP4;
import com.alibaba.copilot.boot.llm.openai.data.completion.CompletionChoice;
import com.alibaba.copilot.boot.llm.openai.data.completion.chat.ChatCompletionChoice;
import com.alibaba.copilot.boot.llm.openai.data.completion.chat.ChatCompletionRequest;
import com.alibaba.copilot.boot.llm.openai.data.completion.chat.ChatMessage;
import com.alibaba.copilot.boot.llm.openai.data.completion.chat.ChatMessageRole;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import freemarker.template.Configuration;
import freemarker.template.Template;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.nodes.Node;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.io.IOException;
import java.io.StringWriter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @ClassName OpenAiService
 * <AUTHOR>
 * @Date 2023/6/10 14:07
 */
@Slf4j
@Service
public class OpenAiBusinessServiceImpl implements OpenAiBusinessService {

    @Resource
    private TemplateCommodityTitleConfig templateCommodityTitleConfig;
    @Resource
    private TemplateSubHeadConfig templateSubHeadConfig;
    @Resource
    private TemplateDescConfig templateDescConfig;
    @Resource
    private TemplateSearchKeyWordConfig templateSearchKeyWordConfig;
    @Resource
    private RecommendPromptConfig recommendPromptConfig;
    @Resource
    private TemplateWeeklyNewspaperConfig templateWeeklyNewspaperConfig;
    @Resource
    private ParagraphTemplateConfig paragraphTemplateConfig;
    @Resource
    private OpenAiBaseClient openAiBaseClient;
    @Resource
    private GenerateWeeklyNewspaperConfig generateWeeklyNewspaperConfig;

    @Override
    public List<String> autoGenerateTitleReason(String oldTitle, List<String> keyWords, String modalParticle,
        Long minWords) {
        List<String> titleList = new ArrayList<>();
        ChatMessage systemMessage = new ChatMessage(ChatMessageRole.USER.value(),
            generateGenerateTitlePrompt(oldTitle, keyWords, modalParticle, minWords));
        ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest
            .builder()
            .model(Models.GTP3.GPT_3_5_TURBO.getId())
            .messages(Collections.singletonList(systemMessage))
            .temperature(1.0)
            .build();
        List<ChatCompletionChoice> choices = openAiBaseClient.createChatCompletion(chatCompletionRequest).getChoices();
        if (!CollectionUtils.isEmpty(choices)) {
            log.info("autoGenerateTitleReason choices :{}", JSON.toJSONString(choices));
            String firstNonEmptyContent = choices.stream()
                .map(completionChoice -> completionChoice.getMessage().getContent())
                .filter(Objects::nonNull)
                .filter(content -> !content.isEmpty())
                .findFirst()
                .orElse("");
            log.info("autoGenerateTitleReason firstNonEmptyContent :{}", firstNonEmptyContent);
            String[] parts = firstNonEmptyContent.split("\n");
            for (String part : parts) {
                titleList.add(part.trim().replaceAll("\\*", ""));
            }
        }
        return titleList;
    }

    @Override
    public List<String> autoGenerateSubHead(String oldDesc) {
        List<String> subHeadList = Lists.newArrayList();

        ChatMessage systemMessage = new ChatMessage(ChatMessageRole.USER.value(),
            generateGenerateSubHeadPrompt(oldDesc));
        ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest
            .builder()
            .model(Models.GTP3.GPT_3_5_TURBO.getId())
            .messages(Collections.singletonList(systemMessage))
            .temperature(0.1)
            .build();
        List<ChatCompletionChoice> choices = openAiBaseClient.createChatCompletion(chatCompletionRequest).getChoices();
        if (!CollectionUtils.isEmpty(choices)) {
            log.info("autoGenerateSubHead choices :{}", JSON.toJSONString(choices));
            String firstNonEmptyContent = choices.stream()
                .map(completionChoice -> completionChoice.getMessage().getContent())
                .filter(Objects::nonNull)
                .filter(content -> !content.isEmpty())
                .findFirst()
                .orElse("");
            String[] parts = firstNonEmptyContent.split("\\|");
            subHeadList.addAll(Arrays.asList(parts));
        }
        return subHeadList;
    }

    private static void removeAttributes(Element element, String... attrs) {
        for (String attr : attrs) {
            if (!element.tagName().equals("img") || (!attr.equals("src") && !attr.equals("alt"))) {
                element.removeAttr(attr);
            }
        }
    }

    private static void removeEmptyTags(Element element) {
        Elements children = element.children();
        for (Element child : children) {
            removeEmptyTags(child);
        }
        if (element.isBlock() && element.text().trim().isEmpty() && !element.hasAttr("src") && element.select("img")
            .isEmpty()) {
            element.remove();
        }
    }

    private static void mergeSpanStyles(Element element) {
        Elements spans = element.select("span");
        for (Element span : spans) {
            for (Node childNode : span.childNodes()) {
                span.before(childNode.clone());
            }
            span.remove();
        }
    }

    private static void keepColorAndFormatting(Element element) {
        Elements elementsWithStyle = element.select("[style]");
        for (Element elem : elementsWithStyle) {
            String style = elem.attr("style");
            String[] styles = style.split(";");
            StringBuilder newStyle = new StringBuilder();
            for (String s : styles) {
                if (s.contains("color") || s.contains("font-weight") || s.contains("font-style") || s.contains(
                    "text-decoration")) {
                    newStyle.append(s).append(";");
                }
            }
            elem.attr("style", newStyle.toString());
        }
    }

    private static void removeUnwantedTags(Element element) {
        Elements elementsToRemove = element.select("strong");
        for (Element elem : elementsToRemove) {
            for (Node childNode : elem.childNodes()) {
                elem.before(childNode.clone());
            }
            elem.remove();
        }
    }

    private static String compressHtml(String html) {
        return html.replaceAll(">\\s+<", "><").trim();
    }

    public static String optimizeHtml(String html) {
        Document document = Jsoup.parse(html);
        // 移除不需要的属性
        for (Element element : document.getAllElements()) {
            removeAttributes(element, "class", "id", "href");
        }
        // 移除空白标签
        removeEmptyTags(document.body());
        // 合并<span>标签的样式到父元素中
        mergeSpanStyles(document.body());
        // 保留内联样式中的文本颜色和部分格式
        keepColorAndFormatting(document.body());
        // 移除不需要的标签
        removeUnwantedTags(document.body());
        String optimizedHtml = document.html();
        return compressHtml(optimizedHtml);
    }

    @Override
    public String autoGenerateDesc(String title, String oldDesc, List<String> keyWords, String modalParticle,
        List<String> subHead)
        throws IOException {
        Document doc = Jsoup.parse(optimizeHtml(oldDesc));
        doc.outputSettings(new Document.OutputSettings().prettyPrint(false));

        Map<String, String> urlToNumberMap = new HashMap<>();
        Map<String, String> numberToUrlMap = new HashMap<>();

        int number = 1;
        for (Element img : doc.select("img")) {
            String src = img.attr("src");
            String replacement = "URL_" + number;
            urlToNumberMap.put(src, replacement);
            numberToUrlMap.put(replacement, src);
            img.attr("src", replacement);
            number++;
        }

        doc.select("div").unwrap();

        String descWithNumbers = doc.html().replaceAll("\\n", "");

        OkHttpClient client = new OkHttpClient.Builder()
            .connectTimeout(120, TimeUnit.SECONDS)
            .readTimeout(120, TimeUnit.SECONDS)
            .writeTimeout(120, TimeUnit.SECONDS)
            .build();

        MediaType mediaType = MediaType.parse("application/json");
        Map<String, Object> parameter = new HashMap<>();
        parameter.put("model", "text-davinci-003");
        parameter.put("prompt", generateGenerateDescPrompt(title, descWithNumbers, keyWords, modalParticle, subHead));
        parameter.put("max_tokens", 1400);
        parameter.put("temperature", 0.7);

        RequestBody body = RequestBody.create(mediaType, com.alibaba.fastjson.JSON.toJSONString(parameter));

        Request request = new Request.Builder()
            .url("http://8.222.145.211:80/v1/completions")
            .post(body)
            .addHeader("Content-Type", "application/json")
            .addHeader("Authorization", "Bearer ***************************************************")
            .build();

        Response response = client.newCall(request).execute();
        String responseBody = response.body().string();

        JSONObject jsonResponse = com.alibaba.fastjson.JSON.parseObject(responseBody);
        JSONArray choiceArray = jsonResponse.getJSONArray("choices");
        List<CompletionChoice> choices = choiceArray.toJavaList(CompletionChoice.class);

        String newDesc = choices.stream()
            .map(CompletionChoice::getText)
            .filter(Objects::nonNull)
            .filter(content -> !content.isEmpty())
            .collect(Collectors.joining(""));

        for (Map.Entry<String, String> entry : numberToUrlMap.entrySet()) {
            String replacement = entry.getKey();
            String originalUrl = entry.getValue();
            newDesc = newDesc.replaceAll("src=\"" + Pattern.quote(replacement) + "\"",
                "src=\"" + Matcher.quoteReplacement(originalUrl) + "\"");
        }

        return newDesc;
    }

    @Override
    public String autoExtractionSearchKeyword(String title) {
        String searchKey = "";
        ChatMessage systemMessage = new ChatMessage(ChatMessageRole.USER.value(), generateSearchKeyWordPrompt(title));
        ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest
            .builder()
            .model(Models.GTP3.GPT_3_5_TURBO.getId())
            .messages(Collections.singletonList(systemMessage))
            .temperature(0.3)
            .build();
        List<ChatCompletionChoice> choices = openAiBaseClient.createChatCompletion(chatCompletionRequest).getChoices();
        if (!CollectionUtils.isEmpty(choices)) {
            log.info("autoExtractionSearchKeyword choices :{}", JSON.toJSONString(choices));
            searchKey = choices.stream()
                .map(completionChoice -> completionChoice.getMessage().getContent())
                .filter(Objects::nonNull)
                .filter(content -> !content.isEmpty())
                .collect(Collectors.joining(""));
            // 使用空格作为分隔符拼接所有非空选项
        }
        return searchKey;
    }

    @Override
    public List<RecommendPromptKey> autoRecommendPromptKeyDto(String prompt) {
        List<RecommendPromptKey> recommendPromptKeyDtoList = Lists.newArrayList();
        String searchKey = "";
        ChatMessage systemMessage = new ChatMessage(ChatMessageRole.USER.value(), generateRecommendPrompt(prompt));
        ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest
            .builder()
            .model(GTP4.GPT_4.getId())
            .messages(Collections.singletonList(systemMessage))
            .temperature(1.0)
            .build();
        List<ChatCompletionChoice> choices = openAiBaseClient.createChatCompletion(chatCompletionRequest).getChoices();
        if (!CollectionUtils.isEmpty(choices)) {
            log.info("autoExtractionSearchKeyword choices :{}", JSON.toJSONString(choices));
            searchKey = choices.stream()
                .map(completionChoice -> completionChoice.getMessage().getContent())
                .filter(Objects::nonNull)
                .filter(content -> !content.isEmpty())
                .collect(Collectors.joining(" "));
            // 使用空格作为分隔符拼接所有非空选项
        }
        recommendPromptKeyDtoList = extractRecommendPromptKeyDTO(searchKey);
        log.info("autoRecommendPromptKeyDto :{}", JSON.toJSONString(recommendPromptKeyDtoList));
        return recommendPromptKeyDtoList;
    }

    @Override
    public String generateWeeklyNewspaper(String weeklyNewspaperDetail, String promptTemplate, String generateModel)
        throws Exception {
        String searchKey = "";
        String weeklyNewspaperPrompt = "";
        log.info("weeklyNewspaperDetail->{}",JSON.toJSONString(weeklyNewspaperDetail));
        if (StringUtils.isBlank(weeklyNewspaperDetail) ||"[]".equals(weeklyNewspaperDetail)){
            log.info("generateWeeklyNewspaper generateWeeklyNewspaper->{}",promptTemplate);
           weeklyNewspaperPrompt = generateWeeklyNewspaper(promptTemplate);
        }else {
            weeklyNewspaperPrompt = generateWeeklyNewspaperPrompt(weeklyNewspaperDetail, promptTemplate);
        }
        if (generateModel.equals(WeeklyNewspaperModelEnum.GPT4.getCode())) {
            ChatMessage systemMessage = new ChatMessage(ChatMessageRole.USER.value(),
                weeklyNewspaperPrompt);
            ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest
                .builder()
                .model(GTP4.GPT_4_O.getId())
                .messages(Collections.singletonList(systemMessage))
                .temperature(0.8)
                .build();
            List<ChatCompletionChoice> choices = openAiBaseClient.createChatCompletion(chatCompletionRequest)
                .getChoices();
            if (!CollectionUtils.isEmpty(choices)) {
                log.info("generateWeeklyNewspaper choices :{}", JSON.toJSONString(choices));
                searchKey = choices.stream()
                    .map(completionChoice -> completionChoice.getMessage().getContent())
                    .filter(Objects::nonNull)
                    .filter(content -> !content.isEmpty())
                    .collect(Collectors.joining(""));
                // 使用空格作为分隔符拼接所有非空选项
            }
        } else if (generateModel.equals(WeeklyNewspaperModelEnum.Qwen.getCode())) {
            searchKey = sendPostRequest(WeeklyNewspaperModelEnum.Qwen.getUrl(), "<|im_start|>system\\nYou are a "
                + "helpful assistant.<|im_end|>\\n<|im_start|>user\\n" + weeklyNewspaperPrompt + ".<|im_end|>\\n"
                + "<|im_start|>assistant\\n");
        } else {
            searchKey = sendPostRequest(WeeklyNewspaperModelEnum.Llama.getUrl(), "<|begin_of_text|><|start_header_id"
                + "|>system<|end_header_id|>\\n\\nYou are a helpful assistant"
                + ".<|eot_id|><|start_header_id|>user<|end_header_id|>\\n\\n" + weeklyNewspaperPrompt + "<|eot_id"
                + "|><|start_header_id|>assistant<|end_header_id|>\\n\\n");
        }
        return searchKey;
    }

    @Override
    public String getMyWeeklyNewspaperTemplate(String weeklyNewspaperDetail) throws Exception {
        String searchKey = "";
        String weeklyNewspaperPrompt = getMyWeeklyNewspaperTemplatePrompt(weeklyNewspaperDetail);
        ChatMessage systemMessage = new ChatMessage(ChatMessageRole.USER.value(), weeklyNewspaperPrompt);
        ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.builder().model(GTP4.GPT_4_O.getId())
            .messages(Collections.singletonList(systemMessage)).temperature(0.8).build();
        List<ChatCompletionChoice> choices = openAiBaseClient.createChatCompletion(chatCompletionRequest).getChoices();
        if (!CollectionUtils.isEmpty(choices)) {
            log.info("getMyWeeklyNewspaperTemplate choices :{}", JSON.toJSONString(choices));
            searchKey = choices.stream().map(completionChoice -> completionChoice.getMessage().getContent()).filter(
                Objects::nonNull).filter(content -> !content.isEmpty()).collect(Collectors.joining(""));
            // 使用空格作为分隔符拼接所有非空选项
        }
        return searchKey;
    }


    private String getMyWeeklyNewspaperTemplatePrompt(String weeklyNewspaperDetail) {
        String template = paragraphTemplateConfig.getData();
        try {
            Template tpl = new Template("strTpl", template, new Configuration());
            Map<String, String> params = new HashMap<>();
            params.put("weeklyNewspaperDetail", weeklyNewspaperDetail == null ? "" : weeklyNewspaperDetail);
            StringWriter writer = new StringWriter();
            tpl.process(params, writer);
            String output = writer.toString();
            log.info("getMyWeeklyNewspaperTemplate :{}", output);
            return output;
        } catch (Exception e) {
            log.error("getMyWeeklyNewspaperTemplate match error. weeklyNewspaperDetail: {}",
                weeklyNewspaperDetail);
            throw new BaseException("template match error.");
        }
    }

    public static String sendPostRequest(String url, String prompt) throws Exception {
        OkHttpClient client = new OkHttpClient.Builder()
            .connectTimeout(120, java.util.concurrent.TimeUnit.SECONDS)
            .writeTimeout(120, java.util.concurrent.TimeUnit.SECONDS)
            .readTimeout(120, java.util.concurrent.TimeUnit.SECONDS)
            .build();

        MediaType mediaType = MediaType.parse("application/json;charset=UTF-8");
        JsonObject mainJson = new JsonObject();
        mainJson.addProperty("prompt", prompt);

        JsonObject generateConfigJson = new JsonObject();
        generateConfigJson.addProperty("top_p", 1);
        generateConfigJson.addProperty("top_k", 30);
        generateConfigJson.addProperty("temperature", 0.8);
        mainJson.add("generate_config", generateConfigJson);

        RequestBody body = RequestBody.create(mediaType, mainJson.toString());
        Request request = new Request.Builder()
            .url(url)
            .post(body)
            .addHeader("Content-Type", "application/json;charset=UTF-8")
            .build();

        log.info("sendPostRequest request : {}", mainJson.toString());

        Response response = client.newCall(request).execute();

        log.info("sendPostRequest response : {}", response.toString());

        String responseStr = response.body().string();
        JsonObject jsonObject = JsonParser.parseString(responseStr).getAsJsonObject();

        String result = jsonObject.get("response").getAsString();
        return result;
    }

    private String generateWeeklyNewspaperPrompt(String weeklyNewspaperDetail, String promptTemplate) {
        String template = templateWeeklyNewspaperConfig.getData();
        try {
            Template tpl = new Template("strTpl", template, new Configuration());
            Map<String, String> params = new HashMap<>();
            params.put("weeklyNewspaperDetail", weeklyNewspaperDetail == null ? "" : weeklyNewspaperDetail);
            params.put("promptTemplate", promptTemplate == null ? "" : promptTemplate);
            StringWriter writer = new StringWriter();
            tpl.process(params, writer);
            String output = writer.toString();
            log.info("generateWeeklyNewspaperPrompt :{}", output);
            return output;
        } catch (Exception e) {
            log.error("generateWeeklyNewspaperPrompt match error. weeklyNewspaperDetail: {}, promptTemplate: {}",
                weeklyNewspaperDetail, promptTemplate);
            throw new BaseException("template match error.");
        }
    }

    private String generateWeeklyNewspaper(String promptTemplate) {
        String template = generateWeeklyNewspaperConfig.getData();
        try {
            Template tpl = new Template("strTpl", template, new Configuration());
            Map<String, String> params = new HashMap<>();
            params.put("weeklyNewspaperDetail", promptTemplate == null ? "" : promptTemplate);
            StringWriter writer = new StringWriter();
            tpl.process(params, writer);
            String output = writer.toString();
            log.info("generateWeeklyNewspaper->{}",JSON.toJSONString(output));
            return output;
        } catch (Exception e) {
            log.error("generateWeeklyNewspaper match error. weeklyNewspaperDetail: {}", promptTemplate);
            throw new BaseException("template match error.");
        }
    }
    private static List<RecommendPromptKey> extractRecommendPromptKeyDTO(String data) {
        List<RecommendPromptKey> dtoList = new ArrayList<>();
        String[] lines = data.split("\n");

        for (String line : lines) {
            String[] parts = line.split(":");
            if (parts.length == 2) {
                RecommendPromptKey dto = new RecommendPromptKey();
                String key = parts[0].trim();
                // Remove asterisk if it's present at the beginning of the key
                if (key.startsWith("*")) {
                    key = key.substring(1).trim();
                }
                dto.setRecommendPromptKey(key);
                dto.setRecommendReason(parts[1].trim());
                dtoList.add(dto);
            }
        }

        return dtoList;
    }

    private String generateGenerateTitlePrompt(String oldTitle, List<String> keyWords, String modalParticle,
        Long minWords) {
        String template = templateCommodityTitleConfig.getData();
        String keyWordsString = String.join("、", keyWords);
        try {
            Template tpl = new Template("strTpl", template, new Configuration());
            Map<String, String> params = new HashMap<>();
            params.put("oldTitle", oldTitle == null ? "" : oldTitle);
            params.put("keyWords", keyWordsString == null ? "" : keyWordsString);
            params.put("modalParticle", modalParticle == null ? "Business" : modalParticle);
            params.put("minWords", minWords == null ? "" : String.valueOf(minWords));
            StringWriter writer = new StringWriter();
            tpl.process(params, writer);
            String output = writer.toString();
            log.info("generateGenerateTitlePrompt :{}", output);
            return output;
        } catch (Exception e) {
            log.error("template match error. template: {}, oldTitle: {}, keyWords: {}, modalParticle:{}", template,
                oldTitle, keyWords, modalParticle);
            throw new BaseException("template match error.");
        }
    }

    private String generateGenerateSubHeadPrompt(String oldDesc) {
        String template = templateSubHeadConfig.getData();
        try {
            Template tpl = new Template("strTpl", template, new Configuration());
            Map<String, String> params = new HashMap<>();
            params.put("oldDesc", oldDesc == null ? "" : oldDesc);
            StringWriter writer = new StringWriter();
            tpl.process(params, writer);
            String output = writer.toString();
            log.info("generateGenerateSubHeadPrompt :{}", output);
            return output;
        } catch (Exception e) {
            log.error("template match error. template: {},  oldDesc: {}", template, oldDesc);
            throw new BaseException("template match error.");
        }
    }

    private String generateGenerateDescPrompt(String title, String oldDesc, List<String> keyWords, String modalParticle,
        List<String> subHead) {
        String template = templateDescConfig.getData();
        String keyWordsString = String.join("、", keyWords);
        String subHeadString = String.join("、", subHead);
        try {
            Template tpl = new Template("strTpl", template, new Configuration());
            Map<String, String> params = new HashMap<>();
            params.put("title", title == null ? "" : title);
            params.put("oldDesc", oldDesc == null ? "" : oldDesc);
            params.put("keyWords", keyWordsString == null ? "" : keyWordsString);
            params.put("TextStyle", modalParticle == null ? "" : modalParticle);
            params.put("Subtitle", subHeadString == null ? "" : subHeadString);
            StringWriter writer = new StringWriter();
            tpl.process(params, writer);
            String output = writer.toString();
            log.info("generateGenerateDescPrompt :{}", output);
            return output;
        } catch (Exception e) {
            log.error("template match error. template: {}, title: {}, keyWords: {}, modalParticle:{}, subHeadString:{}",
                template, title, keyWords, modalParticle, subHeadString);
            throw new BaseException("template match error.");
        }
    }

    private String generateSearchKeyWordPrompt(String title) {
        String template = templateSearchKeyWordConfig.getData();
        try {
            Template tpl = new Template("strTpl", template, new Configuration());
            Map<String, String> params = new HashMap<>();
            params.put("Title", title == null ? "" : title);
            StringWriter writer = new StringWriter();
            tpl.process(params, writer);
            String output = writer.toString();
            log.info("generateSearchKeyWordPrompt :{}", output);
            return output;
        } catch (Exception e) {
            log.error("template match error. template: {}, title: {}", template, title);
            throw new BaseException("template match error.");
        }
    }

    private String generateRecommendPrompt(String prompt) {
        String template = recommendPromptConfig.getData();
        try {
            Template tpl = new Template("strTpl", template, new Configuration());
            Map<String, String> params = new HashMap<>();
            params.put("myWord", prompt == null ? "" : prompt);
            StringWriter writer = new StringWriter();
            tpl.process(params, writer);
            String output = writer.toString();
            log.info("generateRecommendPrompt :{}", output);
            return output;
        } catch (Exception e) {
            log.error("template match error. template: {}, prompt: {}", template, prompt);
            throw new BaseException("template match error.");
        }
    }

}
