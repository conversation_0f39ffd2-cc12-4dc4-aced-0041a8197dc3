package com.aidc.copilot.framework.annotation.impl;

import com.aidc.copilot.framework.annotation.EnableSynchronized;
import com.aidc.copilot.framework.enums.SyncPointEnum;
import com.aidc.copilot.framework.sync.AbstractSynchronizer;
import com.aidc.copilot.framework.sync.SyncRule;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/5/4
 */
@Slf4j
@Aspect
@Component
public class SynchronizerAspect {
    /**
     * 定义切点方法
     */
    @Pointcut("@annotation(com.aidc.copilot.framework.annotation.EnableSynchronized)")
    public void pointCut() {
    }

    /**
     * 环绕目标方法执行, 使用入参(可选, 且可以指定是否拷贝值) & 返回值(可选); 可指定执行时机
     *
     * @param joinPoint
     * @param enableSynchronized
     * @throws Throwable
     */
    @Around("pointCut() && @annotation(enableSynchronized)")
    public void afterMethod(ProceedingJoinPoint joinPoint, EnableSynchronized enableSynchronized) throws Throwable {
        if (SyncPointEnum.BEFORE.equals(enableSynchronized.syncPoint())) {
            AbstractSynchronizer abstractSynchronizer = assemblySynchronizer(joinPoint.getArgs(), null, enableSynchronized);
            abstractSynchronizer.executeSync();
            joinPoint.proceed(joinPoint.getArgs());
        }
        if (SyncPointEnum.AFTER.equals(enableSynchronized.syncPoint())) {
            Object[] args = null;
            if (enableSynchronized.copyInput()) {
                args = joinPoint.getArgs();
            }
            Object result = joinPoint.proceed(joinPoint.getArgs());
            AbstractSynchronizer abstractSynchronizer =
                    assemblySynchronizer(enableSynchronized.copyInput() ? args : joinPoint.getArgs(), result, enableSynchronized);
            abstractSynchronizer.executeSync();
        }
    }

    /**
     * 组装同步器
     *
     * @param methodArgs
     * @param methodResult
     * @param enableSynchronized
     * @return
     * @throws Throwable
     */
    private AbstractSynchronizer assemblySynchronizer(Object[] methodArgs, Object methodResult, EnableSynchronized enableSynchronized)
            throws Throwable {
        // 获取注解配置
        Class<? extends AbstractSynchronizer> synchronizer = enableSynchronized.synchronizer();
        Class<? extends SyncRule<?>>[] rules = enableSynchronized.rules();
        List<SyncRule<?>> syncRules = Arrays.stream(rules).map(item -> {
            try {
                return item.getConstructor().newInstance();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }).collect(Collectors.toList());
        // 实例化同步器并注册同步规则
        return synchronizer.getConstructor(List.class, Object[].class, Object.class)
                .newInstance(syncRules, methodArgs, methodResult);
    }
}
