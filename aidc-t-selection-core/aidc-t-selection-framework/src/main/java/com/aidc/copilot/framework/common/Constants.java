package com.aidc.copilot.framework.common;

import com.google.common.collect.Lists;

import java.util.List;

/**
 * <AUTHOR> @ alibaba-inc.com>
 * @since 2023/5/16
 */

public class Constants {

    public static final String SHOPIFY_SHOP_DOMAIN_COOKIES_KEY = "shopify_domain";

    public static final String FROZEN_REMIND = "frozen_remind";

    public static final long DIAMOND_TIME_OUT = 2000;

    public static final int ENABLE = 0;

    public static final int DISABLE = 1;

    public static final String COMMODITY_VO_COST_PREFIX = "US $";

    public static final List<String> ECONOMY_SHIP_SERVICE_NAME = Lists.newArrayList("CAINIAO_SUPER_ECONOMY", "AE_CN_SUPER_ECONOMY_G",
            "CAINIAO_SUPER_ECONOMY_SG","CAINIAO_EXPEDITED_ECONOMY","YANWEN_JYT","FLYT_ECONOMY_SG","SUNYOU_ECONOMY_SG","YANWEN_ECONOMY_SG",
            "TOPYOU_ECONOMY_SG","BSC_ECONOMY_SG","SUNYOU_ECONOMY","YANWEN_ECONOMY");

    public static final String BR = "BR";
    public static final String CL = "CL";
    public static final String KR = "KR";
    public static final String TR = "TR";

    /**
     * AI抠图对应的api接口分配的业务场景身份
     */
    public static final String AI_IMAGE_SCENE = "DSCopilot";
}
