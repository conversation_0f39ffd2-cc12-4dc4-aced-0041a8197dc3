package com.aidc.copilot.framework.product.model.release;

import com.alibaba.copilot.boot.basic.data.BaseObject;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @desc:
 * @author: yixiao.cx
 * @create: 2023-07-07
 **/
@Setter
@Getter
public class ReleaseSku extends BaseObject {
    /**
     * ID
     */
    private Long id;
    /**
     * 创建时间
     */
    private Date gmtCreate;
    /**
     * 修改时间
     */
    private Date gmtModified;
    /**
     * 商家ID
     */
    private Long merchantId;
    /**
     * 商家店铺
     */
    private String shopDomain;
    /**
     * ds发布商品ID
     */
    private Long releaseProductId;
    /**
     * 渠道SKU ID（我们平台存储的）
     */
    private Long channelSkuId;
    /**
     * 渠道商品ID（AE的）
     */
    private Long sourceSkuId;
    /**
     * 发布SKU ID（shopify的）
     */
    private Long publishSkuId;
    /**
     * 原始成本
     */
    private BigDecimal originPrice;

    /**
     * 发布价格
     */
    private BigDecimal publishPrice;

    /**
     * 原始库存
     */
    private Integer originInventory;

    /**
     * 删除标记
     */
    private Boolean deleted;

    /**
     * 属性（JSON格式）
     */
    private ReleaseSkuAttributes attributes = new ReleaseSkuAttributes(null);
}
