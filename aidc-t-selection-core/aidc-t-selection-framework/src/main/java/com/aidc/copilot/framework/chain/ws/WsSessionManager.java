package com.aidc.copilot.framework.chain.ws;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.websocket.Session;
import java.io.IOException;
import java.util.Date;
import java.util.Iterator;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * @Author: smy
 * @Date: 2023/5/31 7:53 PM
 */
//@Component
@Slf4j
public class WsSessionManager {

    private final static Long SESSION_MAX_IDLE_TIME_MS = 90*1000L;

    private ConcurrentHashMap<String, Session> sessionPool = new ConcurrentHashMap<>();
    private ConcurrentHashMap<String, Date> key2Time = new ConcurrentHashMap<>();

    private static ScheduledThreadPoolExecutor pool = new ScheduledThreadPoolExecutor(1, new ThreadFactoryBuilder().setNameFormat("ws-session-manager").build(),
            new ThreadPoolExecutor.AbortPolicy());

    @PostConstruct
    public void init() {
        pool.scheduleAtFixedRate(() -> {
            final Iterator<Map.Entry<String, Date>> iterator = key2Time.entrySet().iterator();
            Date current = new Date();
            while (iterator.hasNext()) {
                final Map.Entry<String, Date> next = iterator.next();
                final String key = next.getKey();
                final Date value = next.getValue();
                Long duration = current.getTime() - value.getTime();
                if (duration > SESSION_MAX_IDLE_TIME_MS) {
                    iterator.remove();
                    final Session session = sessionPool.remove(key);
                    try {
                        log.info("websocket close idle session, id:{},last heartbeat:{}", session.getId(), value);
                        session.close();
                    } catch (IOException e) {
                        log.error("websocket close idle session failed, {}, {}", session.getId(), e);
                    }
                }
            }
        }, 10, 60, TimeUnit.SECONDS);
    }

    public Session get(String key) {
        return sessionPool.get(key);
    }


    public synchronized void close(String sessionId) {
        key2Time.remove(sessionId);
        Session session = sessionPool.remove(sessionId);
        try {
            log.info("websocket close session, id:{}", sessionId);
            session.close();
        } catch (IOException e) {
            log.warn("websocket close session failed, {}, {}", sessionId, e);
        }
    }

    public synchronized void put(String sessionId, Session session) {
        log.info("websocket  request {}", sessionId);
        sessionPool.put(sessionId, session);
        key2Time.put(sessionId, new Date());
    }

}
