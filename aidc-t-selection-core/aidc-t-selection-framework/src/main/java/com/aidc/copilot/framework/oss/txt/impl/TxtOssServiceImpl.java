package com.aidc.copilot.framework.oss.txt.impl;

import com.aidc.copilot.framework.oss.OssAbstractService;
import com.aidc.copilot.framework.oss.txt.TxtOssClient;
import com.aidc.copilot.framework.utils.OssUtils;
import com.alibaba.copilot.boot.basic.exception.FrameException;
import com.alibaba.fastjson.JSON;
import com.aliyun.oss.model.PutObjectRequest;
import com.aliyun.oss.model.PutObjectResult;
import com.google.common.base.Joiner;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/7/7
 */
@Slf4j
@Component("TxtOssService")
public class TxtOssServiceImpl extends OssAbstractService implements TxtOssClient {
    @Value("${algorithm_image_oss_service_bucket_name}")
    private String bucketName = "t-selection-algorithms-image";

    @Override
    public void saveFileContent(String filePath, String content) {
        if(StringUtils.isBlank(content)) {
            return;
        }

        ByteArrayInputStream inputStream = new ByteArrayInputStream(content.getBytes());
        PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, filePath, inputStream);
        OssUtils.putObject(putObjectRequest);
    }

    @Override
    public String getFileContent(String objectName) {
        Object content = getObject(objectName);
        if(content == null) {
            return null;
        }
        return String.valueOf(content);
    }

    @Override
    public String getFileContentMd5(String filePath) {
        String content = getFileContent(filePath);
        if(content == null) {
            return "";
        }
        return DigestUtils.md5Hex(content);
    }

    @Override
    public void deleteFile(String filePath) {
        OssUtils.deleteObject(bucketName, filePath);
    }

    @Override
    public boolean isFileExist(String filePath) {
        return OssUtils.isObjectExist(bucketName, filePath);
    }
}
