
package com.aidc.copilot.framework.openai.response;

import java.io.Serializable;

import com.aidc.copilot.framework.openai.model.Choice;
import com.aidc.copilot.framework.openai.model.Usage;
import lombok.Data;

/**
 * 描述：
 *
 * <AUTHOR>
 *  2023-02-15
 */
@Data
public class EditResponse implements Serializable {
    private String id;
    private String object;
    private long created;
    private String model;
    private Choice[] choices;
    private Usage usage;
}
