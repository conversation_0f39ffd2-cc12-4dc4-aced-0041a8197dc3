package com.aidc.copilot.framework.ic.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PluginJson {
    private String schema_version;
    private String name_for_model;
    private String legal_info_url;
    private Auth auth;
    private String logo_url;
    private String name_for_human;
    private Api api;
    private String description_for_human;
    private String description_for_model;
    private String contact_email;


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class Auth {

        private String type;

    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class Api {

        private boolean is_user_authenticated;
        private String type;
        private String url;

    }


}
