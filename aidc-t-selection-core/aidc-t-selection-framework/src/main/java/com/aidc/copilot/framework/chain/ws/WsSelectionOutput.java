package com.aidc.copilot.framework.chain.ws;

import lombok.Getter;
import lombok.Setter;

import java.util.*;

/**
 * @desc: 选品输出结果
 * @author: yixiao.cx
 * @create: 2023-06-07
 **/
@Setter
@Getter
public class WsSelectionOutput extends WsOutput {
    private String intention;
    private String recommendSummary;
    private List<String> options = new ArrayList<>();
    private List<SearchKey> searchKeys = new ArrayList<>();

    public WsSelectionOutput() {
        super(new WsSelectionOutputParser());
    }

    @Override
    public boolean isOutputEnd() {
        return getOutput().endsWith("[DONE]");
    }

    @Setter
    @Getter
    public static class SearchKey {
        public SearchKey() {

        }

        public SearchKey(int index, String keyWord, String reason) {
            this.index = index;
            this.keyWord = keyWord;
            this.reason = reason;
        }

        private int index;
        private String keyWord;
        private String reason;
    }
}
