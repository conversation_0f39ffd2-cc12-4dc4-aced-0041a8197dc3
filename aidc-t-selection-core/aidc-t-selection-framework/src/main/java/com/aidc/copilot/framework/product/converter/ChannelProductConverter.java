package com.aidc.copilot.framework.product.converter;


import com.aidc.copilot.dal.product.dataobject.ChannelProductDO;
import com.aidc.copilot.framework.product.model.channel.ChannelProduct;
import com.aidc.copilot.framework.product.model.channel.ChannelProductAttributes;
import com.aidc.copilot.framework.product.model.ProductImage;
import com.alibaba.copilot.boot.basic.factory.Converter;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

/**
 * @desc:
 * @author: yixiao.cx
 * @create: 2023-07-07
 **/
@Component
public class ChannelProductConverter implements Converter<ChannelProductDO, ChannelProduct> {
    public static final Converter<ChannelProductDO, ChannelProduct> INSTANCE = new ChannelProductConverter();

    private static final String[] ignoreProperties = new String[] {"attributes", "images"};

    @Override
    public ChannelProduct convertA2B(ChannelProductDO productDO) {
        if (productDO == null) {
            return null;
        }
        ChannelProduct product = new ChannelProduct();
        BeanUtils.copyProperties(productDO, product, ignoreProperties);
        product.setImages(JSONObject.parseArray(productDO.getImages(), ProductImage.class));
        product.setAttributes(new ChannelProductAttributes(productDO.getAttributes()));
        return product;
    }

    @Override
    public ChannelProductDO convertB2A(ChannelProduct product) {
        if (product == null) {
            return null;
        }
        ChannelProductDO productDO = new ChannelProductDO();
        BeanUtils.copyProperties(product, productDO, ignoreProperties);
        productDO.setImages(JSONArray.toJSONString(product.getImages()));
        productDO.setAttributes(product.getAttributes().toString());
        return productDO;
    }
}
