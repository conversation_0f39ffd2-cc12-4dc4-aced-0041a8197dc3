package com.aidc.copilot.framework.chain;

import javax.websocket.Session;

/**
 * @Author: smy
 * @Date: 2023/6/1 10:50 AM
 */

public interface StateService {

    void start(String sessionId);

    /**
     * socket有新内容要处理
     *
     * @param sessionId
     * @param text
     */
    void push(Session session, String sessionId, String text, String word);

    /**
     * 清除会话
     * @param sessionId
     */
    void cancel(String sessionId);

}
