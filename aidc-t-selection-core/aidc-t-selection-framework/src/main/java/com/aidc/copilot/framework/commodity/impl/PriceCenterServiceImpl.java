package com.aidc.copilot.framework.commodity.impl;

import com.aidc.copilot.framework.commodity.PriceCenterService;
import com.aidc.copilot.framework.commodity.dto.ItemPriceDTO;
import com.aidc.copilot.framework.executor.ExecutorPool;
import com.aidc.copilot.framework.ic.IcService;
import com.alibaba.fastjson2.JSON;
import com.alibaba.global.ic.dto.scenario.query.ProductQueryResultDTO;
import com.aliexpress.price.core.model.builder.item.SimpleProductDataBuilder;
import com.aliexpress.price.core.model.constants.price.PriceTypeEnum;
import com.aliexpress.price.core.model.dto.environment.Environment;
import com.aliexpress.price.core.model.dto.environment.Scene;
import com.aliexpress.price.core.model.dto.item.ItemCustomInfo;
import com.aliexpress.price.core.model.dto.item.ItemInfoSet;
import com.aliexpress.price.core.model.dto.item.SkuCustomInfo;
import com.aliexpress.price.core.model.dto.price.OriginalPriceInfo;
import com.aliexpress.price.core.model.dto.price.SalePrice;
import com.aliexpress.price.core.model.dto.user.BuyerInfo;
import com.aliexpress.price.core.model.dto.user.SellerInfo;
import com.aliexpress.price.open.facade.PriceCenterReadFacade;
import com.aliexpress.price.open.model.request.PriceQueryRequest;
import com.aliexpress.price.open.model.request.extra.ExtraParams;
import com.aliexpress.price.open.model.request.extra.ump.ItemPromotionExtraParams;
import com.aliexpress.price.open.model.request.extra.ump.PromotionExtraParams;
import com.aliexpress.price.open.model.request.extra.ump.SkuPromotionExtraParams;
import com.aliexpress.price.open.model.result.ItemPriceResult;
import com.aliexpress.price.open.model.result.SkuPriceResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PriceCenterServiceImpl implements PriceCenterService {

    @Autowired
    private IcService icService;

    @Autowired
    private PriceCenterReadFacade priceCenterReadFacade;

    public PriceCenterServiceImpl() {

    }


    @Override
    public ItemPriceResult queryPrice(Long productId, String country, String curreny, Locale locale, Long buyerId) {
        ProductQueryResultDTO productById = icService.getProductById(productId);
        if (productById == null) {
            log.info("--->icService.getProductById返回为NULL");
            return null;
        }
        PriceQueryRequest request = new PriceQueryRequest();
        //构建场景信息
        request.setScene(buildScene());
        //构建环境信息
        request.setEnvironment(buildEnvironment(country, curreny, locale));
        //构建商品信息
        request.setItem(buildItemInfoSet(productById));
        //构建买家信息
        request.setBuyer(buildBuyerInfo(buyerId));
        //构建卖家信息
        request.setSeller(buildSellerInfo(productById));
        //构建透传信息
        request.setExtraParams(buildExtraParams(productById));
        log.info("--->priceCenterReadFacade.queryPrice请求参数：{}", JSON.toJSON(request));
        ItemPriceResult itemPriceResult = priceCenterReadFacade.queryPrice(request);
//        log.info("--->priceCenterReadFacade.queryPrice返回值：{}", JSON.toJSON(itemPriceResult));
        return itemPriceResult;
    }

    @Override
    public List<ItemPriceDTO> queryPriceByProducts(List<ProductQueryResultDTO> products, String country, String curreny, Locale locale, Long buyerId) {

        List<Callable<ItemPriceDTO>> callableList = Lists.newArrayList();
        products.forEach(v -> {
            callableList.add(() -> queryDiscountPrice(v, country, curreny, locale, buyerId));
        });
        List<Future<ItemPriceDTO>> futures = Lists.newArrayList();
        try {
            futures = ExecutorPool.invokeAll(ExecutorPool.pricePool, callableList);
        } catch (Throwable e) {
            log.error("priceCenterService.queryPriceByProducts invokeAll error,productIds:{},", products.stream().map(ProductQueryResultDTO::getProductId).collect(Collectors.toList()), e);
        }
        List<ItemPriceDTO> priceR = Lists.newArrayList();
        for (Future<ItemPriceDTO> future : futures) {
            ItemPriceDTO price = null;
            try {
                price = future.get();
            } catch (Throwable e) {
                log.error("priceCenterService.queryPriceByProducts invokeAll error,productIds:{},", products.stream().map(ProductQueryResultDTO::getProductId).collect(Collectors.toList()), e);
            }
            if (price != null) {
                priceR.add(price);
            }
        }
        return priceR;
    }

    @Override
    public ItemPriceResult queryPrice(ProductQueryResultDTO product, String country, String curreny, Locale locale, Long buyerId) {
        PriceQueryRequest request = new PriceQueryRequest();
        //构建场景信息
        request.setScene(buildScene());
        //构建环境信息
        request.setEnvironment(buildEnvironment(country, curreny, locale));
        //构建买家信息
        request.setBuyer(buildBuyerInfo(buyerId));
        //构建卖家信息
        request.setSeller(buildSellerInfo(product));
        //构建透传信息
        request.setExtraParams(buildExtraParams(product));
        log.info("--->priceCenterReadFacade.queryPrice请求参数(不含商品信息)：{}", JSON.toJSONString(request));
        //构建商品信息
        request.setItem(buildItemInfoSet(product));
        ItemPriceResult itemPriceResult = priceCenterReadFacade.queryPrice(request);
        log.info("--->priceCenterReadFacade.queryPrice返回值：{}", JSON.toJSONString(itemPriceResult));
        return itemPriceResult;
    }

    @Override
    public ItemPriceDTO queryDiscountPrice(ProductQueryResultDTO product, String country, String curreny, Locale locale, Long buyerId) {
        ItemPriceDTO itemPriceDTO = new ItemPriceDTO();

        if (product == null || country == null || curreny == null || locale == null) {
            itemPriceDTO.setSkuPrice(new HashMap<>());
            return itemPriceDTO;
        }

        itemPriceDTO.setItemId(product.getProductId());
        ItemPriceResult itemPriceResult = queryPrice(product, country, curreny, locale, buyerId);
        Map<Long, BigDecimal> skuPriceMap = new HashMap<>();
        if (itemPriceResult != null && itemPriceResult.getSkuPriceResultMap() != null) {
            Map<Long, SkuPriceResult> skuPriceResultMap = itemPriceResult.getSkuPriceResultMap();
            skuPriceResultMap.forEach((key, value) -> {
                // 价格信息
                Map<String, SalePrice> salePriceMap = value.getSalePriceMap();
                OriginalPriceInfo originalPriceInfo = value.getOriginalPriceInfo();
                //取值
                if (salePriceMap != null && salePriceMap.get(PriceTypeEnum.DISCOUNT_PRICE.getValue()) != null &&
                        salePriceMap.get(PriceTypeEnum.DISCOUNT_PRICE.getValue()).getPriceValue() != null &&
                        salePriceMap.get(PriceTypeEnum.DISCOUNT_PRICE.getValue()).getPriceValue().getIntentionAmount() != null) {
                    // 折扣价不为空 取值折扣价
                    skuPriceMap.put(key, salePriceMap.get(PriceTypeEnum.DISCOUNT_PRICE.getValue()).getPriceValue().getIntentionAmount());
                    log.info("--->queryPrice:skuID:{},取折扣价：{}", key, salePriceMap.get(PriceTypeEnum.DISCOUNT_PRICE.getValue()).getPriceValue().getIntentionAmount());
                } else if (originalPriceInfo != null && originalPriceInfo.getOriginalPrice() != null &&
                        originalPriceInfo.getOriginalPrice().getIntentionAmount() != null) {
                    // 没有折扣价 取值原价
                    skuPriceMap.put(key, originalPriceInfo.getOriginalPrice().getIntentionAmount());
                    log.info("--->queryPrice:skuID:{},取原价：{}", key, originalPriceInfo.getOriginalPrice().getIntentionAmount());
                } else {
                    //  没有取到价格信息
                    log.info("--->queryPrice:skuID:{},未取到价格信息", key);
                }
            });
        }
        itemPriceDTO.setSkuPrice(skuPriceMap);
        return itemPriceDTO;
    }


    private ExtraParams buildExtraParams(ProductQueryResultDTO productById) {
        ExtraParams extraParams = new ExtraParams();
        extraParams.setPromotionExtraParams(buildPromotionExtraParams(productById));
        return extraParams;
    }

    private PromotionExtraParams buildPromotionExtraParams(ProductQueryResultDTO productById) {
        PromotionExtraParams promotionExtraParams = new PromotionExtraParams();
        promotionExtraParams.setItemPromotionExtraParams(buildItemPromotionExtraParams(productById));
        return promotionExtraParams;
    }

    private ItemPromotionExtraParams buildItemPromotionExtraParams(ProductQueryResultDTO productById) {
        ItemPromotionExtraParams itemPromotionExtraParams = new ItemPromotionExtraParams();
        itemPromotionExtraParams.setSkuPromotionExtraParamsMap(buildSkuPromotionExtraParamsMap(productById));
        return itemPromotionExtraParams;
    }

    private Map<Long, SkuPromotionExtraParams> buildSkuPromotionExtraParamsMap(ProductQueryResultDTO productById) {
        Map<Long, SkuPromotionExtraParams> skuPromotionExtraParamsMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(productById.getSkuList())) {
            productById.getSkuList().forEach(x -> {
                SkuPromotionExtraParams skuPromotionExtraParams = new SkuPromotionExtraParams();
                skuPromotionExtraParams.setFeatureMap(x.getFeatureMap());
                skuPromotionExtraParamsMap.put(x.getSkuId(), skuPromotionExtraParams);
            });
        }
        return skuPromotionExtraParamsMap;
    }


    private SellerInfo buildSellerInfo(ProductQueryResultDTO productById) {
        SellerInfo sellerInfo = new SellerInfo();
        sellerInfo.setUserId(productById.getSellerQueryResultDTO().getId());
        return sellerInfo;
    }

    private BuyerInfo buildBuyerInfo(Long buyerId) {
        BuyerInfo buyerInfo = new BuyerInfo();
        buyerInfo.setUserId(buyerId);
        return buyerInfo;
    }

    private ItemInfoSet buildItemInfoSet(ProductQueryResultDTO productById) {

        ItemInfoSet itemInfoSet = new ItemInfoSet();
        itemInfoSet.setProductQueryResult(productById);
        itemInfoSet.setItemCustomInfo(buildItemCustomInfo(productById));
        return itemInfoSet;
    }

    private ItemCustomInfo buildItemCustomInfo(ProductQueryResultDTO productById) {
        ItemCustomInfo itemCustomInfo = new ItemCustomInfo();
        // AE商品ID
        itemCustomInfo.setTargetId(String.valueOf(productById.getProductId()));
        itemCustomInfo.setItemId(productById.getProductId());
        // 是否可选-默认值
        itemCustomInfo.setSelected(true);
        // 卖家信息
        itemCustomInfo.setSeller(buildSellerInfo(productById));
        // skuMap初始化
        itemCustomInfo.setSkuMap(buildSkuMap(productById));
        // SimpleProductQueryResult与SimpleSkuQueryResult封装
        SimpleProductDataBuilder.fillItemCustomInfo(productById, itemCustomInfo);
        return itemCustomInfo;
    }

    private Map<Long, SkuCustomInfo> buildSkuMap(ProductQueryResultDTO productById) {
        Map<Long, SkuCustomInfo> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(productById.getSkuList())) {
            productById.getSkuList().forEach(sku -> {
                SkuCustomInfo skuCustomInfo = new SkuCustomInfo();
                skuCustomInfo.setItemId(productById.getProductId());
                skuCustomInfo.setSkuId(sku.getSkuId());
                skuCustomInfo.setPurchaseQuantity(1);
                skuCustomInfo.setPromotionTag(true);
                map.put(sku.getSkuId(), skuCustomInfo);
            });
        }
        return map;
    }

    private Environment buildEnvironment(String country, String curreny, Locale locale) {
        Environment environment = new Environment();
        // 价格中心给的
        environment.setSaasRegion("aeg");
        environment.setShipToCountry(country);
        environment.setLocale(locale.toString());
        environment.setCurrency(curreny);
        // PC端传DESKTOP
        environment.setPlatformType("DESKTOP");
        return environment;
    }

    private Scene buildScene() {
        Scene scene = new Scene();
        // 申请资源
        scene.setScene("cost");
        scene.setAppName("aidc-t-selection");
        return scene;
    }
}
