
package com.aidc.copilot.framework.ic.impl;

import com.aidc.copilot.framework.ic.IcService;
import com.aidc.copilot.framework.ic.dto.IcProductDTO;
import com.aidc.copilot.framework.ic.dto.ImageDTO;
import com.aidc.copilot.framework.ic.dto.SkuDTO;
import com.aidc.copilot.framework.redis.RedisService;
import com.aidc.copilot.framework.utils.AmountUtils;
import com.alibaba.common.lang.StringUtil;
import com.alibaba.copilot.boot.basic.exception.BizException;
import com.alibaba.copilot.boot.basic.exception.ErrorCode;
import com.alibaba.copilot.boot.monitor.annotation.Monitor;
import com.alibaba.copilot.boot.tools.thread.ThreadTracePoolExecutor;
import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.fastjson2.JSON;
import com.alibaba.global.ic.api.CustomerProductServiceFacade;
import com.alibaba.global.ic.constant.FeatureKeyConstants;
import com.alibaba.global.ic.constant.ProductContentType;
import com.alibaba.global.ic.dto.media.MediaInfoDTO;
import com.alibaba.global.ic.dto.media.ProductImageDTO;
import com.alibaba.global.ic.dto.product.price.PriceDTO;
import com.alibaba.global.ic.dto.scenario.query.*;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * @Author: mianyun.yt
 * @Date: 2023/4/28
 */
@Slf4j
@Service
public class IcServiceImpl implements IcService {

    private static final String AE_PROPDUCT_KEY = "ae_product:%s:%s";
    @Resource
    private CustomerProductServiceFacade customerProductServiceFacade;

    @Resource
    private RedisService redisService;

    @Value("${ae.product.redis.cache.time}")
    private Long aeProductRedisCacheTime;

    private ExecutorService icProductQueryExecutor;

    public IcServiceImpl(@Value("${spring.profiles.active}") String env) {
        // 设置全局默认租户
        //Landlord.setDefaultTenantId(TenantId.AE_GLOBAL);
        //        RPCContext.getClientContext().putAttachment("_GL_K", "AE_GLOBAL");

        //        if (env.equals("staging")) {
        //        RequestCtxUtil.setTargetCluster("aliyun-region-vpc-ap-southeast-1-pre");
        //        }
        icProductQueryExecutor = ThreadTracePoolExecutor.of("ic-query", 10, 50, 30, new LinkedBlockingQueue<>(512));
    }

    @Override
    public ProductQueryResultDTO getProductById(Long productId) {
        SingleProductQueryCondition queryCondition = SingleProductQueryCondition
            .queryByProductIdBuilder(productId)
            .build();

        ProductQueryResponse queryRes = customerProductServiceFacade.queryProduct(
            ProductQueryRequest.builder()
                .addQueryCondition(queryCondition)
                //可选, 商品详情的fileserver key，不渲染服务端内容
                .addRequireContentType(ProductContentType.DESCRIPTION)
                //可选，默认不返回类目属性的属性名和枚举属性值的文本
                .addRequireContentType(ProductContentType.PROPERTY_TEXT)
                .addRequireContentType(ProductContentType.IMAGE)
                //可选，默认标题和详描会返回所有语言，设置以后只返回请求语言的相关数据，
                //会影响标题、描述、属性名、属性文本
                //                        .requestLocale(Locale.US)
                //可选，默认返回所有分国家数据，只返回请求国家的相关数据，目前只影响区域价格（原AE offer price）
                //                        .countryCode("US")
                .build()
        );

        if (queryRes.isSuccess()) {
            //查询成功
            return queryRes.getSingleProduct();
        } else {
            log.error("单个查询商品失败, pid：{}, 错误码：{}", productId, queryRes.getErrorCode().getDisplayText());
            //查询失败
            if (queryRes.isProductNotExisted(queryCondition)) {
                //商品ID找不到，一般是商品被删除了

            } else {
                //系统异常

            }
        }

        return null;
    }

    @Override
    @Monitor(name = "IC查询商品信息", level = Monitor.Level.P1, layer = Monitor.Layer.GATEWAY)
    public List<ProductQueryResultDTO> getProductListByIds(List<Long> productIds) {
        List<Future<ProductQueryResultDTO>> futures = productIds.stream()
            .map(productId -> icProductQueryExecutor.submit(() -> getProductById(productId)))
            .collect(Collectors.toList());

        return futures.stream().map(future -> {
            try {
                return future.get();
            } catch (Throwable e) {
                log.error("query product from ic fail", e);
                throw new RuntimeException("query product info fail, please try again");
            }
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    @Override
    public List<ProductQueryResultDTO> listProductByIds(List<Long> productIds) {

        List<ProductQueryResultDTO> result = new ArrayList<>();

        // 分组product
        List<List<Long>> productPartition = Lists.partition(productIds, 20);

        productPartition.forEach(pids -> {
            ProductQueryRequest request = new ProductQueryRequest();
            List<SingleProductQueryCondition> conditions = pids.stream().map(productId -> SingleProductQueryCondition.queryByProductIdBuilder(productId).build()).collect(Collectors.toList());
            request.setProductQueryConditions(conditions);
            request.getProductQueryConditions().addAll(conditions);
            request.setRequireContentTypes(new ProductContentTypes());
            request.getRequireContentTypes().add(ProductContentType.PROPERTY_TEXT.getValue());
            request.getRequireContentTypes().add(ProductContentType.DESCRIPTION.getValue());
            request.getRequireContentTypes().add(ProductContentType.IMAGE.getValue());
            //        request.setRequestLocaleList(Collections.singletonList(Locale.US));
            ProductQueryResponse queryRes = customerProductServiceFacade.queryProduct(request);
            if (queryRes.isSuccess()) {
                result.addAll(queryRes.getBatchProducts());
            } else {
                log.error("批量查询商品失败, pids：{}, 错误码：{}", pids, queryRes.getErrorCode().getDisplayText());
            }
        });
        return result;
    }

    @Override
    public List<SkuQueryResultDTO> getSkuList(Long productId) {
        ProductQueryResultDTO product = getProductById(productId);

        if (product == null) {
            return null;
        }

        return product.getSkuList();
    }

    @Override
    public IcProductDTO getProductByIdWithCache(Long productId, Locale locale) {
        IcProductDTO icProductDTO = null;
        String product = redisService.readValue(String.format(AE_PROPDUCT_KEY, locale.getLanguage(), productId));
        if (StringUtil.isNotBlank(product)) {
            log.info("--->IcServiceImpl:getProductByIdWithCache,redis cache:{}", product);
            try {
                icProductDTO = JSON.parseObject(product, IcProductDTO.class);
            } catch (Exception e) {
                log.error("--->IcServiceImpl:getProductByIdWithCache,redis cache deserialization error:{}", e.getMessage(), e);
            }
        }
        if (icProductDTO != null) {
            log.info("--->IcServiceImpl:getProductByIdWithCache,get product by redis cache,productId:{}", productId);
            return icProductDTO;
        }
        // get value by ic
        SingleProductQueryCondition queryCondition = SingleProductQueryCondition
            .queryByProductIdBuilder(productId)
            .build();
        ProductQueryResponse queryRes = customerProductServiceFacade.queryProduct(
            ProductQueryRequest.builder()
                .addQueryCondition(queryCondition)
                //可选, 商品详情的fileserver key，不渲染服务端内容
                .addRequireContentType(ProductContentType.DESCRIPTION)
                //可选，默认不返回类目属性的属性名和枚举属性值的文本
                .addRequireContentType(ProductContentType.PROPERTY_TEXT)
                .addRequireContentType(ProductContentType.IMAGE)
                .requestLocale(locale)
                .build()
        );
        if (queryRes.isSuccess()) {
            log.info("--->IcServiceImpl:getProductByIdWithCache,get product by ic success,productId:{}", productId);
            ProductQueryResultDTO singleProduct = queryRes.getSingleProduct();
            // 数据映射
            icProductDTO = assembleIcProductDTO(singleProduct, locale);
            // write redis cache
            try {
                String productStr = JSON.toJSONString(icProductDTO);
                log.info("--->IcServiceImpl:getProductByIdWithCache,product str:{}", productStr);
                redisService.writeValueWithExpire(String.format(AE_PROPDUCT_KEY, locale.getLanguage(), productId), aeProductRedisCacheTime, productStr);
            } catch (Exception e) {
                log.error("--->IcServiceImpl:getProductByIdWithCache,write redis cache error,productId:{}", productId, e);
            }
            return icProductDTO;
        }
        return null;
    }

    /**
     * 封装参数IcProductDTO
     *
     * @param singleProduct
     * @param locale
     * @return
     */
    private IcProductDTO assembleIcProductDTO(ProductQueryResultDTO singleProduct, Locale locale) {
        IcProductDTO icProduct = new IcProductDTO();
        // AE productId
        icProduct.setProductId(singleProduct.getProductId());
        // 根据locale获取title
        if (singleProduct.getTitle().get(locale).isPresent()) {
            icProduct.setTitle(singleProduct.getTitle().get(locale).get());
        } else {
            singleProduct.getTitle().getDefault().ifPresent(icProduct::setTitle);
        }
        // 图片信息
        if (singleProduct.getMediaInfoDTO().isPresent()) {
            MediaInfoDTO mediaInfoDTO = singleProduct.getMediaInfoDTO().get();
            List<ProductImageDTO> images = mediaInfoDTO.getImages();
            if (CollectionUtils.isNotEmpty(images)) {
                List<ImageDTO> imageDTOList = images.stream().map(img -> {
                    ImageDTO imageDTO = new ImageDTO();
                    imageDTO.setUrl(img.getImageUrl());
                    imageDTO.setMain(img.getMain());
                    return imageDTO;
                }).collect(Collectors.toList());
                icProduct.setImageDTOList(imageDTOList);
            }
        }
        // 一级类目名称
        if (singleProduct.getCategoryQueryResultDTO() != null) {
            if (singleProduct.getCategoryQueryResultDTO().getTopCategoryName().get(locale).isPresent()) {
                icProduct.setTopCategoryName(singleProduct.getCategoryQueryResultDTO().getTopCategoryName().get(locale).get());
            } else {
                singleProduct.getCategoryQueryResultDTO().getTopCategoryName().getDefault().ifPresent(icProduct::setTopCategoryName);
            }
        }
        // 商品详情url
        icProduct.setDetailPageUrl(singleProduct.getDetailPageUrl());
        // sku基本信息
        if (CollectionUtils.isNotEmpty(singleProduct.getSkuList())) {
            List<SkuDTO> skuDTOS = singleProduct.getSkuList().stream().map(sku -> {
                SkuDTO skuDTO = new SkuDTO();
                skuDTO.setProductId(sku.getProductId());
                skuDTO.setSkuId(sku.getSkuId());
                skuDTO.setFixedPrice(getPriceValue(sku.getPriceDTO()));
                sku.getFeature(FeatureKeyConstants.FEATURE_SKU_SC_ITEM_ID).ifPresent(scItemId -> {
                    if (StringUtil.isNotBlank(scItemId)) {
                        skuDTO.setScItemId(Long.valueOf(scItemId));
                    }
                });
                if (sku.getSalePropertyPairList() != null) {
                    // 销售属性Imgae
                    List<ImageDTO> images = sku.getSalePropertyPairList().stream().filter(x -> x.getImageDTO() != null).map(x -> {
                        ImageDTO imageDTO = new ImageDTO();
                        imageDTO.setUrl(x.getImageDTO().getImageUrl());
                        imageDTO.setMain(x.getImageDTO().getMain());
                        return imageDTO;
                    }).collect(Collectors.toList());
                    skuDTO.setImages(images);
                }
                return skuDTO;
            }).collect(Collectors.toList());
            icProduct.setSkuDTOS(skuDTOS);
        }
        icProduct.setOriginalCurrencyCode(singleProduct.getOriginalCurrencyCode());
        Optional.of(singleProduct.getOriginalLocale()).ifPresent(data -> icProduct.setCountry(data.getCountry()));
        Optional.of(singleProduct.getOriginalLocale()).ifPresent(data -> icProduct.setLang(data.getDisplayLanguage()));
        icProduct.setDeliveryTime(singleProduct.getFeatureMap().get("delivery_time"));
        icProduct.setSellerId(singleProduct.getSellerQueryResultDTO().getId());
        // 根据locale获取desc
        if (singleProduct.getPcDescription().flatMap(desc -> desc.getText().get(locale)).isPresent()) {
            icProduct.setDescription(singleProduct.getPcDescription().flatMap(desc -> desc.getText().get(locale)).get());
        } else {
            singleProduct.getPcDescription().flatMap(desc -> desc.getText().getDefault()).ifPresent(icProduct::setDescription);
        }
        return icProduct;
    }


    BigDecimal getPriceValue(PriceDTO priceDTO) {
        if (priceDTO == null || priceDTO.getFixedPrice() == null || priceDTO.getFixedPrice().getNumber() == null) {
            return null;
        }
        return AmountUtils.toBigDecimal(priceDTO.getFixedPrice().getNumber());
    }

    @Override
    public List<IcProductDTO> listProductByIdsWithCache(List<Long> productIds, Locale locale) {
        CopyOnWriteArrayList<IcProductDTO> cwProductList = new CopyOnWriteArrayList<>();
        CountDownLatch countDownLatch = new CountDownLatch(productIds.size());
        productIds.forEach(c -> {
            icProductQueryExecutor.execute(() -> {
                try {
                    IcProductDTO productByIdWithCache = getProductByIdWithCache(c, locale);
                    if (productByIdWithCache != null) {
                        cwProductList.add(productByIdWithCache);
                    }
                } finally {
                    countDownLatch.countDown();
                }
            });
        });
        try {
            countDownLatch.await(30, TimeUnit.SECONDS);
            return new ArrayList<IcProductDTO>(cwProductList);
        } catch (InterruptedException e) {
            log.error("--->IcServiceImpl:listProductByIdsWithCache, get data error,reason:{}", e.getMessage(), e);
            return cwProductList;
        }
    }
}
