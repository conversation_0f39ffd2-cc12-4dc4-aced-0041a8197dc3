package com.aidc.copilot.framework.oss.txt;

import com.aidc.copilot.framework.enums.FileExtEnum;

import java.io.InputStream;
import java.time.Duration;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/7/7
 */
public interface TxtOssClient {
    /**
     * 以字节数组形式上传 TXT 文件
     *
     * @param filePath 文件路径
     * @param content    文本内容
     * @return 资源路径
     */
    void saveFileContent(String filePath, String content);

    /**
     * 查询文件
     *
     * @param filePath 目标文件 key
     * @return
     */
    String getFileContent(String filePath);

    /**
     * 获取文件内容MD5
     * @param filePath
     * @return
     */
    String getFileContentMd5(String filePath);

    /**
     * 删除文件
     * @param filePath
     */
    void deleteFile(String filePath);

    /**
     * 检测文件是否存在
     * @param filePath
     * @return
     */
    boolean isFileExist(String filePath);
}
