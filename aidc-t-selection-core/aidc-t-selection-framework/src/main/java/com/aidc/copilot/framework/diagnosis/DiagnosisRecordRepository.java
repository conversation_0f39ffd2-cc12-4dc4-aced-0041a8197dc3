package com.aidc.copilot.framework.diagnosis;

import com.aidc.copilot.dal.diagnosis.dataobject.DiagnosisRecordDO;
import com.aidc.copilot.dal.diagnosis.mapper.DiagnosisRecordMapper;
import com.aidc.copilot.framework.diagnosis.converter.DiagnosisRecordConverter;
import com.aidc.copilot.framework.diagnosis.dto.DiagnosisRecordDTO;
import com.aidc.copilot.framework.utils.CollectionUtils;
import com.alibaba.copilot.boot.monitor.annotation.Monitor;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

/**
 * 诊断记录repository
 */
@Repository
@Slf4j
public class DiagnosisRecordRepository {

    @Resource
    private DiagnosisRecordMapper recordMapper;

    @Monitor(name = "创建或更新诊断记录", level = Monitor.Level.P1, layer = Monitor.Layer.REPOSITORY)
    public boolean createOrUpdate(DiagnosisRecordDTO recordDTO) {
        DiagnosisRecordDO recordDO = DiagnosisRecordConverter.INSTANCE.dtoToDo(recordDTO);
        if (recordDO.getId() != null) {
            int result = recordMapper.updateById(recordDO);
            return result == 1L;
        }

        int result = recordMapper.insert(recordDO);
        recordDTO.setId(recordDO.getId());
        return result == 1L;
    }

    @Monitor(name = "获取最新批次的诊断记录", level = Monitor.Level.P1, layer = Monitor.Layer.REPOSITORY)
    public DiagnosisRecordDTO getLastBatchRecord(String shopDomain) {
        Page<DiagnosisRecordDO> queryPage = new Page<>(1, 1);

        QueryWrapper<DiagnosisRecordDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("shop_domain", shopDomain);
        queryWrapper.orderBy(true, false, "id");

        Page<DiagnosisRecordDO> resultDOPage = recordMapper.selectPage(queryPage, queryWrapper);
        DiagnosisRecordDO recordDO = CollectionUtils.getFirst(resultDOPage.getRecords());
        if (recordDO == null) {
            return null;
        }

        return DiagnosisRecordConverter.INSTANCE.doToDto(recordDO);
    }

    @Monitor(name = "获取最新执行批次", level = Monitor.Level.P1, layer = Monitor.Layer.REPOSITORY)
    public Integer getLastBatch(String shopDomain) {
        DiagnosisRecordDTO recordDTO = getLastBatchRecord(shopDomain);
        if (recordDTO == null) {
            return 0;
        }

        return recordDTO.getBatch();
    }
}
