package com.aidc.copilot.framework.oss.image.impl;

import com.aidc.copilot.framework.enums.FileExtEnum;
import com.aidc.copilot.framework.oss.OssAbstractService;
import com.aidc.copilot.framework.oss.image.ImagesOssClient;
import com.alibaba.normandy.credential.CredentialProvider;
import com.aliyun.oss.OSS;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.*;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.Duration;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/6/10
 */
@Slf4j
@Component("AlgorithmImageOssService")
public class AlgorithmImageImagesOssServiceImpl extends OssAbstractService implements ImagesOssClient {
    @Value("${algorithm_image_oss_service_bucket_name}")
    private String bucketName = "t-selection-algorithms-image";

    @Override
    public String putImage(String fileUri, String fileName, String imageUrl, FileExtEnum fileExt) {
        try {
            InputStream inputStream = new URL(imageUrl).openStream();
            return super.putObject(fileUri, fileName, inputStream, fileExt);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * @param fileUri  文件 URI
     * @param fileName 文件名称
     * @param bytes    字节数组 (非base64)
     * @return 上传后的图片路径
     */
    @Override
    public String putImage(String fileUri, String fileName, byte[] bytes, FileExtEnum fileExt) {
        return super.putObject(fileUri, fileName, bytes, fileExt);
    }

    @Override
    public String putObject(String fileUri, String fileName, InputStream inputStream, FileExtEnum fileExt) {
        return putImage(fileUri, fileName, inputStream, fileExt, DEFAULT_SAVED_DURATION);
    }

    /**
     * @param fileUri     文件 URI
     * @param fileName    文件名称
     * @param inputStream 流
     * @return 上传后的图片路径
     */
    @Override
    public String putImage(String fileUri, String fileName, InputStream inputStream, FileExtEnum fileExt, Duration saveDuration) {
        return super.putObject(fileUri, fileName, inputStream, fileExt, saveDuration);
    }

    /**
     * 生成 Jpeg 图片名称
     *
     * @param fileUri
     * @param fileName
     * @return
     */
    @Override
    protected String genOssFileOfExtName(String fileUri, String fileName, FileExtEnum fileExt) {
        return super.genOssFileOfExtName(fileUri, fileName, fileExt);
    }

    @Override
    public String getImage(String objectName) {
        try {
            return String.valueOf(super.getObject(objectName));
        } catch (Exception e) {
            log.error("AlgorithmImageOssService#putObject error, bucketName is {}, objectName is {}", bucketName, objectName, e);
            throw new RuntimeException(e);
        }
    }

    private static byte[] imageFilePathToBytes(String imageFilePath) throws IOException {
        return Files.readAllBytes(Paths.get(imageFilePath));
    }

    private static void byteToFile(byte[] bytes, String filePath) {
        try (FileOutputStream fos = new FileOutputStream(filePath)) {
            fos.write(bytes);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
