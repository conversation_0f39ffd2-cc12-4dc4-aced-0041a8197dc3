package com.aidc.copilot.framework.product.converter;

import com.aidc.copilot.dal.product.dataobject.SupplierSkuDO;
import com.aidc.copilot.framework.product.model.supplier.SupplierSku;
import com.aidc.copilot.framework.product.model.supplier.SupplierSkuAttributes;
import com.alibaba.copilot.boot.basic.factory.Converter;
import org.springframework.beans.BeanUtils;

/**
 * @desc:
 * @author: yixiao.cx
 * @create: 2023-07-07
 **/
public class SupplierSkuConverter implements Converter<SupplierSkuDO, SupplierSku> {

    public static final Converter<SupplierSkuDO, SupplierSku> INSTANCE = new SupplierSkuConverter();

    private static final String[] ignoreProperties = new String[]{"attributes"};

    @Override
    public SupplierSku convertA2B(SupplierSkuDO supplierSkuDO) {
        if (supplierSkuDO == null) {
            return null;
        }
        SupplierSku sku = new SupplierSku();
        BeanUtils.copyProperties(supplierSkuDO, sku, ignoreProperties);
        sku.setAttributes(new SupplierSkuAttributes(supplierSkuDO.getAttributes()));

        return sku;
    }

    @Override
    public SupplierSkuDO convertB2A(SupplierSku supplierSku) {
        if (supplierSku == null) {
            return null;
        }
        SupplierSkuDO skuDO = new SupplierSkuDO();
        BeanUtils.copyProperties(supplierSku, skuDO, ignoreProperties);
        skuDO.setAttributes(supplierSku.getAttributes().toString());

        return skuDO;
    }
}
