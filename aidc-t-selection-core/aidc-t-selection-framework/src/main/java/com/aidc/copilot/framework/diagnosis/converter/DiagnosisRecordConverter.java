package com.aidc.copilot.framework.diagnosis.converter;

import com.aidc.copilot.dal.diagnosis.dataobject.DiagnosisRecordDO;
import com.aidc.copilot.framework.diagnosis.dto.DiagnosisRecordAttributes;
import com.aidc.copilot.framework.diagnosis.dto.DiagnosisRecordDTO;
import com.aidc.copilot.framework.enums.DiagnosisStatus;
import com.alibaba.fastjson.JSON;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface DiagnosisRecordConverter {
    DiagnosisRecordConverter INSTANCE = Mappers.getMapper(DiagnosisRecordConverter.class);

    /**
     * DO转换为DTO
     */
    @Mapping(source = "attributes", target = "attributes", qualifiedByName = "stringToAttributes")
    @Mapping(target = "status", source = "status", qualifiedByName = "stringToStatus")
    DiagnosisRecordDTO doToDto(DiagnosisRecordDO diagnosisRecordDO);

    /**
     * DTO转换为DO
     */
    @Mapping(target = "attributes", qualifiedByName = "attributesToString")
    @Mapping(target = "status", source = "status", qualifiedByName = "statusToString")
    DiagnosisRecordDO dtoToDo(DiagnosisRecordDTO diagnosisRecordDTO);

    @Named("stringToStatus")
    default DiagnosisStatus stringToStatus(String status) {
        if (status == null) {
            return null;
        }
        return DiagnosisStatus.valueOf(status.toUpperCase());
    }

    @Named("statusToString")
    default String statusToString(DiagnosisStatus status) {
        if (status == null) {
            return null;
        }
        return status.name();
    }

    @Named("stringToAttributes")
    default DiagnosisRecordAttributes stringToAttributes(String attributes) {
        if (attributes == null) {
            return new DiagnosisRecordAttributes(null);
        }
        return JSON.parseObject(attributes, DiagnosisRecordAttributes.class);
    }

    @Named("attributesToString")
    default String attributesToString(DiagnosisRecordAttributes attributes) {
        return JSON.toJSONString(attributes);
    }
}
