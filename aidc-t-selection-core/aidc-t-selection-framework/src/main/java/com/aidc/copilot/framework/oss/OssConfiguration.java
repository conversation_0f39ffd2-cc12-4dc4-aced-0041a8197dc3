package com.aidc.copilot.framework.oss;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version 2023/6/25
 */
@Configuration
public class OssConfiguration {

    @Value("${algorithm_image_oss_service_endpoint}")
    private String endpoint = "oss-ap-southeast-1.aliyuncs.com";

    @Value("${algorithm_image_oss_ak}")
    private String ak = "LTAI5tSD2toHzKtoE9qPuaqs";

    @Value("${algorithm_image_oss_sk}")
    private String sk = "******************************";

    @Bean
    public OSS oss() {
        return new OSSClientBuilder().build(
                endpoint, ak, sk
        );
    }
}
