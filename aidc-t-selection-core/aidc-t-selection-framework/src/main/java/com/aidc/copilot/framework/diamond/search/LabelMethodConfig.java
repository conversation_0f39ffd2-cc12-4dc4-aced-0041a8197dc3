package com.aidc.copilot.framework.diamond.search;

import com.aidc.copilot.framework.diamond.AbstractConfigCenter;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class LabelMethodConfig extends AbstractConfigCenter<List<LabelMethodConfigData>> {

    protected final String clazzSimpleName = getClass().getSimpleName();
    private static final String DATA_ID = "aidc-search-config";

    @Override
    protected String getDataId() {
        return DATA_ID;
    }

    @Override
    protected void compile(String dataStr) {
        log.info("{}#receiveConfigInfo receive configInfo, configInfo={}", clazzSimpleName, dataStr);
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            Map<String, List<Map<String, String>>> logisticsMap = objectMapper.readValue(dataStr, new TypeReference<Map<String, List<Map<String, String>>>>(){});
            List<LabelMethodConfigData> labelMethodConfigDataList = new ArrayList<>();
            for (Map.Entry<String, List<Map<String, String>>> entry : logisticsMap.entrySet()) {
                String country = entry.getKey();
                LabelMethodConfigData labelMethodConfigData = new LabelMethodConfigData();
                labelMethodConfigData.setCountry(country);

                List<LabelMethodConfigData.LabelMethodData> configDataList = new ArrayList<>();
                for (Map<String, String> map : entry.getValue()) {
                    String label = map.get("label");
                    String method = map.get("method");
                    LabelMethodConfigData.LabelMethodData configData = new LabelMethodConfigData.LabelMethodData();
                    configData.setLabel(label);
                    configData.setMethod(method);
                    configDataList.add(configData);
                }
                labelMethodConfigData.setConfigDataList(configDataList);

                labelMethodConfigDataList.add(labelMethodConfigData);
            }
            data = labelMethodConfigDataList;
        } catch (Exception e) {
            log.error("LabelMethodConfig#receiveConfigInfo error", e);
        }
    }
}
