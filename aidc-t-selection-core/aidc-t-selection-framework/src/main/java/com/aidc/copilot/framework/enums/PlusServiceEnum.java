package com.aidc.copilot.framework.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/6/7
 */
@Getter
public enum PlusServiceEnum {
    /**
     * AI 生成商品标题
     */
    AI_GENERATE_COMMODITY_TITLE(1, "ai_generate_commodity_title"),
    /**
     * AI 生成商品描述
     */
    AI_GENERATE_COMMODITY_DESCRIPTION(2, "ai_generate_commodity_description"),
    /**
     * 图片背景替换
     */
    IMAGE_BACKGROUND_REPLACEMENT(3, "image_background_replacement"),
    /**
     * 图片模特替换
     */
    IMAGE_MODEL_REPLACEMENT(4, "image_model_replacement"),
    /**
     * 图片去水印
     */
    REMOVE_WATERMARK_FROM_PICTURES(5, "remove_watermark_from_pictures"),

    ;


    /**
     * 平台编码
     */
    private final Integer code;

    /**
     * 平台名称
     */
    private final String desc;

    PlusServiceEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
