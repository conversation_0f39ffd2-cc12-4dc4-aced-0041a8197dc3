package com.aidc.copilot.framework.ic.dto;

import com.alibaba.global.ic.support.MultiLangStringDTO;
import lombok.Data;

import java.util.List;

/**
 * IC商品DTO
 */
@Data
public class IcProductDTO {
    /**
     * 商品ID
     */
    private Long productId;

    /**
     * 标题-当前Locale
     */
    private String title;

    /**
     * 一级类目类目名-当前Locale
     */
    private String topCategoryName;

    /**
     * 商品描述-当前Locale（PC）
     */
    private String description;

    /**
     * 商品详情url
     */
    private String detailPageUrl;

    /**
     * 图片信息-商品级别
     */
    private List<ImageDTO> imageDTOList;

    /**
     * sku 基本信息
     */
    private List<SkuDTO> skuDTOS;

    /**
     * 原发币种
     * */
    private String originalCurrencyCode;

    /**
     * 送达时间
     * */
    private String deliveryTime;

    /**
     * 选品时选定的国家
     */
    private String country;

    /**
     * 商品描述语言
     */
    private String lang;

    /**
     * seller id
     */
    private Long sellerId;

}
