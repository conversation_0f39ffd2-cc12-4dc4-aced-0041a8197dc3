package com.aidc.copilot.framework.product.model.channel;

import com.alibaba.copilot.boot.basic.data.Attributes;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.LinkedHashMap;

/**
 * @desc:
 * @author: yixiao.cx
 * @create: 2023-07-10
 **/
@Slf4j
public class ChannelSkuAttributes extends Attributes {
    /**
     * 旧商品数据关联
     */
    private static final String ATTR_COMMODITY_SKU_ID = "commoditySkuId";

    /**
     * Selling price（import页面编辑）
     */
    private static final String ATTR_SKU_SELLING_PRICE = "skuSellingPrice";

    /**
     * Stock to Shopify（import页面编辑）
     */
    private static final String ATTR_SKU_STOCK_TO_SHOPIFY = "skuStockToShopify";


    /**
     * Stock to Shopify（import页面编辑）
     */
    private static final String ATTR_SKU_STOCK_ON_AE = "skuStockOnAe";


    /**
     * SKU 销售属性
     */
    private static final String ATTR_CHANNEL_SKU_SALE_PROPERTY_PAIR = "skuSalePropertyPair";
    /**
     * SKU 划线价
     */
    private static final String ATTR_CHANNEL_SKU_COMPARE_AT_PRICE = "skuCompareAtPrice";
    /**
     * 从Shopify往回迁移
     */
    private static final String ATTR_MIGRATE_FROM_SHOPIFY = "migrateBackFromShopify";

    public ChannelSkuAttributes(String json) {
        super(json);
    }

    public void setCommoditySkuId(Long id) {
        put(ATTR_COMMODITY_SKU_ID, id);
    }

    public Long getCommoditySkuId() {
        return getAsLong(ATTR_COMMODITY_SKU_ID);
    }

    public void setSkuSellingPrice(BigDecimal price) {
        put(ATTR_SKU_SELLING_PRICE, price);
    }

    public BigDecimal getSkuSellingPrice() {
        return get(ATTR_SKU_SELLING_PRICE, BigDecimal.class);
    }

    public void setSkuStockToShopify(Integer stock) {
        put(ATTR_SKU_STOCK_TO_SHOPIFY, stock);
    }

    public Integer getSkuStockToShopify() {
        return getAsInteger(ATTR_SKU_STOCK_TO_SHOPIFY);
    }

    public void setSkuStockOnAe(Integer stock) {
        put(ATTR_SKU_STOCK_ON_AE, stock);
    }

    public Integer getSkuStockOnAe() {
        return getAsInteger(ATTR_SKU_STOCK_ON_AE);
    }

    public void setSkuSalePropertyPair(LinkedHashMap<String, String> salePropertyPair) {
        if (salePropertyPair == null || salePropertyPair.isEmpty()) {
            return;
        }
        if (!containsKey(ATTR_CHANNEL_SKU_SALE_PROPERTY_PAIR)) {
            put(ATTR_CHANNEL_SKU_SALE_PROPERTY_PAIR, new JSONObject());
        }

        JSONObject salePropertyPairInfo = get(ATTR_CHANNEL_SKU_SALE_PROPERTY_PAIR, JSONObject.class);
        salePropertyPairInfo.putAll(salePropertyPair);

        put(ATTR_CHANNEL_SKU_SALE_PROPERTY_PAIR, salePropertyPairInfo);
    }

    public LinkedHashMap<String, String> getSkuSalePropertyPair() {
        if (!containsKey(ATTR_CHANNEL_SKU_SALE_PROPERTY_PAIR)) {
            return new LinkedHashMap<>();
        }

        JSONObject salePropertyPairInfo = get(ATTR_CHANNEL_SKU_SALE_PROPERTY_PAIR, JSONObject.class);
        if (salePropertyPairInfo == null) {
            return new LinkedHashMap<>();
        }

        return JSON.parseObject(String.valueOf(salePropertyPairInfo), new TypeReference<LinkedHashMap<String, String>>() {
        });
    }

    /**
     * 设置划线价
     *
     * @param skuCompareAtPrice
     */
    public void setCompareAtPrice(String skuCompareAtPrice) {
        put(ATTR_CHANNEL_SKU_COMPARE_AT_PRICE, skuCompareAtPrice);
    }

    /**
     * 获取划线价
     */
    public String getCompareAtPrice() {
        if (!containsKey(ATTR_CHANNEL_SKU_COMPARE_AT_PRICE)) {
            return StringUtils.EMPTY;
        }
        return getAsString(ATTR_CHANNEL_SKU_COMPARE_AT_PRICE);
    }

    public void setMigrateBackFromShopify(Boolean migrateBackFromShopify) {
        if (migrateBackFromShopify != null) {
            put(ATTR_MIGRATE_FROM_SHOPIFY, migrateBackFromShopify);
        }
    }
}
