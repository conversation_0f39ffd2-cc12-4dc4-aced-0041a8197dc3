package com.aidc.copilot.framework.asynctask.worker;

import com.aidc.copilot.framework.asynctask.model.AsyncTask;
import com.aidc.copilot.framework.asynctask.model.AsyncTaskStatus;
import com.aidc.copilot.framework.asynctask.repository.AsyncTaskRepository;
import com.alibaba.copilot.boot.basic.result.Result;
import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.boot.monitor.annotation.Monitor;
import com.alibaba.copilot.boot.monitor.annotation.MonitorResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @desc:
 * @author: yixiao.cx
 * @create: 2023-06-26
 **/
@Slf4j
@Component
public class AsyncTaskWorkerExecutor {

    @Resource
    private AsyncTaskWorkerWatcher asyncTaskWorkerWatcher;
    @Resource
    private AsyncTaskWorkerRegistry asyncTaskWorkerRegistry;
    @Resource
    private AsyncTaskRepository asyncTaskRepository;

    @Monitor(name = "异步任务执行", level = Monitor.Level.P1, layer = Monitor.Layer.OTHER)
    @MonitorResult
    public Result execute(AsyncTask task) {
        AsyncTaskWorker worker = asyncTaskWorkerRegistry.getWorker(task.getType());
        if(worker == null) {
            log.warn("can not found worker for task, taskId={}, type={}", task.getId(), task.getType());
            return handleNoWorkerTask(task);
        }

        if(task.isExpired()) {
            log.info("task is expired, taskId={}", task.getId());
            return handleExpireTask(task);
        }

        if(task.isExceedRetryTimes()) {
            log.info("task is exceed retry times, taskId={}", task.getId());
            return handleFailTask(task);
        }

        if(! checkTaskCapacity(task.getType(), worker.capacity())) {
            log.info("too many running tasks, suspend task, taskId={}, taskType={}", task.getId(), task.getType());
            asyncTaskRepository.updateTaskStatus(task, AsyncTaskStatus.SUSPEND);
            return Result.buildSuccess();
        }

        return doExecuteTask(task, worker);
    }

    private Result doExecuteTask(AsyncTask task, AsyncTaskWorker worker) {
        // 更新任务为执行中
        if(! asyncTaskRepository.updateTaskStatus(task, AsyncTaskStatus.RUNNING)) {
            return Result.buildFailure("update task status to running fail");
        }

        asyncTaskWorkerWatcher.addWatchTask(task);
        try {
            SingleResult result = worker.execute(task);
            task.setResult(result);
            AsyncTaskStatus taskStatus = result.isSuccess() ? AsyncTaskStatus.SUCCESS : AsyncTaskStatus.FAILURE;
            boolean success = asyncTaskRepository.updateTaskStatusAndResult(task, taskStatus);
            log.info("worker execute finish, taskId={}, success={}, result={}", task.getId(), success, task.getResult());
            return Result.buildSuccess();
        } catch (Exception e) {
            log.error("worker execute error, taskId=" + task.getId(), e);
            SingleResult result = SingleResult.buildFailure(e.getClass().getSimpleName(), e.getMessage());
            task.setResult(result);
            asyncTaskRepository.updateTaskStatusAndResult(task, AsyncTaskStatus.FAILURE);
            return Result.buildSuccess();
        }
    }

    private boolean checkTaskCapacity(String taskType, int capacity) {
        Long runningCount = asyncTaskRepository.queryRunningTaskCount(taskType);
        return runningCount < capacity;
    }

    private Result handleNoWorkerTask(AsyncTask task) {
        asyncTaskRepository.updateTaskStatus(task, AsyncTaskStatus.SUSPEND);
        return Result.buildSuccess();
    }

    private Result handleExpireTask(AsyncTask task) {
        if(task.getExecuteCount() == 0) {
            asyncTaskRepository.updateTaskStatus(task, AsyncTaskStatus.EXPIRED);
        } else {
            asyncTaskRepository.updateTaskStatus(task, AsyncTaskStatus.FAILURE);
        }
        return Result.buildSuccess();
    }

    private Result handleFailTask(AsyncTask task) {
        asyncTaskRepository.updateTaskStatus(task, AsyncTaskStatus.FAILURE);
        return Result.buildSuccess();
    }
}
