package com.aidc.copilot.framework.email.repository.impl;

import com.aidc.copilot.dal.email.dataobject.EmailSubscriptionDO;
import com.aidc.copilot.dal.email.mapper.EmailSubscriptionMapper;
import com.aidc.copilot.framework.email.converter.EmailSubscriptionConverter;
import com.aidc.copilot.framework.email.dto.UpdateEmailSubscription;
import com.aidc.copilot.framework.email.model.EmailSubscription;
import com.aidc.copilot.framework.email.repository.EmailSubscriptionRepository;
import com.aidc.copilot.framework.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import groovy.util.logging.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2024/1/16
 */
@Slf4j
@Repository
public class EmailSubscriptionRepositoryImpl implements EmailSubscriptionRepository {
    @Resource
    private EmailSubscriptionMapper emailSubscriptionMapper;

    @Override
    public EmailSubscription getEmailSubscriptionById(Long id) {
        if (id == null) {
            return null;
        }

        EmailSubscriptionDO emailSubscriptionDO = emailSubscriptionMapper.selectById(id);
        if (emailSubscriptionDO == null) {
            return null;
        }
        if (emailSubscriptionDO.getDeleted()) {
            return null;
        }

        return EmailSubscriptionConverter.INSTANCE.convertA2B(emailSubscriptionDO);
    }

    @Override
    public List<EmailSubscription> getEmailSubscriptionByUserId(Long userId) {
        if (userId == null) {
            return null;
        }
        QueryWrapper<EmailSubscriptionDO> queryWrapper = new QueryWrapper<EmailSubscriptionDO>();
        queryWrapper.eq("user_id", userId);
        List<EmailSubscriptionDO> emailSubscriptionDOList = emailSubscriptionMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(emailSubscriptionDOList)) {
            return Collections.emptyList();
        }
        return emailSubscriptionDOList.stream()
            .filter(subscription -> !subscription.getDeleted())
            .map(EmailSubscriptionConverter.INSTANCE::convertA2B)
            .collect(Collectors.toList());
    }

    @Override
    public int updateEmailSubscription(UpdateEmailSubscription updateEmailSubscription) {
        if (updateEmailSubscription == null) {
            return 0;
        }

        EmailSubscriptionDO emailSubscriptionDO = new EmailSubscriptionDO();
        emailSubscriptionDO.setCategory(updateEmailSubscription.getCategory());
        emailSubscriptionDO.setEmail(updateEmailSubscription.getEmail());
        emailSubscriptionDO.setUserId(updateEmailSubscription.getUserId());
        // sale country 默认不设置
        emailSubscriptionDO.setSaleCountry(null);
        emailSubscriptionDO.setExclusiveOpportunities(updateEmailSubscription.getExclusiveOpportunities());
        emailSubscriptionDO.setDeleted(updateEmailSubscription.getDeleted());
        emailSubscriptionDO.setCategoryName(updateEmailSubscription.getCategoryName());

        int rowCount = 0;
        if (updateEmailSubscription.getId() != null) {
            // 更新
            emailSubscriptionDO.setId(updateEmailSubscription.getId());
            emailSubscriptionDO.setGmtModified(new Date());
            rowCount = emailSubscriptionMapper.updateById(emailSubscriptionDO);
        } else {
            // 新增
            emailSubscriptionDO.setGmtCreate(new Date());
            emailSubscriptionDO.setGmtModified(new Date());
            rowCount = emailSubscriptionMapper.insert(emailSubscriptionDO);
        }

        return rowCount;
    }

    @Override
    public List<EmailSubscription> allEmailSubscriptions() {
        QueryWrapper<EmailSubscriptionDO> queryWrapper = new QueryWrapper<EmailSubscriptionDO>();
        queryWrapper.eq("deleted", Boolean.FALSE);
        List<EmailSubscriptionDO> emailSubscriptionDOS = emailSubscriptionMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(emailSubscriptionDOS)) {
            return new ArrayList<>();
        }

        return EmailSubscriptionConverter.INSTANCE.convertA2B(emailSubscriptionDOS);
    }
}
