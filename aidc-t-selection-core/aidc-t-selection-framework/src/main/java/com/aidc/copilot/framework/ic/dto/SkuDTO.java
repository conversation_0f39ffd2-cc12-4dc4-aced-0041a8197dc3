package com.aidc.copilot.framework.ic.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class SkuDTO {

    /**
     * productId
     */
    private Long productId;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * 商品一口价
     */
    private BigDecimal fixedPrice;

    /**
     * sku-feature:sc_item_id
     */
    private Long scItemId;

    /**
     * sku销售属性-imgae
     */
    private List<ImageDTO> images;

}
