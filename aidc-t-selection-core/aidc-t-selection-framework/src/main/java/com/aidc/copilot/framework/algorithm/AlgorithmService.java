package com.aidc.copilot.framework.algorithm;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;

import com.aidc.copilot.framework.utils.AISelectionFeedbackLogUtils;
import com.aidc.service.api.client.common.Result;
import com.aidc.service.api.client.image.dto.ImageGenRequest;
import com.aidc.service.api.client.image.dto.ImageGenResponse;
import com.aidc.service.api.client.image.dto.ImageSegmentRequest;
import com.aidc.service.api.client.image.service.ImageSegementService;
import com.aidc.service.api.client.image.service.RemoveBackgroundSundryService;
import com.aidc.service.api.client.text.ProductDescGenerationService;
import com.aidc.service.api.client.text.ProductTitleGenerationService;
import com.aidc.service.api.client.text.dto.ProductDescGenerationRequest;
import com.aidc.service.api.client.text.dto.ProductDescGenerationResponse;
import com.aidc.service.api.client.text.dto.ProductTitleGenerationRequest;
import com.aidc.service.api.client.text.dto.ProductTitleGenerationResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.springframework.stereotype.Service;

/**
 * @ClassName algorithmService
 * <AUTHOR>
 * @Date 2023/11/15 14:23
 */
@Service
@Slf4j
public class AlgorithmService {

    @Resource
    private ProductTitleGenerationService productTitleGenerationService;

    @Resource
    private ImageSegementService imageSegementService;

    @Resource
    private RemoveBackgroundSundryService removeBackgroundSundryService;

    @Resource
    private ProductDescGenerationService productDescGenerationService;

    public String generateTitle(String productName, List<String> productKeywords, List<String> productCategories) {
        ProductTitleGenerationRequest request = new ProductTitleGenerationRequest();
        request.setScene("DSCopilot");
        request.setProductName(productName);
        request.setProductCategory(productCategories);
        request.setProductKeyword(productKeywords);
        request.setTargetLanguage("en");
        log.info("generateTitle request->{}", JSON.toJSONString(request));
        Result<ProductTitleGenerationResponse> result = productTitleGenerationService.generate(request);
        log.info("generateTitle result->{}", JSON.toJSONString(request));
        if (result == null || result.getData() == null) {
            return null;
        }
        List<String> titles = result.getData().getGeneratedTitle();
        return titles != null && !titles.isEmpty() ? titles.get(0) : null;
    }

    public String generationDesc(String productTitle, String productDescription, List<String> productKeywords, String productCategories) {
        ProductDescGenerationRequest productDescGenerationRequest = new ProductDescGenerationRequest();
        productDescGenerationRequest.setScene("DSCopilot");
        productDescGenerationRequest.setProductTitle(productTitle);
        productDescGenerationRequest.setProductCategory(productCategories);
        productDescGenerationRequest.setProductKeyWords(productKeywords);
        productDescGenerationRequest.setTargetLanguage("en");
        productDescGenerationRequest.setGenerateRichTextDescription(Boolean.TRUE);
        // 使用Jsoup抽取纯文本
        Document doc = Jsoup.parse(productDescription);
        String textDescription = doc.body().text();
        productDescGenerationRequest.setProductDescription(textDescription);
        log.info("generationDesc request->{}", JSON.toJSONString(productDescGenerationRequest));
        Result<ProductDescGenerationResponse> result = productDescGenerationService.generate(productDescGenerationRequest);
        log.info("generateTitle result->{}", JSON.toJSONString(result));
        if (result == null || result.getData() == null) {
            return null;
        }
        return result.getData().getRichTextDescription();
    }

    public String autoSegment(String imageUrl, String imageBase64, Long targetHeight, Long targetWidth) {
        if (StringUtils.isBlank(imageUrl) && StringUtils.isBlank(imageBase64)) {
            return null;
        }
        ImageSegmentRequest imageSegmentRequest = new ImageSegmentRequest();
        imageSegmentRequest.setScene("DSCopilot");
        if (StringUtils.isNotBlank(imageBase64)) {
            imageSegmentRequest.setImageBase64(imageBase64);
        } else {
            imageSegmentRequest.setImageUrl(imageUrl);
        }
        Optional<Long> optTargetWidth = Optional.ofNullable(targetWidth);
        Optional<Long> optTargetHeight = Optional.ofNullable(targetHeight);
        optTargetWidth.ifPresent(v -> imageSegmentRequest.setTargetWidth(Math.toIntExact(v)));
        optTargetHeight.ifPresent(v -> imageSegmentRequest.setTargetHeight(Math.toIntExact(v)));
        log.info("autoSegment request->{}", JSON.toJSONString(imageSegmentRequest));
        Result<ImageGenResponse> imageGenResponseResult = imageSegementService.autoSegment(imageSegmentRequest);
        log.info("autoSegment result->{}", JSON.toJSONString(imageGenResponseResult));
        ImageGenResponse data = imageGenResponseResult.getData();
        return data.getImageUrl();
    }

    public String removeElements(String imageUrl, String imageBase64, List<Integer> objectRemoveElements,
        List<Integer> nonObjectRemoveElements) {
        if (StringUtils.isBlank(imageUrl) && StringUtils.isBlank(imageBase64)) {
            return null;
        }
        ImageGenRequest imageSegmentRequest = new ImageGenRequest();
        imageSegmentRequest.setScene("DSCopilot");
        if (StringUtils.isNotBlank(imageBase64)) {
            imageSegmentRequest.setImageBase64(imageBase64);
        } else {
            imageSegmentRequest.setImageUrl(imageUrl);
        }
        Optional<List<Integer>> optTargetObjectRemoveElements = Optional.ofNullable(objectRemoveElements);
        Optional<List<Integer>> optTargetNonObjectRemoveElements = Optional.ofNullable(nonObjectRemoveElements);
        optTargetObjectRemoveElements.ifPresent(v -> imageSegmentRequest.setObjectRemoveElements(v));
        optTargetNonObjectRemoveElements.ifPresent(v -> imageSegmentRequest.setNonObjectRemoveElements(v));
        log.info("removeElements request->{}", JSON.toJSONString(imageSegmentRequest));
        Result<ImageGenResponse> imageGenResponseResult = removeBackgroundSundryService.removeElements(
            imageSegmentRequest);
        log.info("removeElements result->{}", JSON.toJSONString(imageGenResponseResult));
        ImageGenResponse data = imageGenResponseResult.getData();
        return data.getImageUrl();
    }

}
