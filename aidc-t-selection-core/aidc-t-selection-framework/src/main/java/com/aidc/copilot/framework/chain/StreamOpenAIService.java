package com.aidc.copilot.framework.chain;

import cn.hutool.http.ContentType;
import com.aidc.copilot.framework.chain.ws.SessionMsgUtil;
import com.aidc.copilot.framework.exception.BaseException;
import com.aidc.copilot.framework.feedback.FeedbackLogger;
import com.aidc.copilot.framework.openai.response.ChatCompletionResponse;
import com.aidc.copilot.framework.openai.model.Message;
import com.aidc.copilot.framework.openai.client.OpenAiStreamClient;
import com.aidc.copilot.framework.redis.RedisService;
import com.aidc.copilot.framework.utils.AISelectionFeedbackLogUtils;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import freemarker.template.Configuration;
import freemarker.template.Template;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;
import okhttp3.sse.EventSources;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.websocket.Session;
import java.io.StringWriter;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

/**
 * @Author: smy
 * @Date: 2023/5/30 9:03 PM
 */
@Component
@Slf4j
public class StreamOpenAIService {

    private OpenAiStreamClient client;
    @Resource
    private TemplateConfig templateConfig;

    @Resource
    private RedisService redisService;
    @Resource
    private StateService stateService;

    private final static String USER_ID = "user_id";

    private String generatePrompt(String myWord, String history) {
        String template = templateConfig.getData();
        try {
            Template tpl = new Template("strTpl", template, new Configuration());
            Map<String, String> params = new HashMap<>();
            params.put("myWord", myWord == null? "":myWord);
            params.put("history", history == null? "":history);
            StringWriter writer = new StringWriter();
            tpl.process(params, writer);
            String output = writer.toString();
            return output;
        } catch (Exception e) {
            log.error("template match error. template: {}, myWord: {}, history: {}", template, myWord, history);
            throw new BaseException("template match error.");
        }
    }

    public void cancel(Session session, String sessionId, String word) {
        try {
            AISelectionFeedbackLogUtils.aiSelectionCancelLogs(AISelectionFeedbackLogUtils.parseToUserId(session), templateConfig.getData(), sessionId, word);
        } catch (Exception e) {
            log.error("feedback log error", e);
        }
        stateService.cancel(sessionId);
    }

    public void predict(Session session, String sessionId, String word, String intention) {
        CountDownLatch countDownLatch = new CountDownLatch(1);
        try {
            EventSource.Factory factory = EventSources.createFactory(new OkHttpClient());
            Message message = Message.builder().role(Message.Role.USER).content(generatePrompt(word, intention)).build();
            Map<String, Object> parameter = new HashMap<>();
            parameter.put("model", "anthropic.claude-3-5-sonnet-20240620-v1:0");
            parameter.put("messages", Arrays.asList(message));
            parameter.put("max_tokens", 2048);
            parameter.put("stream", true);
            parameter.put("temperature", 0.5);
            ObjectMapper mapper = new ObjectMapper();
            String requestBody = mapper.writeValueAsString(parameter);
            Request request = new Request.Builder()
                .url("https://pre-openai-keys.alibaba-inc.com/v1/chat/completions")
                .post(RequestBody.create(MediaType.parse(ContentType.JSON.getValue()), requestBody))
                .addHeader("Content-Type", "application/json")
                .addHeader("Authorization", "Bearer aib_copilot-app_3k1!9")
                .build();

            //创建事件
            EventSource eventSource = factory.newEventSource(request, new EventSourceListener() {

                @Override
                public void onOpen(EventSource eventSource, Response response) {
                    log.info("stream openai start");
                    stateService.start(sessionId);
                }

                @Override
                public void onEvent(EventSource eventSource, @Nullable String id, @Nullable String type, String data) {
                    log.info("stream openai result：{}", data);
                    if (StringUtils.isNotBlank(data)) {
                        if (data.equals("[DONE]")) {
                            log.info("stream openai result end");
                            stateService.push(session, sessionId, "[DONE]", word);
                            return;
                        }
                        ChatCompletionResponse chatCompletionResponse = JSONObject.parseObject(data, ChatCompletionResponse.class);

                        Optional<String> firstNonEmptyContent = chatCompletionResponse.getChoices().stream()
                            .map(completionChoice -> completionChoice.getDelta().getContent())
                            .filter(Objects::nonNull) // 过滤出非空的内容
                            .filter(content -> !content.isEmpty()) // 过滤出非空字符串
                            .findFirst(); // 获取第一个非空字符串

                        firstNonEmptyContent.ifPresent(content -> stateService.push(session, sessionId, content, word)); // 如果存在非空字符串，那么推送
                    }
                }

                @SneakyThrows
                @Override
                public void onFailure(EventSource eventSource, @Nullable Throwable t, @Nullable Response response) {
                    if(Objects.isNull(response)){
                        log.error("stream openai failure:{}", t);
                        eventSource.cancel();
                        countDownLatch.countDown();
                        return;
                    }
                    ResponseBody body = response.body();
                    if (Objects.nonNull(body)) {
                        try {
                            AISelectionFeedbackLogUtils.aiSelectionErrorLogs(Objects.isNull(session.getUserProperties()) ? 0L : Long.parseLong((String) session.getUserProperties().get(USER_ID)), templateConfig.getData(), sessionId, word, body.string());
                            AISelectionFeedbackLogUtils.gptLogInfo(word, templateConfig, body.string(), "failure");
                        } catch (Exception e) {
                            log.error("feedback log error: ", e);
                        }
                        log.error("stream openai failure body：{}，error：{}", body.string(), t);
                    } else {
                        try {
                            AISelectionFeedbackLogUtils.aiSelectionErrorLogs(Objects.isNull(session.getUserProperties()) ? 0L : Long.parseLong((String) session.getUserProperties().get(USER_ID)), templateConfig.getData(), sessionId, word, response.message());
                            AISelectionFeedbackLogUtils.gptLogInfo(word, templateConfig, response.message(), "failure");
                        } catch (Exception e) {
                            log.error("feedback log error: ", e);
                        }
                        log.error("stream openai failure response：{}，error：{}", response, t);
                    }
                    eventSource.cancel();
                    countDownLatch.countDown();
                }

                @Override
                public void onClosed(EventSource eventSource) {
                    log.info("stream openai end");
                    AISelectionFeedbackLogUtils.gptLogInfo(word, templateConfig, "", "closed");
                    countDownLatch.countDown();
                }
            });
        } catch (Exception e) {
            log.error("predict error", e);
            SessionMsgUtil.error(session, e.getMessage());
        } finally {
            countDownLatch.countDown();
        }
        try {
            countDownLatch.await(35, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            log.error("stream openai reqeust interrupted.", e);
        }
    }
}
