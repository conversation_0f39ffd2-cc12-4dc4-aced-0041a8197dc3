package com.aidc.copilot.framework.diamond.search;

import com.aidc.copilot.framework.diamond.AbstractConfigCenter;
import com.aidc.copilot.framework.diamond.prt.PrtConfigData;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class PrtFilterConfig extends AbstractConfigCenter<PrtFilterConfigData> {
    protected final String clazzSimpleName = getClass().getSimpleName();
    private static final String DATA_ID = "aidc-search-prt-config";

    @Override
    protected String getDataId() {
        return DATA_ID;
    }

    @Override
    protected void compile(String dataStr) {
        log.info("{}#receiveConfigInfo receive configInfo, configInfo={}", clazzSimpleName, dataStr);
        try {
            JSONArray jsonArray = JSONArray.parseArray(dataStr);
            PrtFilterConfigData prtConfigData = new PrtFilterConfigData();
            List<PrtFilterConfigData.PrtFilterData> configDataList = new ArrayList<>();
            for (int i = 0; i < jsonArray.size(); i++) {
                configDataList.add(JSON.parseObject(jsonArray.getString(i), PrtFilterConfigData.PrtFilterData.class));
            }
            prtConfigData.setConfigDataList(configDataList);
            data = prtConfigData;
        } catch (Exception e) {
            log.error("PrtFilterConfig#receiveConfigInfo error", e);
        }
    }
}
