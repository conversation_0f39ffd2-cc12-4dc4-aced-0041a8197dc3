package com.aidc.copilot.framework.product.model.release;

import com.alibaba.copilot.boot.basic.data.Attributes;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import org.apache.commons.lang3.StringUtils;

import java.util.LinkedHashMap;

/**
 * @desc:
 * @author: yixiao.cx
 * @create: 2023-07-10
 **/
public class ReleaseSkuAttributes extends Attributes {

    /**
     * 旧商品数据关联
     */
    private static final String ATTR_COMMODITY_SKU_ID = "commoditySkuId";

    /**
     * Stock to Shopify
     */
    private static final String ATTR_SKU_STOCK_TO_SHOPIFY = "skuStockToShopify";

    /**
     * SKU 销售属性
     */
    private static final String ATTR_RELEASE_SKU_SALE_PROPERTY_PAIR = "skuSalePropertyPair";

    /**
     * 是否从shopify平台同步过来的
     */
    private static final String ATTR_SYNC_FROM_SHOPIFY = "syncFromShopify";

    /**
     * 从Shopify往回迁移
     */
    private static final String ATTR_MIGRATE_FROM_SHOPIFY = "migrateBackFromShopify";

    public void setCommoditySkuId(Long id) {
        put(ATTR_COMMODITY_SKU_ID, id);
    }

    public Long getCommoditySkuId() {
        return getAsLong(ATTR_COMMODITY_SKU_ID);
    }

    public ReleaseSkuAttributes(String json) {
        super(json);
    }

    public void setSkuStockToShopify(Integer stock) {
        put(ATTR_SKU_STOCK_TO_SHOPIFY, stock);
    }

    public Integer getSkuStockToShopify() {
        return getAsInteger(ATTR_SKU_STOCK_TO_SHOPIFY);
    }

    public void setSkuSalePropertyPair(LinkedHashMap<String, String> salePropertyPair) {
        if (salePropertyPair == null || salePropertyPair.isEmpty()) {
            return;
        }
        if (!containsKey(ATTR_RELEASE_SKU_SALE_PROPERTY_PAIR)) {
            put(ATTR_RELEASE_SKU_SALE_PROPERTY_PAIR, new JSONObject());
        }

        JSONObject salePropertyPairInfo = get(ATTR_RELEASE_SKU_SALE_PROPERTY_PAIR, JSONObject.class);
        salePropertyPairInfo.putAll(salePropertyPair);

        put(ATTR_RELEASE_SKU_SALE_PROPERTY_PAIR, salePropertyPairInfo);
    }

    public LinkedHashMap<String, String> getSkuSalePropertyPair() {
        if (!containsKey(ATTR_RELEASE_SKU_SALE_PROPERTY_PAIR)) {
            return new LinkedHashMap<>();
        }

        JSONObject salePropertyPairInfo = get(ATTR_RELEASE_SKU_SALE_PROPERTY_PAIR, JSONObject.class);
        if (salePropertyPairInfo == null) {
            return new LinkedHashMap<>();
        }

        return JSON.parseObject(String.valueOf(salePropertyPairInfo), new TypeReference<LinkedHashMap<String, String>>() {
        });
    }

    public void setSyncFromShopify(Boolean syncFromShopify) {
        if (syncFromShopify == null) {
            syncFromShopify = false;
        }
        put(ATTR_SYNC_FROM_SHOPIFY, syncFromShopify);
    }

    public boolean getSyncFromShopify() {
        Boolean syncFromShopify = (Boolean) super.getAttributes().get(ATTR_SYNC_FROM_SHOPIFY);
        if (syncFromShopify == null) {
            return false;
        }

        return syncFromShopify;
    }

    public void setMigrateBackFromShopify(Boolean migrateBackFromShopify) {
        if (migrateBackFromShopify != null) {
            put(ATTR_MIGRATE_FROM_SHOPIFY, migrateBackFromShopify);
        }
    }
}
