package com.aidc.copilot.framework.diamond.ai_background;

import com.aidc.copilot.framework.diamond.AbstractConfigCenter;
import com.aidc.copilot.framework.diamond.country.CountryConfigData;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;

@Slf4j
@Component
public class BackgroundSwitcherConfig extends AbstractConfigCenter<BackgroudSwitcherConfigData> {

    protected final String simpleName = getClass().getSimpleName();

    private static final String DATA_ID = "com.aidc.copilot.ai_background";

    @Override
    protected String getDataId() {
        return DATA_ID;
    }

    @Override
    protected void compile(String dataStr) {
        log.info("{}#receiveConfigInfo receive configInfo, configInfo={}", simpleName, dataStr);
        if (dataStr == null) {
            return;
        }
        try {
            data = JSON.parseObject(dataStr, BackgroudSwitcherConfigData.class);
        } catch (Exception e) {
            log.error("CountryConfig#receiveConfigInfo error", e);
        }
    }

    public static final BackgroudSwitcherConfigData DEFAULT_VALUE = new BackgroudSwitcherConfigData()
            .setBackgroundList(
                    Arrays.asList(
                            new BackgroudSwitcherConfigData.BackgroundConfig()
                                    .setBgId("beige")
                                    .setBgUrl("https://t-selection-algorithms-image.oss-ap-southeast-1.aliyuncs.com/background/beige.png")
                                    .setPrompt(""),
                            new BackgroudSwitcherConfigData.BackgroundConfig()
                                    .setBgId("gray")
                                    .setBgUrl("https://t-selection-algorithms-image.oss-ap-southeast-1.aliyuncs.com/background/gray.png")
                                    .setPrompt(""),
                            new BackgroudSwitcherConfigData.BackgroundConfig()
                                    .setBgId("white")
                                    .setBgUrl("https://t-selection-algorithms-image.oss-ap-southeast-1.aliyuncs.com/background/white.png")
                                    .setPrompt(""),
                            new BackgroudSwitcherConfigData.BackgroundConfig()
                                    .setBgId("kitchen")
                                    .setBgUrl("https://t-selection-algorithms-image.oss-ap-southeast-1.aliyuncs.com/background/kitchen.png")
                                    .setPrompt("")
                    )
            );

    public BackgroundSwitcherConfig() {
        super();
        setData(DEFAULT_VALUE);
    }
}
