package com.aidc.copilot.framework.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @description 供货平台
 * @email <EMAIL>
 * @date 2023/5/4
 */
@Getter
public enum CommodityPlatformEnum {
    /**
     * AE
     */
    AE("1", "AliExpress"),
    ;


    /**
     * 平台编码
     */
    private final String code;

    /**
     * 平台名称
     */
    private final String desc;

    CommodityPlatformEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据 code 查询商品平台信息
     *
     * @param code
     * @return
     */
    public static CommodityPlatformEnum getCommodityPlatformByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        return Arrays.stream(CommodityPlatformEnum.values())
                .filter(source -> source.code.equals(code)).findFirst().orElse(null);
    }

}
