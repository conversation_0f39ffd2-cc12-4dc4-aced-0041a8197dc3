package com.aidc.copilot.framework.email.repository;

import com.aidc.copilot.framework.email.dto.UpdateEmailSubscription;
import com.aidc.copilot.framework.email.model.EmailSubscription;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2024/1/16
 */
public interface EmailSubscriptionRepository {
    /**
     * 根据主键查询邮件订阅情况
     *
     * @param id
     * @return
     */
    EmailSubscription getEmailSubscriptionById(Long id);

    /**
     * 查指定用户的邮件订阅情况
     *
     * @param userId
     * @return
     */
    List<EmailSubscription> getEmailSubscriptionByUserId(Long userId);

    /**
     * 新增或更新订阅
     *
     * @param updateEmailSubscription
     * @return
     */
    int updateEmailSubscription(UpdateEmailSubscription updateEmailSubscription);

    /**
     * 所有订阅关系
     *
     * @return
     */
    List<EmailSubscription> allEmailSubscriptions();
}
