package com.aidc.copilot.framework.chain.ws;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * @desc:
 * @author: yixiao.cx
 * @create: 2023-06-07
 **/
public abstract class WsOutput {
    /**
     * 原始输出文本
     */
    private StringBuilder outputSb = new StringBuilder();
    /**
     * 输出文本解析器
     */
    private WsOutputParser parser;

    public WsOutput(WsOutputParser parser) {
        this.parser = parser;
    }

    @JSONField(serialize = false)
    public String getOutput() {
        return outputSb.toString();
    }

    public void appendOutput(String text) {
        outputSb.append(text);
        parser.parse(this);
    }

    public abstract boolean isOutputEnd();
}
