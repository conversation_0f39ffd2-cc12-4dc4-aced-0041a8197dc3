package com.aidc.copilot.framework.openai.config;

import com.alibaba.common.lang.StringUtil;

import com.aidc.copilot.framework.diamond.AbstractConfigCenter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @ClassName ParagraphTemplateConfig
 * <AUTHOR>
 * @Date 2024/6/13 15:59
 */
@Component
@Slf4j
public class ParagraphTemplateConfig extends AbstractConfigCenter<String> {

    private final String defaultTemplate = "作为一个专业的周报专家，你主要根据用户提供的历史多篇周报信息，进行分析理解。反思并整理出一份与用户风格相符的周报格式。\n"
        + "\n"
        + "历史周报信息如下：\n"
        + "${weeklyNewspaperDetail}\n"
        + "\n"
        + "请注意，你的目标理解并学习历史周报的风格和格式，生成一份符合用户风格的示例周报格式。\n"
        + "最后，请记住除周报格式以外的任何信息都不要输出，最终呈现的内容只有周报格式，也不要给出。。";

    @Override
    protected String getDataId() {
        return "com.aidc.copilot.paragraph.template";
    }

    @Override
    protected void compile(String dataStr) {
        data = dataStr;
    }

    @Override
    public String getData() {
        if (StringUtil.isBlank(super.getData())) {
            return defaultTemplate;
        }
        return super.getData();
    }
}
