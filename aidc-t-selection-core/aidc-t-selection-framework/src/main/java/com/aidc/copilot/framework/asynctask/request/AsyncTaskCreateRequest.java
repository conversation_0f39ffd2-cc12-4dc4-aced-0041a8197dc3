package com.aidc.copilot.framework.asynctask.request;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * @desc:
 * @author: yixiao.cx
 * @create: 2023-06-27
 **/
@Setter
@Getter
@Builder
public class AsyncTaskCreateRequest implements Serializable {
    /**
     * 所属店铺
     */
    private String store;
    /**
     * 外部ID
     */
    private String outerId;
    /**
     * 任务Key(幂等用)
     */
    private String uniqueKey;
    /**
     * 任务类型
     */
    private String type;
    /**
     * 重试间隔(毫秒)
     */
    private Integer retryInterval = 0;
    /**
     * 最大重试次数
     */
    private Integer maxRetryTimes = 0;
    /**
     * 开始执行时间
     */
    private Date startExecuteDate;
    /**
     * 过期时间
     */
    private Date expireDate;
    /**
     * 任务请求
     */
    private Object request;
    /**
     * 任务特殊字段 json map[string]string
     */
    private String attributes;
}
