package com.aidc.copilot.framework.email;

import com.taobao.csp.switchcenter.annotation.AppSwitch;
import com.taobao.csp.switchcenter.annotation.NameSpace;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;

@NameSpace(nameSpace = "Email-Template")
@Slf4j
public class EmailTemplateConfig {

    //@TemplateTitleMapping(type = SiteInboxMsg.Type_Item_Inventory_Short)
    @AppSwitch(des = "订单发货_邮件主题")
    public static String Type_Title_Order_Shipments =
        "您的商品${itemTitle}已经发货";

    @AppSwitch(des = "订单发货_邮件body")
    public static String Type_Body_Order_Shipments =
        "您的商品<a href='${shopifyItemLink}' target=\"_blank\" >${itemTitle}</a>，<br> 已经发货。";




    //@TemplateTitleMapping(type = SiteInboxMsg.Type_Item_Inventory_Short)
    @AppSwitch(des = "订单签收_邮件主题")
    public static String Type_Title_Order_Receipt =
        "您的商品${itemTitle}已经签收";

    @AppSwitch(des = "订单签收_邮件body")
    public static String Type_Body_Order_Receipt =
        "您的商品<a href='${shopifyItemLink}' target=\"_blank\" >${itemTitle}</a>，<br> 已经签收。";

}

