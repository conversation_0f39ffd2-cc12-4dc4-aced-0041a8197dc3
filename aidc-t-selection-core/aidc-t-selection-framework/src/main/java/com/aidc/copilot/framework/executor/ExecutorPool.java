package com.aidc.copilot.framework.executor;

import com.google.common.util.concurrent.ThreadFactoryBuilder;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;

/**
 * 统一线程池
 *
 * <AUTHOR>
 * @since 2020/4/3
 */
public class ExecutorPool {

    /**
     * 价格线程池
     */
    public static ExecutorService pricePool = new ThreadPoolExecutor(128, 128,
            60L, TimeUnit.SECONDS, new ArrayBlockingQueue<>(20480), new ThreadFactoryBuilder().setNameFormat(
            "query-price-pool-%d").build(),
            new ThreadPoolExecutor.AbortPolicy());

    public static <T> List<Future<T>> invokeAll(ExecutorService executorService, List<Callable<T>> callableList, long timeoutMs) throws InterruptedException {
        List<Callable<T>> ttls = new ArrayList<>(callableList.size());
        for (Callable<T> callable : callableList) {
            ttls.add(callable);
        }
        return executorService.invokeAll(ttls,timeoutMs, TimeUnit.MILLISECONDS);
    }
    public static <T> List<Future<T>> invokeAll(ExecutorService executorService, List<Callable<T>> callableList) throws InterruptedException {
        List<Callable<T>> ttls = new ArrayList<>(callableList.size());
        for (Callable<T> callable : callableList) {
            ttls.add(callable);
        }
        return executorService.invokeAll(ttls);
    }
    public static <T> Future<T> submit(ExecutorService executorService, Callable<T> callable) {
        return executorService.submit(callable);
    }

}
