package com.aidc.copilot.framework.diamond.ai_background;

import com.aidc.copilot.framework.annotation.DiamondConfigInfo;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/13
 */
@Data
@Accessors(chain = true)
public class BackgroudSwitcherConfigData implements Serializable {
    private List<BackgroundConfig> backgroundList;

    @Data
    public static class BackgroundConfig implements Serializable {
        private String bgId;
        private String prompt;
        private String bgUrl;
    }
}
