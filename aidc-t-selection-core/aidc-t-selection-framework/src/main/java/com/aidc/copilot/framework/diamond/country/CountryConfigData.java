package com.aidc.copilot.framework.diamond.country;

import lombok.Data;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/4/28
 */
@Data
@Component
public class CountryConfigData {
    private List<ConfigData> countryList;

    @Data
    public static class ConfigData {
        /**
         * 国家编码
         */
        private String code;

        /**
         * 国家描述
         */
        private String text;
    }
}
