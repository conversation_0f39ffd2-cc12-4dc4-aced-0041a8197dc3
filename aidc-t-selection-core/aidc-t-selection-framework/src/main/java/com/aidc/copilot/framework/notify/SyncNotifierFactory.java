package com.aidc.copilot.framework.notify;

import com.aidc.copilot.framework.utils.SpringContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description 获取指定的通知器策略类
 * @email <EMAIL>
 * @date 2023/5/4
 */
@Slf4j
@Component
public class SyncNotifierFactory {
    /**
     * 获取通知器
     *
     * @param type 通知器类型
     * @return
     */
    public static SyncNotifier getSyncNotifier(String type) {
        try {
            return SpringContextUtils.getBean(type, SyncNotifier.class);
        } catch (Exception e) {
            log.error("SyncNotifierFactory#getSyncNotifier error type is: {}", type, e);
            throw new RuntimeException(e);
        }
    }
}
