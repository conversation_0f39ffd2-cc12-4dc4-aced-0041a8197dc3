package com.aidc.copilot.framework.chain;

import com.aidc.copilot.framework.diamond.AbstractConfigCenter;
import com.alibaba.common.lang.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Author: smy
 * @Date: 2023/5/31 9:58 AM
 */
@Component
@Slf4j
public class TemplateConfig extends AbstractConfigCenter<String> {

    private final String defaultTemplate = "You are the product selection assistant of the merchant.\n" +
            "\n" +
            "## Please think step by step:\n" +
            "\n" +
            "step 1: Based on the question, deduce 3 commodity keywords that meet AliExpress to build 3 search keys. If the question contains objective description of the commodity, add them to the keywords.\n" +
            "step 2: Give reasons for each keyword.\n" +
            "step 3: Extract a word in line with the search engine from the recommendation reasons, and combine this word with keywords to form a new search key.\n" +
            "step 4: Summarize the previous steps and output the search keywords and recommendation reasons.\n" +
            "step 5: Create a one-sentence recommendation summary based on the search keywords above and the reasons for the recommendation.\n" +
            "step 6: Summarize the question and output to intention.\n" +
            "\n" +
            "## Example:\n" +
            "\n" +
            "Question: I want to open a clothing store for women, please recommend suitable products for sale in the store\n" +
            "\n" +
            "## Recommendation summary：\n" +
            "\n" +
            "To cater to the diverse needs of women, a clothing store should stock loose women's clothing for different occasions, exquisite accessories like jewelry, scarves, purses, and hats to enhance their outfits, and high heeled shoes to provide stylish options for all types of footwear\n" +
            "\n" +
            "## Search keys：\n" +
            "\n" +
            " * loose women's clothing: Women's clothing are essential, providing options for loose styles and occasions\n" +
            " * exquisite women's accessories: Women's accessories enhance the clothing and complete any outfit, such as exquisite jewelry\n" +
            "\n" +
            "## Options：\n" +
            "\n" +
            " * designer women's clothing\n" +
            " * trendy women's accessories\n" +
            " * high-quality women's shoes\n" +
            " * eco-friendly fabrics\n" +
            "\n" +
            "## Intention：\n" +
            "\n" +
            "${history}\n" +
            " \n" +
            "## Start:\n" +
            "\n" +
            "Question:${myWord}";

    @Override
    protected String getDataId() {
        return "com.aidc.copilot.selection.template";
    }

    @Override
    protected void compile(String dataStr) {
        data = dataStr;
    }

    @Override
    public String getData() {
        if (StringUtil.isBlank(super.getData())) {
            return defaultTemplate;
        }
        return super.getData();
    }
}
