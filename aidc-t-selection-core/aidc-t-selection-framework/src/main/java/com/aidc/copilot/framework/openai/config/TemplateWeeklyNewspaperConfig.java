package com.aidc.copilot.framework.openai.config;

import com.alibaba.common.lang.StringUtil;

import com.aidc.copilot.framework.diamond.AbstractConfigCenter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @ClassName TemplateWeeklyNewspaperConfig
 * <AUTHOR>
 * @Date 2024/5/28 14:22
 */
@Component
@Slf4j
public class TemplateWeeklyNewspaperConfig extends AbstractConfigCenter<String>{

        private final String defaultTemplate = "作为一个专业的周报总结专家，你要根据各团队员工的周报，进行分析和理解。然后反思并整理这些信息，来生成一份新的、高质的周报。\n"
            + "${promptTemplate}\n"
            + "\n"
            + "请注意，你的目标是生成一份内容全新高质量的周报，请理解并学习周报example的格式，但不要将样例的内容插入你整理和总结的新周报中。\n"
            + "接下来将给出新的一周员工们的周报内容，请根据周报内容进行理解和抽象关键的重点，并按照给出的示例周报格式生成新的一篇周报。\n"
            + "\n"
            + "内容来源员工的周报记录：\n"
            + "${weeklyNewspaperDetail}";

        @Override
        protected String getDataId() {
        return "com.aidc.copilot.weeklyNewspaper.template";
    }

        @Override
        protected void compile(String dataStr) {
        data = dataStr;
    }

        @Override
        public String getData() {
        if (StringUtil.isBlank(super.getData())) {
            return defaultTemplate;
        }
        return super.getData();
    }
}
