package com.aidc.copilot.framework.hsf;

import com.aidc.service.api.client.gateway.service.ApiGatewayService;
import com.alibaba.boot.hsf.annotation.HSFConsumer;
import com.alibaba.copilot.enabler.client.email.facade.EmailSendHsfApi;
import com.alibaba.copilot.enabler.client.payment.facade.AepayHsfApi;
import com.alibaba.copilot.enabler.client.subscription.facade.DscSubscriptionHsfApi;
import com.alibaba.copilot.enabler.client.subscription.facade.ShopifyCallbackHsfApi;
import com.alibaba.copilot.enabler.client.subscription.facade.ShopifySubscriptionHsfApi;
import com.alibaba.copilot.enabler.client.subscription.facade.SubscriptionHsfApi;
import com.alibaba.copilot.enabler.client.user.facade.UserQueryHsfApi;
import com.alibaba.copilot.enabler.client.user.facade.UserRegisterHsfApi;
import com.alibaba.copilot.enabler.client.user.facade.UserTokenHsfApi;
import com.alibaba.global.address.api.facade.UserAddressReadFacade;
import com.alibaba.global.exchange.api.facade.GlobalRateFacade;
import com.alibaba.global.filebroker.api.FileBrokerUploadService;
import com.alibaba.global.ic.api.CustomerProductServiceFacade;
import com.alibaba.global.inventory.api.InventoryMerchantFacade;
import com.alibaba.global.inventory.platform.api.InventoryChannelReadFacade;
import com.alibaba.global.merchant.seller.api.facade.SellerReadFacade;
import com.alibaba.global.order.management.api.facade.OrderQueryForBuyerFacade;
import com.alibaba.global.payment.api.facade.PaymentOpenFacade;
import com.alibaba.global.uop.api.FulfillmentPackageQueryFacade;
import com.alibaba.global.user.api.facade.GlobalSessionFacade;
import com.alibaba.global.user.api.facade.UserBoolTagFacade;
import com.alibaba.global.user.api.facade.UserReadFacade;
import com.alibaba.intl.addressserver.service.remote.interfaces.AddressRemoteService4Ocean;
import com.alibaba.intl.biz.evaluation.share.statistic.interfaces.EvaluationRemoteStatisticService;
import com.alibaba.taihang.api.feature.query.FeatureQueryApi;
import com.alibaba.taihang.api.group.GroupApi;

import com.aidc.service.api.client.image.service.ImageSegementService;
import com.aidc.service.api.client.image.service.RemoveBackgroundSundryService;
import com.aidc.service.api.client.text.ProductDescGenerationService;
import com.aidc.service.api.client.text.ProductTitleGenerationService;
import com.aliexpress.price.open.facade.PriceCenterReadFacade;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;


/**
 * @Author: mianyun.yt
 * @Date: 2023/4/28
 */
@Profile(value = {"production", "staging", "staging2"})
@Configuration
public class HsfConfig {
    // ae address
    @HSFConsumer(configServerCenters = {"rg-us-east"})
    private UserAddressReadFacade userAddressReadFacade;
    @HSFConsumer(configServerCenters = {"aliyun-region-vpc-ap-southeast-1"})
    private AddressRemoteService4Ocean addressRemoteService4Ocean;

    // ae order
    @HSFConsumer(configServerCenters = {"aliyun-region-vpc-ap-southeast-1"})
    private OrderQueryForBuyerFacade orderQueryForBuyerFacade;
    @HSFConsumer(configServerCenters = {"aliyun-region-vpc-ap-southeast-1"})
    private FulfillmentPackageQueryFacade fulfillmentPackageQueryFacade;
    @HSFConsumer(serviceGroup = "gps", serviceVersion = "1.0.0", configServerCenters = {"rg-us-east"})
    private PaymentOpenFacade paymentOpenFacade;

    // price center
    @HSFConsumer(configServerCenters = {"aliyun-region-vpc-ap-southeast-1"}, serviceGroup = "HSF", serviceVersion = "1.0.0", clientTimeout = 3000)
    PriceCenterReadFacade priceCenterReadFacade;

    // others
    @HSFConsumer(serviceVersion = "1.0.0")
    private FileBrokerUploadService fileBrokerUploadService;

    @HSFConsumer(serviceVersion = "${user.read.version}", clientTimeout = 3000)
    private UserReadFacade userReadFacade;

    @HSFConsumer(serviceVersion = "${ae.user.read.version}", configServerCenters = {"aliyun-region-vpc-ap-southeast-1"}, clientTimeout = 3000)
    private UserReadFacade aeUserReadFacade;

    @HSFConsumer(serviceVersion = "${user.read.version}", clientTimeout = 3000)
    private GlobalSessionFacade globalSessionFacade;

    @HSFConsumer(serviceVersion = "${user.read.version}", clientTimeout = 3000)
    private UserBoolTagFacade userBoolTagFacade;

//    @HSFConsumer(configServerCenters = {"aliyun-region-vpc-ap-southeast-1-pre"})
    @HSFConsumer(configServerCenters = {"aliyun-region-vpc-ap-southeast-1"}, clientTimeout = 6000)
    private CustomerProductServiceFacade customerProductServiceFacade;

//    @HSFConsumer(serviceVersion = "${ic.inventory.version}", configServerCenters = {"aliyun-region-vpc-ap-southeast-1-pre"})
    @HSFConsumer(serviceVersion = "${ic.inventory.version}", configServerCenters = {"aliyun-region-vpc-ap-southeast-1"})
    private InventoryMerchantFacade inventoryMerchantFacade;

    @HSFConsumer(serviceVersion = "${ic.inventory.version}", configServerCenters = {"aliyun-region-vpc-ap-southeast-1"})
    private InventoryChannelReadFacade inventoryChannelReadFacade;

//    @HSFConsumer(serviceVersion = "1.0.0", configServerCenters = {"aliyun-region-vpc-ap-southeast-1-pre"})
    @HSFConsumer(serviceVersion = "1.0.0", configServerCenters = {"aliyun-region-vpc-ap-southeast-1"})
    private GlobalRateFacade GlobalRateFacade;

    @HSFConsumer(serviceVersion = "1.0.0", configServerCenters = {"aliyun-region-vpc-ap-southeast-1"}, serviceGroup = "DUBBO", clientTimeout = 3000)
    private EvaluationRemoteStatisticService evaluationRemoteStatisticService;

    @HSFConsumer(serviceVersion = "1.0.0", configServerCenters = {"aliyun-region-vpc-ap-southeast-1"})
    private SellerReadFacade sellerReadFacade;

    @HSFConsumer(serviceVersion = "1.0.0.AE", serviceGroup = "taihang-framework", configServerCenters = {"aliyun-region-vpc-ap-southeast-1"}, clientTimeout = 20000)
    private GroupApi groupApi;


    @HSFConsumer(serviceVersion = "1.0.0", configServerCenters = {"aliyun-region-vpc-ap-southeast-1"})
    private FeatureQueryApi featureQueryApi;

    @HSFConsumer(serviceVersion = "1.0.0", clientTimeout = 20000)
    private UserQueryHsfApi userQueryHsfApi;

    @HSFConsumer(serviceVersion = "1.0.0", clientTimeout = 20000)
    private UserRegisterHsfApi userRegisterHsfApi;

    @HSFConsumer(serviceVersion = "1.0.0",configServerCenters = {"aliyun-region-vpc-ap-southeast-1"}, clientTimeout = 20000)
    private ProductTitleGenerationService productTitleGenerationService;


    @HSFConsumer(serviceVersion = "1.0.0",configServerCenters = {"aliyun-region-vpc-ap-southeast-1"}, clientTimeout = 60000)
    private ProductDescGenerationService productDescGenerationService;


    @HSFConsumer(serviceVersion = "1.0.0",configServerCenters = {"aliyun-region-vpc-ap-southeast-1"}, clientTimeout = 20000)
    private ImageSegementService imageSegementService;


    @HSFConsumer(serviceVersion = "1.0.0",configServerCenters = {"aliyun-region-vpc-ap-southeast-1"}, clientTimeout = 20000)
    private RemoveBackgroundSundryService removeBackgroundSundryService;

    @HSFConsumer(serviceVersion = "1.0.0", configServerCenters = {"aliyun-region-vpc-ap-southeast-1"}, clientTimeout = 20000)
    private ApiGatewayService apiGatewayService;


    @HSFConsumer(serviceVersion = "1.0.0", clientTimeout = 20000)
    private ShopifySubscriptionHsfApi subscriptionHsfApi;

    @HSFConsumer(serviceVersion = "1.0.0", clientTimeout = 20000)
    private SubscriptionHsfApi basicSubscriptionHsfApi;

    @HSFConsumer(serviceVersion = "1.0.0", clientTimeout = 20000)
    private ShopifyCallbackHsfApi shopifyCallbackHsfApi;

    @HSFConsumer(serviceVersion = "1.0.0", clientTimeout = 20000)
    private AepayHsfApi aepayHsfApi;

    @HSFConsumer(serviceVersion = "1.0.0", clientTimeout = 20000)
    private EmailSendHsfApi emailSendHsfApi;

    @HSFConsumer(serviceVersion = "1.0.0", clientTimeout = 20000)
    private DscSubscriptionHsfApi dscSubscriptionHsfApi;

    @HSFConsumer(serviceVersion = "1.0.0")
    private UserTokenHsfApi userTokenHsfApi;
}
