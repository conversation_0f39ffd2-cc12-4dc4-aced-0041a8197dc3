package com.aidc.copilot.framework.asynctask.model;

import com.aidc.copilot.dal.asynctask.dataobject.AsyncTaskDO;
import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.fastjson.JSONObject;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.util.Date;
import java.util.Objects;

/**
 * @desc: 异步任务
 * @author: yixiao.cx
 * @create: 2023-06-26
 **/
@Setter
@Getter
public class AsyncTask extends AsyncTaskDO {

    public static AsyncTask of(AsyncTaskDO taskDO) {
        AsyncTask task = new AsyncTask();
        BeanUtils.copyProperties(taskDO, task);
        return task;
    }

    public <REQUEST> REQUEST getRequest(Class<REQUEST> requestClass) {
        if(getRequest() == null) {
            return null;
        }
        return JSONObject.parseObject(getRequest(), requestClass);
    }

    public <DATA> SingleResult<DATA> getResult(Class<DATA> dataClass) {
        if(getResult() == null) {
            return null;
        }

        JSONObject resultJSON = JSONObject.parseObject(getResult());
        SingleResult result = resultJSON.toJavaObject(SingleResult.class);
        result.setData(resultJSON.getObject("data", dataClass));
        return result;
    }

    public void setRequest(Object request) {
        if(request == null) {
            return;
        }
        setRequest(JSONObject.toJSONString(request));
    }

    public void setResult(SingleResult result) {
        if(result == null) {
            return;
        }
        setResult(JSONObject.toJSONString(result));
    }

    /**
     * 是否为逻辑成功
     *
     * @return true：是
     */
    public boolean isLogicSucc() {
        return Objects.equals(AsyncTaskStatus.SUCCESS.name(), this.getStatus());
    }

    /**
     * 是否为逻辑执行中
     *
     * @return true：是
     */
    public boolean isLogicOngoing() {
        if (Objects.equals(AsyncTaskStatus.INITIAL.name(), this.getStatus())
                || Objects.equals(AsyncTaskStatus.PENDING.name(), this.getStatus())
                || Objects.equals(AsyncTaskStatus.RUNNING.name(), this.getStatus())
                || Objects.equals(AsyncTaskStatus.SUSPEND.name(), this.getStatus())) {
            return !isExpired();
        }

        if (Objects.equals(AsyncTaskStatus.FAILURE.name(), this.getStatus())) {
            if (isExceedRetryTimes()) {
                return false;
            }

            return !isExpired();
        }

        return false;
    }

    /**
     * 是否为逻辑失败
     *
     * @return true：是
     */
    public boolean isLogicFail() {
        if (Objects.equals(AsyncTaskStatus.EXPIRED.name(), this.getStatus())
                || Objects.equals(AsyncTaskStatus.INTERRUPT.name(), this.getStatus())) {
            return true;
        }

        if (Objects.equals(AsyncTaskStatus.FAILURE.name(), this.getStatus())) {
            if (isExceedRetryTimes()) {
                return true;
            }

            return isExpired();
        }

        return false;
    }

    public boolean isExpired() {
        return getExpireDate().before(new Date());
    }

    public boolean isExceedRetryTimes() {
        return getExecuteCount() > getMaxRetryTimes();
    }
}
