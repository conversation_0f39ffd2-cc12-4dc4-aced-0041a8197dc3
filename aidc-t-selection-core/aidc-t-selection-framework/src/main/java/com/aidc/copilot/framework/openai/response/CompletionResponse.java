package com.aidc.copilot.framework.openai.response;

import java.io.Serializable;

import com.aidc.copilot.framework.openai.model.Choice;
import com.aidc.copilot.framework.openai.model.Usage;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2023/5/30 21:19
 */
@Data
public class CompletionResponse extends OpenAiResponse implements Serializable {
    private String id;
    private String object;
    private long created;
    private String model;
    private Choice[] choices;
    private Usage usage;
}
