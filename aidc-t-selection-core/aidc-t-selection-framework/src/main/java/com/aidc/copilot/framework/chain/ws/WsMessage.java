package com.aidc.copilot.framework.chain.ws;

import com.alibaba.fastjson2.JSON;
import lombok.Data;

/**
 * @Author: smy
 * @Date: 2023/6/2 4:59 PM
 */
@Data
public class WsMessage<T> {

    private boolean success = true;

    private String message;

    private T data;


    public static WsMessage newErrorMessage(String  message) {
        WsMessage wsMessage = new WsMessage();
        wsMessage.setSuccess(false);
        wsMessage.setMessage(message);
        return wsMessage;
    }

    public static WsMessage newErrorMessage(String message, Object data) {
        WsMessage wsMessage = new WsMessage();
        wsMessage.setSuccess(false);
        wsMessage.setMessage(message);
        wsMessage.setData(data);
        return wsMessage;
    }

    public String toText() {
        return JSON.toJSONString(this);
    }
}
