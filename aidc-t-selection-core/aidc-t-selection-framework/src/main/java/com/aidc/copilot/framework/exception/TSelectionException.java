package com.aidc.copilot.framework.exception;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/4/28
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class TSelectionException extends BaseException {

    public TSelectionException(String errorCode, String message) {
        super(errorCode, message);
    }

    public TSelectionException(String errorCode, String message, Throwable throwable) {
        super(errorCode, message, throwable);
    }

    public TSelectionException(TSelectionExceptionEnum exceptionEnum) {
        super(exceptionEnum.getErrorCode(), exceptionEnum.getErrorMessage());
    }
}
