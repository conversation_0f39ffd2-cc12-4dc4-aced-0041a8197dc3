package com.aidc.copilot.framework.exception;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2024/2/1
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class SubscriptionFeatureUsageException extends BaseException {
    private Object data;

    public SubscriptionFeatureUsageException(String errorCode) {
        super(errorCode);
    }

    public SubscriptionFeatureUsageException(String errorCode, String message) {
        super(errorCode, message);
    }

    public SubscriptionFeatureUsageException(String errorCode, String message, Throwable throwable) {
        super(errorCode, message, throwable);
    }

    public SubscriptionFeatureUsageException(TSelectionExceptionEnum exceptionEnum) {
        super(exceptionEnum.getErrorCode(), exceptionEnum.getErrorMessage());
    }

    public SubscriptionFeatureUsageException(TSelectionExceptionEnum exceptionEnum, Object data) {
        super(exceptionEnum.getErrorCode(), exceptionEnum.getErrorMessage());
        this.data = data;
    }
}
