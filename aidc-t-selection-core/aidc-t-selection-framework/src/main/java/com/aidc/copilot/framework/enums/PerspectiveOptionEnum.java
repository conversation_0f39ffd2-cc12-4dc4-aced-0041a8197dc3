package com.aidc.copilot.framework.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * @ClassName PerspectiveOptionEnum
 * <AUTHOR>
 * @Date 2023/10/16 14:38
 */
public enum PerspectiveOptionEnum {

    FACEBOOK_ADS("FACEBOOK_ADS", "Facebook ads", "facebook_ads", "facebook_ads_featured"),

    GOOGLE_ADS("GOOGLE_ADS", "Google ads", "google_ads", "google_ads_featured"),

    GROWING_DEMAND("GROWING_DEMAND", "Growing demand", "growing_demand", "growing_demand_featured"),

    HIGH_DEMAND("HIGH_DEMAND", "High demand", "high_demand", "high_demand_featured"),

    NEW_ADDED_PRODUCTS("NEW_ADDED_PRODUCTS", "New added products", "new_added", "new_added_featured"),

    DROPSHIPPER_WINNING_PRODUCTS("DROPSHIPPER_WINNING_PRODUCTS", "Dropshipper winning products", "ds_winning", "ds_winning_featured");

    /**
     * 经营编码
     */
    private final String code;

    /**
     * 经营名称
     */
    private final String desc;

    /**
     * 数据表字段
     */
    private String fieldName;

    /**
     * 排序字段
     */
    private final String scoreFieldName;

    PerspectiveOptionEnum(String code, String desc, String fieldName, String scoreFieldName) {
        this.code = code;
        this.desc = desc;
        this.fieldName = fieldName;
        this.scoreFieldName = scoreFieldName;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public String getFieldName() {
        return fieldName;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    public String getScoreFieldName() {
        return scoreFieldName;
    }

    public static PerspectiveOptionEnum fromCode(String code) {
        if (StringUtils.isBlank(code)){
            return null;
        }
        for (PerspectiveOptionEnum option : values()) {
            if (option.getCode().equals(code)) {
                return option;
            }
        }
        return null;
    }
}
