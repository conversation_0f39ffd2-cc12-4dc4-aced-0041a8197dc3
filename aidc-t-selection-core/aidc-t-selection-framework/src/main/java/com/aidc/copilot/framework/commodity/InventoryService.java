package com.aidc.copilot.framework.commodity;

import com.alibaba.global.ic.dto.scenario.query.ProductQueryResultDTO;
import com.alibaba.global.ic.dto.scenario.query.SkuQueryResultDTO;
import com.alibaba.global.inventory.api.request.InvSkuDTO;
import com.alibaba.global.inventory.api.request.sourcing.InvSourcingRequestDTO;
import com.alibaba.global.inventory.api.response.merchant.InvQuerySkuResultDTO;
import com.alibaba.global.inventory.api.response.sourcing.InvSourcingSkuResultDTO;

import java.util.List;

/**
 * 库存域服务
 * 文档: https://aliyuque.antfin.com/sirius/sev7ge/inventorymerchantfacade.batchqueryshipfrominventory
 *
 * @Author: mianyun.yt
 * @Date: 2023/5/4
 */
public interface InventoryService {

    /**
     * 根据 sku id 查询库存
     */
    Long getInventoryBySkuId(Long sellerId, Long itemId, Long scItemId, Long skuId);

    /**
     * 根据 sku id 查询渠道库存
     *
     * @param sellerId
     * @param itemId
     * @param scItemId
     * @param skuId
     * @return
     */
    Long getChannelInventoryBySkuId(Long sellerId, Long itemId, Long scItemId, Long skuId);

    Long getInventoryBySkuId(ProductQueryResultDTO product, SkuQueryResultDTO sku);

    /**
     * 指定销售地查询库存
     */
//    List<InvSourcingSkuResultDTO> sourcing(ProductQueryResultDTO product, String divisionId);

//    List<InvSourcingSkuResultDTO> sourcing(List<InvSourcingRequestDTO> requestDTOList);

    /**
     * 批量查询库存
     */
    List<InvQuerySkuResultDTO> batchQueryInventory(List<InvSkuDTO> invSkuDTOList);

    List<InvQuerySkuResultDTO> batchQueryInventory(ProductQueryResultDTO product);

}
