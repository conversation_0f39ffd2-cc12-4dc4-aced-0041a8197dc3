package com.aidc.copilot.framework.openai.model;

import java.util.List;

import com.aidc.copilot.framework.openai.api.KeyStrategyFunction;

/**
 * 描述：自定义的key使用策略
 * 总是使用第一个key
 *
 * <AUTHOR>
 * @date 2023-04-03
 */
public class FirstKeyStrategy implements KeyStrategyFunction<List<String>, String> {

    /**
     * 总是使用第一个
     * @param keys
     * @return
     */
    @Override
    public String apply(List<String> keys) {
        return keys.get(0);
    }
}
