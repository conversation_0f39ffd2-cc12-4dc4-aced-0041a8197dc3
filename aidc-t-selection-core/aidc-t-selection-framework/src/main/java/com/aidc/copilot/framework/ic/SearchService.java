package com.aidc.copilot.framework.ic;

import com.aidc.copilot.framework.ic.dto.MainSiteSearchRes;

/**
 * @Author: mianyun.yt
 * @Date: 2023/5/4
 */
public interface SearchService {

    /**
     * 主站搜索
     * 文档：https://aliyuque.antfin.com/ae-search/nq5vgm/vox5ali96aukaw1c?singleDoc#
     *
     * @param keywords        关键词
     * @param userMemberSeq   买家登录账号ID
     * @param lang            用户选择的语种
     * @param currency        用户选择的币种
     * @param shipFromCountry 从哪里发货
     * @param shipToCountry   发货到哪个国家
     * @param state           发货到哪个省份
     * @param city            发货到哪个城市
     * @param pr              价格区间筛选，eg：100-200
     * @param bids            品牌墙筛选
     * @param attr            属性筛选
     * @param pageNo          页码
     * @param pageSize        每页大小
     * @return 商品列表
     */
    MainSiteSearchRes mainSiteSearch(String keywords, String userMemberSeq, String lang, String currency,
                                     String shipFromCountry, String shipToCountry, String state, String city, String pr, String bids,
                                     String attr, Long pageNo, Long pageSize);

}
