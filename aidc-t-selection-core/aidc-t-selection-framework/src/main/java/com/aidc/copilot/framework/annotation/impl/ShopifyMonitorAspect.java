package com.aidc.copilot.framework.annotation.impl;

import java.util.Arrays;

import com.alibaba.fastjson.JSON;

import com.aidc.copilot.framework.shopify.common.exception.ShopifyException;
import com.aidc.copilot.framework.shopify.sdk.ShopifyRequest;
import com.aidc.copilot.framework.utils.MonitorLogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;


/**
 * shopify api监控切面
 * <AUTHOR> @ alibaba-inc.com>
 * @since 2023/5/25
 */
@Slf4j
@Aspect
@Component
public class ShopifyMonitorAspect {

    @Around("@annotation(com.aidc.copilot.framework.annotation.ShopifyMonitor)")
    public Object monitor(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        Throwable t = null;
        Object result = null;
        try{
            result = joinPoint.proceed();
            return result;
        } catch (Throwable throwable) {
            t = throwable;
            throw t;
        } finally {
            try {
                long endTime = System.currentTimeMillis();
                long executionTime = endTime - startTime;

                String method = joinPoint.getSignature().getName();

                Object[] args = joinPoint.getArgs();
                String api = "";
                ShopifyRequest request = null;
                if(args != null && args.length > 0) {
                    request = (ShopifyRequest)Arrays.stream(args).filter(o -> o instanceof ShopifyRequest).findFirst().orElse(null);
                    if(request != null) {
                        api = request.getPath().replaceAll("\\d+", "*");;
                    }
                }

                String shopifyApi = method + " " + api;

                if(request != null) {
                    log.debug("shopify request: url:{} request:{} result:{}", request.getUrl(), JSON.toJSONString(request.getBody()), JSON.toJSON(result));
                }

                if(t == null) {
                    //success
                    MonitorLogUtil.logShopifyInvoke(shopifyApi, "200", executionTime);
                } else if(t instanceof ShopifyException) {
                    //shopify error
                    ShopifyException e = (ShopifyException)t;
                    MonitorLogUtil.logShopifyInvoke(shopifyApi, e.getErrorCode(), executionTime);
                } else {
                    //others
                    MonitorLogUtil.logShopifyInvoke(shopifyApi, "500", executionTime);
                }
            } catch (Throwable throwable) {
                //ignore
            }
        }
    }
}
