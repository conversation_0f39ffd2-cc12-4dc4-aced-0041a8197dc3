package com.aidc.copilot.framework.openai.config;

import com.alibaba.common.lang.StringUtil;

import com.aidc.copilot.framework.diamond.AbstractConfigCenter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @ClassName generateWeeklyNewspaperConfig
 * <AUTHOR>
 * @Date 2024/7/4 18:09
 */
@Component
@Slf4j
public class GenerateWeeklyNewspaperConfig extends AbstractConfigCenter<String> {

    private final String defaultTemplate = "作为一个专业的周报专家，你需要根据提供基础的周报内容进行深入分析和理解。\n"
        + "你的任务是从中提取关键的项目、成就、问题和计划，并对这些信息进行扩展和细化，以生成一份高质量的全新周报。请注意以下几点：\n"
        + "1. 全面分析和理解周报内容：对每个要素进行深度理解。\n"
        + "2. 扩展关键信息。\n"
        + "3. 请保持基础周报内容的格式风格。\n"
        + "请根据以下示例指引，对提供的信息进行扩展和丰富：\n"
        + "\n"
        + "基础的周报内容：\n"
        + " ${weeklyNewspaperDetail}\n"
        + "\n"
        + "请接下来给出最终优化后的周报内容：\n";

    @Override
    protected String getDataId() {
        return "com.aidc.copilot.weeklyNewspaper.generate";
    }

    @Override
    protected void compile(String dataStr) {
        data = dataStr;
    }

    @Override
    public String getData() {
        if (StringUtil.isBlank(super.getData())) {
            return defaultTemplate;
        }
        return super.getData();
    }
}
