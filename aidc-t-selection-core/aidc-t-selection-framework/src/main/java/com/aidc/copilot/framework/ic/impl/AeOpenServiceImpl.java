package com.aidc.copilot.framework.ic.impl;

import com.aidc.copilot.framework.ae_open_platform.SignatureAlgo;
import com.aidc.copilot.framework.ic.AeOpenService;
import com.aidc.copilot.framework.ic.dto.GenerateTokenDTO;
import com.aidc.copilot.framework.switchconfig.SwitchConfig;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.global.iop.api.IopClient;
import com.global.iop.api.IopClientImpl;
import com.global.iop.api.IopRequest;
import com.global.iop.api.IopResponse;
import com.global.iop.domain.Protocol;
import com.global.iop.util.ApiException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.Map;

/**
 * @Author: mianyun.yt
 * @Date: 2023/5/8
 */
@Slf4j
@Service
public class AeOpenServiceImpl implements AeOpenService {

    @Value("${ae.open.key}")
    private String appKey;

    @Value("${ae.open.secret}")
    private String appSecret;

    @Value("${ae.open.url}")
    private String url;

    @Value("${ae.open.url.pre}")
    private String preUrl;

    private IopClient client;

    private IopClient preClient;

    @PostConstruct
    public void init() {
        try {
            client = new IopClientImpl(url, appKey, appSecret);
            preClient = new IopClientImpl(preUrl, appKey, appSecret);
        } catch (Throwable t) {
            log.error("init iop client error", t);
        }
    }

    @Override
    public GenerateTokenDTO generateToken(String code) throws ApiException {

        IopRequest request = new IopRequest();
        request.setApiName("/auth/token/create");
        request.addApiParameter("code", code);
        IopResponse response = client.execute(request, Protocol.GOP);

        return JSONObject.parseObject(response.getGopResponseBody(), GenerateTokenDTO.class);
    }

    @Override
    public GenerateTokenDTO refreshToken(String refreshToken) throws ApiException {
        IopRequest request = new IopRequest();
        request.setApiName("/auth/token/refresh");
        request.addApiParameter("refresh_token", refreshToken);
        IopResponse response = client.execute(request, Protocol.GOP);

        return JSONObject.parseObject(response.getGopResponseBody(), GenerateTokenDTO.class);
    }

    @Override
    public String calculateFreight(String param, String accessToken) throws ApiException {
        IopRequest request = new IopRequest();
        request.setApiName("aliexpress.logistics.buyer.freight.calculate");
        request.addApiParameter("param_aeop_freight_calculate_for_buyer_d_t_o", param);
        IopResponse response = client.execute(request, accessToken, Protocol.TOP);
        log.info("calculateFreight request: {} response: {}", param, JSON.toJSONString(response.getGopResponseBody()));
        return response.getGopResponseBody();
    }

    @Override
    public String getFreight(String param, String accessToken) throws ApiException {
        IopRequest request = new IopRequest();
        request.setApiName("aliexpress.logistics.buyer.freight.get");
        request.addApiParameter("aeopFreightCalculateForBuyerDTO", param);
        IopResponse response = client.execute(request, accessToken, Protocol.TOP);
        log.info("getFreight request: {} response: {}", param, JSON.toJSONString(response.getGopResponseBody()));
        return response.getGopResponseBody();
    }

    @Override
    public String getProduct(Long productId, String accessToken) throws ApiException {
        IopRequest request = new IopRequest();
        request.setApiName("aliexpress.ds.product.get");
        request.addApiParameter("product_id", String.valueOf(productId));
        request.addApiParameter("target_language", "EN");
        IopResponse response = (SwitchConfig.aeOpenUsePre ? preClient : client).execute(request, accessToken, Protocol.TOP);
        log.info("getProduct productId: {} response: {}", productId, JSON.toJSONString(response.getGopResponseBody()));
        return response.getGopResponseBody();
    }

    @Override
    public String createOrder(String param, String token) throws ApiException {
        IopRequest request = new IopRequest();
        request.setApiName("aliexpress.trade.buy.placeorder");
        request.addApiParameter("param_place_order_request4_open_api_d_t_o", param);
        IopResponse response = (SwitchConfig.aeOpenUsePre ? preClient : client).execute(request, token, Protocol.TOP);
        log.info("createOrder request: {} response: {}", param, JSON.toJSONString(response.getGopResponseBody()));
        return response.getGopResponseBody();
    }

    @Override
    public String queryOrder(String param, String token) throws ApiException {
        IopRequest request = new IopRequest();
        request.setApiName("aliexpress.ds.trade.order.get");
        request.addApiParameter("order_id", param);
        IopResponse response = client.execute(request, token, Protocol.TOP);
        log.error("queryOrder request: {} response: {}", param, JSON.toJSONString(response.getGopResponseBody()));
        return response.getGopResponseBody();
    }

    @Override
    public String queryTrackingInfo(Map<String, String> params, String token) throws ApiException {
        log.info("AeOpenServiceImpl.queryTrackingInfo, params={}, token={}", JSON.toJSONString(params), token);

        IopRequest request = new IopRequest();
        request.setApiName("aliexpress.logistics.ds.trackinginfo.query");
        for (String key : params.keySet()) {
            request.addApiParameter(key, params.get(key));
        }
        IopResponse response = client.execute(request, token, Protocol.TOP);
        log.error("trackingInfo request: {} response: {}", JSON.toJSONString(params), JSON.toJSONString(response.getGopResponseBody()));
        return response.getGopResponseBody();
    }

    @Override
    public String payAuth(String param, String token) throws ApiException {
        IopRequest request = new IopRequest();
        request.setApiName("com.aliexpress.pay.auth");
        request.addApiParameter("param0", param);
        IopResponse response = (SwitchConfig.aeOpenUsePre ? preClient : client).execute(request, token, Protocol.TOP);
        log.error("payAuth request: {} response: {}", param, JSON.toJSONString(response.getGopResponseBody()));
        return response.getGopResponseBody();
    }

    @Override
    public String payOrderGroup(String param, String token) throws ApiException {
        IopRequest request = new IopRequest();
        request.setApiName("com.aliexpress.pay.group");
        request.addApiParameter("param0", param);
        IopResponse response = (SwitchConfig.aeOpenUsePre ? preClient : client).execute(request, token, Protocol.TOP);
        log.error("payOrderGroup request: {} response: {}", param, JSON.toJSONString(response.getGopResponseBody()));
        return response.getGopResponseBody();
    }

    /**
     * 查询店铺名
     *
     * @param params
     * @param token
     * @return
     * @throws ApiException
     */
    @Override
    public String queryStoreName(Map<String, String> params, String token) {
        log.info("AeOpenServiceImpl.queryStoreName, params={}, token={}", JSON.toJSONString(params), token);

        try {
            IopRequest request = new IopRequest();
            request.setApiName("aliexpress.postproduct.redefining.findaeproductbyidfordropshipper");
            for (String key : params.keySet()) {
                request.addApiParameter(key, params.get(key));
            }
            IopResponse response = client.execute(request, token, Protocol.TOP);
            log.info("AeOpenServiceImpl.queryStoreName, response={}", JSON.toJSONString(response.getGopResponseBody()));

            JSONObject jsonObject = JSON.parseObject(response.getGopResponseBody());
            String storeName = jsonObject.getJSONObject("aliexpress_postproduct_redefining_findaeproductbyidfordropshipper_response").getJSONObject("result").getJSONObject("store_info").getString("store_name");

            return storeName;
        } catch (Exception e) {
            log.error("AeOpenServiceImpl.queryStoreName exception, e={}", e.getMessage(), e);
        }

        return null;
    }

    @Override
    public Object generateAffiliateLinks(Map<String, String> params) throws ApiException, IOException {

        final String appKey = "501212";
        final String appSecret = "CGt7O34uk5HiEbcXhBe9SR8x1SIZDGdY";

        final String url = "http://gw.api.taobao.com/router/rest";
        final String apiName = "aliexpress.affiliate.link.generate";

        client = new IopClientImpl(url, appKey, appSecret);
        IopRequest request = new IopRequest();
        request.setApiName(apiName);
        String sign = SignatureAlgo.signApiRequest(params, appSecret, "sha256", apiName);
        request.addApiParameter("app_signature", sign);
        request.addApiParameter("promotion_link_type", params.get("promotion_link_type"));
        request.addApiParameter("source_values", params.get("source_values"));
        request.addApiParameter("tracking_id", params.get("tracking_id"));
        IopResponse response = client.execute(request, Protocol.TOP);
        System.out.println(response.getGopResponseBody());
//        Thread.sleep(10);
        return response;
    }

}
