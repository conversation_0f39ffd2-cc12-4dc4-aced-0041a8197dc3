package com.aidc.copilot.framework.chain.ws;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import javax.websocket.Session;
import java.io.IOException;

/**
 * @Author: smy
 * @Date: 2023/6/6 10:34 AM
 */
@Slf4j
public class SessionMsgUtil {

    public synchronized static void error(Session session, String errorMsg) {
        try {
            session.getBasicRemote().sendText(WsMessage.newErrorMessage(errorMsg).toText());
        } catch (IOException e) {
            log.error("websocket write error", e);
        }
    }

    public synchronized static void errorData(Session session, String errorMsg, Object data) {
        try {
            session.getBasicRemote().sendText(WsMessage.newErrorMessage(errorMsg, data).toText());
        } catch (IOException e) {
            log.error("websocket write error", e);
        }
    }

    public synchronized static void success(Session session, Object data) {
        WsMessage wsMessage = new WsMessage<>();
        wsMessage.setData(data);
        try {
            session.getBasicRemote().sendText(wsMessage.toText());
        } catch (IOException e) {
            log.error("websocket write error", e);
        }
    }

    public synchronized static void success(Session session, State sate, Object data) {
        WsMessage wsMessage = new WsMessage<>();
        wsMessage.setData(new StateMessage(sate, data));
        try {
            session.getBasicRemote().sendText(wsMessage.toText());
        } catch (IOException e) {
            log.error("websocket write error", e);
        }
    }

    @Data
    public static class StateMessage<T> {
        private State step;
        private T data;

        public StateMessage(State step, T data) {
            this.step = step;
            this.data = data;
        }
    }

}
