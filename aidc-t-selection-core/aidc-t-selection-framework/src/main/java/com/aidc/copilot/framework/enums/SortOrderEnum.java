package com.aidc.copilot.framework.enums;

/**
 * @ClassName SortOrderEnum
 * <AUTHOR>
 * @Date 2024/1/16 14:31
 */
public enum SortOrderEnum {
    /**
     * 正排
     */
    ASC("ASC"),
    /**
     * 倒排
     */
    DESC("DESC");

    /**
     * 编码
     */
    private final String code;

    SortOrderEnum(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }
}
