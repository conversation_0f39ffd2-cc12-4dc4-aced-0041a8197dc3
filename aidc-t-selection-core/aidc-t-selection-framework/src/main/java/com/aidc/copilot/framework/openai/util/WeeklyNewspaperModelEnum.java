package com.aidc.copilot.framework.openai.util;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
@AllArgsConstructor
public enum WeeklyNewspaperModelEnum {

    GPT4("GPT4","https://openai-keys.alibaba-inc.com"),

    <PERSON><PERSON>("Qwen-72B","http://aib-innovation-qwen.alibaba-inc.com"),

    <PERSON><PERSON><PERSON>("Llama3-8B","http://aib-innovation-llama.alibaba-inc.com");

    private final String code;

    private final String url;

    public static WeeklyNewspaperModelEnum getValue(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (WeeklyNewspaperModelEnum weeklyNewspaperModelEnum : WeeklyNewspaperModelEnum.values()) {
            if (weeklyNewspaperModelEnum.getCode().equals(code)) {
                return weeklyNewspaperModelEnum;
            }
        }
        return null;
    }
}
