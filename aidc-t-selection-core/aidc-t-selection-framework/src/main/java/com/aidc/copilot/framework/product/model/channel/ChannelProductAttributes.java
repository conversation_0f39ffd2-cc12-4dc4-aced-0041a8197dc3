package com.aidc.copilot.framework.product.model.channel;

import com.alibaba.copilot.boot.basic.data.Attributes;
import com.alibaba.fastjson.JSONArray;

import java.util.ArrayList;
import java.util.List;

/**
 * @desc:
 * @author: yixiao.cx
 * @create: 2023-07-10
 **/
public class ChannelProductAttributes extends Attributes {
    private static final String ATTR_COMMODITY_ID = "commodityId";
    /**
     * 推送过的店铺
     */
    private static final String ATTR_PUSH_STORES = "pushStores";
    /**
     * sku数量
     */
    private static final String ATTR_SKU_SIZE = "skuSize";
    /**
     * 货币
     */
    private static final String ATTR_CURRENCY = "currency";
    /**
     * 语言
     */
    private static final String ATTR_LANG = "lang";
    /**
     * 国家
     */
    private static final String ATTR_COUNTRY = "country";
    /**
     * 导入任务id
     */
    private static final String ATTR_IMPORT_TASK_ID = "importTaskId";

    /**
     * 从Shopify往回迁移
     */
    private static final String ATTR_MIGRATE_FROM_SHOPIFY = "migrateBackFromShopify";

    /**
     * Shopify商品id
     */
    private static final String ATTR_SHOPIFY_PRODUCT = "shopifyProductId";

    public ChannelProductAttributes(String json) {
        super(json);
    }

    public void addPushStore(String store) {
        if(! containsKey(ATTR_PUSH_STORES)) {
            put(ATTR_PUSH_STORES, new JSONArray());
        }

        JSONArray pushStores = get(ATTR_PUSH_STORES, JSONArray.class);
        pushStores.add(store);
    }

    public void delPushStore(String storeToDel) {
        if (!containsKey(ATTR_PUSH_STORES)) {
            return;
        }

        JSONArray pushStores = get(ATTR_PUSH_STORES, JSONArray.class);
        for (int i = 0; i < pushStores.size(); i++) {
            String store = pushStores.getString(i);
            if (store.equals(storeToDel)) {
                pushStores.remove(store);
                put(ATTR_PUSH_STORES, pushStores);
                break;
            }
        }
    }

    public List<String> getPushStores() {
        if(! containsKey(ATTR_PUSH_STORES)) {
            return new ArrayList<>();
        }

        JSONArray pushStores = get(ATTR_PUSH_STORES, JSONArray.class);
        return pushStores.toJavaList(String.class);
    }

    public void setCurrency(String currency) {
        put(ATTR_CURRENCY, currency);
    }

    public String getCurrency() {
        return getAsString(ATTR_CURRENCY);
    }

    public void setLang(String lang) {
        put(ATTR_LANG, lang);
    }

    public String getLang() {
        return getAsString(ATTR_LANG);
    }

    public void setCountry(String country) {
        put(ATTR_COUNTRY, country);
    }

    public String getCountry() {
        return getAsString(ATTR_COUNTRY);
    }

    public void setCommodityId(Long id) {
        put(ATTR_COMMODITY_ID, id);
    }

    public Long getCommodityId() {
        return getAsLong(ATTR_COMMODITY_ID);
    }

    public void setSkuSize(Integer size) {
        put(ATTR_SKU_SIZE, size);
    }

    public Integer getSkuSize() {
        return getAsInteger(ATTR_SKU_SIZE);
    }

    public void setImportTaskId(Long taskId) {
        put(ATTR_IMPORT_TASK_ID, taskId);
    }

    public Long getImportTaskId() {
        return getAsLong(ATTR_IMPORT_TASK_ID);
    }

    public void setMigrateBackFromShopify(Boolean migrateBackFromShopify) {
        if (migrateBackFromShopify != null) {
            put(ATTR_MIGRATE_FROM_SHOPIFY, migrateBackFromShopify);
        }
    }

    public void setShopifyProductId(Long shopifyProductId) {
        put(ATTR_SHOPIFY_PRODUCT, shopifyProductId);
    }

    public Long getShopifyProductId() {
        return getAsLong(ATTR_SHOPIFY_PRODUCT);
    }
}
