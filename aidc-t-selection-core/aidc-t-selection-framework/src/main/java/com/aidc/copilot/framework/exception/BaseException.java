package com.aidc.copilot.framework.exception;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/4/28
 */
@Data
@NoArgsConstructor
public class BaseException extends RuntimeException {
    private String errorCode;

    public BaseException(String errorCode) {
        this.errorCode = errorCode;
    }

    public BaseException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

    public BaseException(String errorCode, String message, Throwable throwable) {
        super(message, throwable);
        this.errorCode = errorCode;
    }
}
