package com.aidc.copilot.framework.metaq;

import com.taobao.metaq.client.MetaProducer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR> @ alibaba-inc.com>
 * @since 2023/5/12
 */

@Configuration
@Slf4j
public class MetaqUnitConfig {

    @Value("${AE_UNIT_TAG}")
    public String VPC_AP_SOURCEEAST_UNIT_TAG;

    @Value("${spring.ons.producer.producer-vpc-ap-sourceeast-id}")
    private String PRODUCER_GROUP;



    @Bean("vpcApSoutheastProducer")
    public MetaProducer initVpcApSoutheastProducer() {
        MetaProducer producer = new MetaProducer(PRODUCER_GROUP);
        if (StringUtils.isNotBlank(this.VPC_AP_SOURCEEAST_UNIT_TAG)) {
            producer.setUnitName(this.VPC_AP_SOURCEEAST_UNIT_TAG);
            producer.setInstanceName(this.VPC_AP_SOURCEEAST_UNIT_TAG);
        }
        try {
            producer.start();
            return producer;
        } catch (Exception e) {
            log.error("init unit producer fail;", e);
        }
        return null;
    }

}
