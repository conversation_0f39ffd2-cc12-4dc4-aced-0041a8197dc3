package com.aidc.copilot.framework.datasource;

import com.alibaba.boot.tddl.builder.datasource.TDataSourceBuilder;
import com.baomidou.mybatisplus.core.config.GlobalConfig;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.taobao.tddl.client.jdbc.TDataSource;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

/**
 * @Description data source config
 * <AUTHOR>
 * @Date 2022/1/19 上午11:41
 **/
@Slf4j
@Configuration
@MapperScan(value = {"com.aidc.copilot.dal.*.mapper"},
        sqlSessionFactoryRef = "sqlSessionFactory")
public class DataSourceConfig {
    @Bean(name = "dataSource", initMethod = "init", destroyMethod = "destroy")
    @Primary
    public TDataSource dataSource() {
        return TDataSourceBuilder.create().appName("AIDC_T_SELECTION_APP").sharding(false).build();
    }

    @Bean(name = "sqlSessionFactory")
    @Primary
    public SqlSessionFactory sqlSessionFactory(DataSource dataSource
            , @Qualifier("mysqlMybatisPlusInterceptor") MybatisPlusInterceptor mybatisPlusInterceptor
            , @Value("classpath:mybatis/mybatis-config.xml") Resource configLocation
            , @Qualifier("mysqlGlobalConfig") GlobalConfig globalConfig
    ) throws Exception {
        MybatisSqlSessionFactoryBean sqlSessionFactoryBean = new MybatisSqlSessionFactoryBean();
        sqlSessionFactoryBean.setDataSource(dataSource);
        sqlSessionFactoryBean.setConfigLocation(configLocation);
        sqlSessionFactoryBean.setGlobalConfig(globalConfig);
        // 设置 mapper xml 路径
        sqlSessionFactoryBean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/*Mapper.xml"));
        Interceptor[] plugins = new Interceptor[]{mybatisPlusInterceptor};
        sqlSessionFactoryBean.setPlugins(plugins);
        return sqlSessionFactoryBean.getObject();
    }

    /**
     * 声明式事务
     *
     * @return 事务管理器
     */
    @Bean(name = "transactionManager")
    @Primary
    public DataSourceTransactionManager transactionManager() {
        return new DataSourceTransactionManager(dataSource());
    }
}
