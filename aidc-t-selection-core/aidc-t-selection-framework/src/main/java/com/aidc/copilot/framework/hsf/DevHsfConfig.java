package com.aidc.copilot.framework.hsf;

import com.alibaba.boot.hsf.annotation.HSFConsumer;
import com.alibaba.global.address.api.facade.UserAddressReadFacade;
import com.alibaba.global.filebroker.api.FileBrokerUploadService;
import com.alibaba.global.ic.api.CustomerProductServiceFacade;
import com.alibaba.global.inventory.api.InventoryMerchantFacade;
import com.alibaba.global.inventory.platform.api.InventoryChannelReadFacade;
import com.alibaba.global.merchant.seller.api.facade.SellerReadFacade;
import com.alibaba.global.order.management.api.facade.OrderQueryForBuyerFacade;
import com.alibaba.global.payment.api.facade.PaymentOpenFacade;
import com.alibaba.global.uop.api.FulfillmentPackageQueryFacade;
import com.alibaba.global.user.api.facade.GlobalSessionFacade;
import com.alibaba.global.user.api.facade.UserBoolTagFacade;
import com.alibaba.global.user.api.facade.UserReadFacade;
import com.alibaba.intl.addressserver.service.remote.interfaces.AddressRemoteService4Ocean;
import com.alibaba.intl.biz.evaluation.share.statistic.interfaces.EvaluationRemoteStatisticService;
import com.aliexpress.price.open.facade.PriceCenterReadFacade;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

@Profile(value = {"testing"})
@Configuration
public class DevHsfConfig {

    // ae address
    @HSFConsumer
    private UserAddressReadFacade userAddressReadFacade;
    @HSFConsumer
    private AddressRemoteService4Ocean addressRemoteService4Ocean;


    // ae order
    @HSFConsumer
    private OrderQueryForBuyerFacade orderQueryForBuyerFacade;
    @HSFConsumer
    private FulfillmentPackageQueryFacade fulfillmentPackageQueryFacade;
    @HSFConsumer
    private PaymentOpenFacade paymentOpenFacade;

    // price center
    @HSFConsumer
    PriceCenterReadFacade priceCenterReadFacade;

    // others
    @HSFConsumer
    private FileBrokerUploadService fileBrokerUploadService;

    @HSFConsumer
    private UserReadFacade userReadFacade;

    @HSFConsumer
    private UserReadFacade aeUserReadFacade;

    @HSFConsumer
    private GlobalSessionFacade globalSessionFacade;

    @HSFConsumer
    private UserBoolTagFacade userBoolTagFacade;

    @HSFConsumer
    private CustomerProductServiceFacade customerProductServiceFacade;

    @HSFConsumer
    private InventoryMerchantFacade inventoryMerchantFacade;

    @HSFConsumer
    private InventoryChannelReadFacade inventoryChannelReadFacade;

    @HSFConsumer
    private com.alibaba.global.exchange.api.facade.GlobalRateFacade GlobalRateFacade;

    @HSFConsumer
    private EvaluationRemoteStatisticService evaluationRemoteStatisticService;

    @HSFConsumer
    private SellerReadFacade sellerReadFacade;
}
