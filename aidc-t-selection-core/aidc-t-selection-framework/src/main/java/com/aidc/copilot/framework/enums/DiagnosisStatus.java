package com.aidc.copilot.framework.enums;

import com.alibaba.copilot.boot.basic.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 诊断状态
 */
@Getter
@AllArgsConstructor
public enum DiagnosisStatus implements IEnum<DiagnosisStatus> {

    /**
     * 未诊断
     */
    UNPROCESSED,

    /**
     * 诊断中
     */
    PROCESSING,

    /**
     * 已诊断
     */
    PROCESSED,

    /**
     * 诊断失败
     */
    FAILURE,
    ;
}
