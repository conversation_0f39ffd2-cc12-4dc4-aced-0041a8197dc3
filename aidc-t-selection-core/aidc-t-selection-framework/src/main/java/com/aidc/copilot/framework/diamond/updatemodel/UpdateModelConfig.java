package com.aidc.copilot.framework.diamond.updatemodel;

import com.aidc.copilot.framework.annotation.DiamondConfigInfo;
import com.aidc.copilot.framework.diamond.JsonConfigCenter;
import org.springframework.stereotype.Component;

import java.util.Arrays;

/**
 * 更换模特的配置
 *
 * <AUTHOR>
 * @version 2023/6/14
 */
@Component
@DiamondConfigInfo(dataId = "com.aidc.copilot.updatemodel")
public class UpdateModelConfig extends JsonConfigCenter<UpdateModelConfigData> {

    public static final UpdateModelConfigData DEFAULT_VALUE = new UpdateModelConfigData()
            .setModels(Arrays.asList(
                    new UpdateModelConfigData.ModelItem()
                            .setId("black_woman")
                            .setPreviewUrl("https://t-selection-algorithms-image.oss-ap-southeast-1.aliyuncs.com/model/black_woman.jpg")
                            .setPrompt("RAW photo, woman, (high detailed skin), 8k uhd, dslr, high quality, black woman, girl, attractive young woman"),
                    new UpdateModelConfigData.ModelItem()
                            .setId("brown_woman")
                            .setPreviewUrl("https://t-selection-algorithms-image.oss-ap-southeast-1.aliyuncs.com/model/brown_woman.jpg")
                            .setPrompt("pr1yanka, photorealistic, cinematic, a beautiful girl, brown eyes,Indian"),
                    new UpdateModelConfigData.ModelItem()
                            .setId("white_woman")
                            .setPreviewUrl("https://t-selection-algorithms-image.oss-ap-southeast-1.aliyuncs.com/model/white_woman.jpg")
                            .setPrompt("Olivia Holt,masterpiece, best quality,looking at viewer, detailed beautiful face"),
                    new UpdateModelConfigData.ModelItem()
                            .setId("white_man")
                            .setPreviewUrl("https://t-selection-algorithms-image.oss-ap-southeast-1.aliyuncs.com/model/white_man.jpg")
                            .setPrompt("facial portrait, a man, masterpiece, best quality, perfect anatomy, high quality, highly detailed"),
                    new UpdateModelConfigData.ModelItem()
                            .setId("black_man")
                            .setPreviewUrl("https://t-selection-algorithms-image.oss-ap-southeast-1.aliyuncs.com/model/black_man.jpg")
                            .setPrompt("lancereddick, detailed skin texture, cinematic lighting, tee grizzley, photorealism, masterpiece, samuelLjackson"),
                    new UpdateModelConfigData.ModelItem()
                            .setId("brown_man")
                            .setPreviewUrl("https://t-selection-algorithms-image.oss-ap-southeast-1.aliyuncs.com/model/brown_man.jpg")
                            .setPrompt("Vijay, Thalapathi,best quality, masterpiece, ultra high res,detailed background,solo,male, Suriya")
            ));

    public UpdateModelConfig() {
        super();
        setData(DEFAULT_VALUE);
    }
}
