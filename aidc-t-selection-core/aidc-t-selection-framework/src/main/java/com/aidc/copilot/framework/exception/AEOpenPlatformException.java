package com.aidc.copilot.framework.exception;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * AE 开放平台特定异常-增加data字段
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class AEOpenPlatformException extends BaseException {

    private String data;

    public AEOpenPlatformException(String errorCode) {
        super(errorCode);
    }

    public AEOpenPlatformException(String errorCode, String message) {
        super(errorCode, message);
    }

    public AEOpenPlatformException(String errorCode, String message, Throwable throwable) {
        super(errorCode, message, throwable);
    }

    public AEOpenPlatformException(TSelectionExceptionEnum exceptionEnum) {
        super(exceptionEnum.getErrorCode(), exceptionEnum.getErrorMessage());
    }

    public AEOpenPlatformException(TSelectionExceptionEnum exceptionEnum, String data) {
        super(exceptionEnum.getErrorCode(), exceptionEnum.getErrorMessage());
        this.data = data;
    }
}
