package com.aidc.copilot.framework.asynctask.worker;

import com.aidc.copilot.framework.asynctask.model.AsyncTask;
import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @desc:
 * @author: yixiao.cx
 * @create: 2023-06-28
 **/
@Slf4j
@Component
public class TestTaskWorker implements AsyncTaskWorker {
    @Override
    public String type() {
        return "testTask";
    }

    @Override
    public int capacity() {
        return 1;
    }

    @Override
    public SingleResult<Object> execute(AsyncTask task) {
        log.info("start to execute test task: {}", JSONObject.toJSONString(task));
        try {
            Thread.sleep(1000 * 10);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        if(task.getExecuteCount() == 1) {
            log.info("test execute exception: {}", JSONObject.toJSONString(task));
            throw new RuntimeException("task error");
        }

        if(task.getExecuteCount() == 2) {
            log.info("test execute fail: {}", JSONObject.toJSONString(task));
            return SingleResult.buildFailure("", "task fail");
        }


        log.info("test execute success: {}", JSONObject.toJSONString(task));
        return SingleResult.buildSuccess("execute ok");
    }
}
