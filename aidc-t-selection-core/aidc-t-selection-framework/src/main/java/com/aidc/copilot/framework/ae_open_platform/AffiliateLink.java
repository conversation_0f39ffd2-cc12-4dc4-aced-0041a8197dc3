package com.aidc.copilot.framework.ae_open_platform;

import com.aidc.copilot.framework.ic.IcService;
import com.aidc.copilot.framework.ic.dto.AffiliateLinkResponseDTO;
import com.alibaba.fastjson.JSONObject;
import com.global.iop.api.IopClient;
import com.global.iop.api.IopClientImpl;
import com.global.iop.api.IopRequest;
import com.global.iop.api.IopResponse;
import com.global.iop.domain.Protocol;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;

@Service
@Slf4j
public class AffiliateLink {

    @Value("${affiliate.link.appkey}")
    private String appKey;

    @Value("${affiliate.link.appSecret}")
    private String appSecret;
    @Value("${affiliate.link.url}")
    private String url;
    @Value("${affiliate.link.apiName}")
    private String apiName;
    private IopClient client;

    @Resource
    private IcService icService;

    @PostConstruct
    public void init() {
        client = new IopClientImpl(url, appKey, appSecret);
    }

    public IopResponse genAffiliateLinks(Map<String, String> params) {
        IopRequest request = new IopRequest();
        request.setApiName(apiName);
        request.addApiParameter("promotion_link_type", params.get("promotion_link_type"));
        request.addApiParameter("source_values", params.get("source_values"));
        request.addApiParameter("tracking_id", params.get("tracking_id"));
        try {
            return client.execute(request, Protocol.TOP);
        } catch (Exception e) {
            log.error("genAffiliateLinks failed", e);
        }
        return null;
    }

    public IopResponse genAffiliateLinkSimple(String sourceUrl) {
        Map<String, String> params = new HashMap<>();
        params.put("source_values", sourceUrl);
        params.put("tracking_id", "dscopilot");
        params.put("promotion_link_type", "2");
        return genAffiliateLinks(params);
    }

    public String getPromotionLinkById(String productId) {
        return null;
    }

    public String getPromotionLink(String url) {
        // TODO: 2023/6/21 处理非联盟品的场景
        return getAffiliateLink(genAffiliateLinkSimple(url));
    }

    private String getAffiliateLink(IopResponse response) {
        try {
            if (Objects.nonNull(response) && Objects.nonNull(response.getGopResponseBody())) {
                AffiliateLinkResponseDTO responseBody = JSONObject.parseObject(response.getGopResponseBody(), AffiliateLinkResponseDTO.class);
                if (Objects.nonNull(responseBody) && Objects.nonNull(responseBody.getRespResult())
                        && responseBody.getRespResult().getRespCode().equals("200")) {
                    if (Objects.nonNull(responseBody.getRespResult().getResult().getPromotionLinks())) {
                        return responseBody.getRespResult().getResult().getPromotionLinks().getPromotionLink();
                    }
                }
            }
        } catch (Exception e) {
            log.error("GetAffiliateLink failed", e);
        }

        return StringUtils.EMPTY;
    }

    public List<String> getPromotionLinksById(List<Long> productIds){
        List<String> temp = new ArrayList<>();
        for (Long id: productIds) {
            temp.add(getPromotionLinkById(id));
        }
        return temp;
    }

    public String getPromotionLinkById(Long productId) {
        String url = icService.getProductById(productId).getDetailPageUrl();
        return getPromotionLink(url);
    }


}
