package com.aidc.copilot.framework.product.model.channel;

import com.aidc.copilot.framework.product.model.ProductImage;
import com.alibaba.copilot.boot.basic.data.BaseObject;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @desc:
 * @author: yixiao.cx
 * @create: 2023-07-07
 **/
@Setter
@Getter
public class ChannelProduct extends BaseObject {
    /**
     * ID
     */
    private Long id;
    /**
     * 创建时间
     */
    private Date gmtCreate;
    /**
     * 修改时间
     */
    private Date gmtModified;
    /**
     * 商家ID
     */
    private Long merchantId;

    /**
     * 来源商品ID
     */
    private Long sourceProductId;

    /**
     * 导入来源
     */
    private String sourcePlatform;

    /**
     * 标题
     */
    private String title;

    /**
     * 图片集合
     */
    private List<ProductImage> images;
    /**
     * 规格
     */
    private String specification;
    /**
     * 描述
     */
    private String descriptionUrl;
    /**
     * 描述文本
     */
    private String descriptionTxt;

    /**
     * 是否已发布
     */
    private Boolean released;

    /**
     * 删除标记
     */
    private Boolean deleted;

    /**
     * 一级类目id
     */
    private Long firstCategoryId;

    /**
     * sku总量
     */
    private Long skuSize;

    /**
     * 商品SKU
     */
    private List<ChannelSku> skus = new ArrayList<>();

    /**
     * 属性（JSON格式）
     */
    private ChannelProductAttributes attributes = new ChannelProductAttributes(null);

    public void setId(Long id) {
        this.id = id;
        refreshSku();
    }

    public void setSkus(List<ChannelSku> skus) {
        if(this.id != null) {
            refreshSku();
        }
        this.skus = skus;
        this.getAttributes().setSkuSize(skus.size());
    }

    public void refreshSku() {
        if(id == null) {
            return;
        }

        // 填充SKU的商品ID
        if(CollectionUtils.isNotEmpty(skus)) {
            skus.forEach(sku -> sku.setChannelProductId(id));
        }
    }
}
