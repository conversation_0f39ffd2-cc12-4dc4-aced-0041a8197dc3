package com.aidc.copilot.framework.chain.ws;

import org.apache.commons.lang3.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @desc:
 * @author: yixiao.cx
 * @create: 2023-06-07
 **/
public class WsSelectionOutputParser implements WsOutputParser<WsSelectionOutput> {
    private static String RECOMMEND_SUMMARY_REGEX = "##\\s*Recommendation\\s*summary\\s*\\n*(.+?)(?:\r\n|\n)*($|##)";
    private static String SEARCH_KEY_REGEX = "##\\s*Search\\s*keys\\s*\n*([\\d|\\D]*?)(\n[^\n]*$|\n*##)";
    private static String OPTION_REGEX = "##\\s*Options\\s*\n*([\\d|\\D]*?)\n*##";
    private static String INTENTION_REGEX = "##\\s*Intention\\s*\n*(.+?)\n*\\[DONE\\]";

    private static String SEARCH_KEY_LINE_REGEX = "\\*\\s*(.+?)\\s*:\\s*(.+)";
    private static String OPTION_LINE_REGEX = "\\*\\s*(.+)";

    private static Pattern RECOMMEND_SUMMARY_PATTERN = Pattern.compile(RECOMMEND_SUMMARY_REGEX);
    private static Pattern SEARCH_KEY_PATTERN = Pattern.compile(SEARCH_KEY_REGEX);
    private static Pattern OPTION_PATTERN = Pattern.compile(OPTION_REGEX);
    private static Pattern INTENTION_PATTERN = Pattern.compile(INTENTION_REGEX);

    private static Pattern SEARCH_KEY_LINE_PATTERN = Pattern.compile(SEARCH_KEY_LINE_REGEX);
    private static Pattern OPTION_LINE_PATTERN = Pattern.compile(OPTION_LINE_REGEX);

    private boolean isRecommendSummaryParseDone = false;
    private boolean isSearchKeysParseDone = false;
    private boolean isOptionsParseDone = false;
    private boolean isIntentionParseDone = false;

    @Override
    public void parse(WsSelectionOutput output) {
        String text = output.getOutput();
        parseRecommendSummary(text, output);
        parseSearchKeys(text, output);
        parseOption(text, output);
        parseIntention(text, output);
    }

    private void parseRecommendSummary(String text, WsSelectionOutput output) {
        if(isRecommendSummaryParseDone) {
            return;
        }

        Matcher matcher = RECOMMEND_SUMMARY_PATTERN.matcher(text);
        if(matcher.find()) {
            String ending = matcher.group(2);
            output.setRecommendSummary(matcher.group(1));
            isRecommendSummaryParseDone = ending.endsWith("##");
        }
    }

    private void parseSearchKeys(String text, WsSelectionOutput output) {
        if(isSearchKeysParseDone) {
            return;
        }

        Matcher matcher = SEARCH_KEY_PATTERN.matcher(text);
        if(! matcher.find()) {
            return;
        }

        String searchKeysText = matcher.group(1);
        String ending = matcher.group(2);

        isSearchKeysParseDone = ending.endsWith("##");
        String[] lines = searchKeysText.split("\n");
        for(int i = output.getSearchKeys().size(); i < lines.length; i++) {
            Matcher lineMatcher = SEARCH_KEY_LINE_PATTERN.matcher(lines[i]);
            if(! lineMatcher.find()) {
                continue;
            }

            int index = output.getSearchKeys().size();
            String key = lineMatcher.group(1);
            String reason = lineMatcher.group(2);
            output.getSearchKeys().add(new WsSelectionOutput.SearchKey(index, key, reason));
        }
    }

    private void parseOption(String text, WsSelectionOutput output) {
        if(isOptionsParseDone) {
            return;
        }

        Matcher matcher = OPTION_PATTERN.matcher(text);
        if(! matcher.find()) {
            return;
        }

        isOptionsParseDone = true;
        String optionsText = matcher.group(1);
        String[] lines = optionsText.split("\n");

        for(int i = output.getOptions().size(); i < lines.length; i++) {
            Matcher lineMatcher = OPTION_LINE_PATTERN.matcher(lines[i]);
            if(! lineMatcher.find() || StringUtils.isBlank(lineMatcher.group(1))) {
                continue;
            }

            String option = lineMatcher.group(1);
            output.getOptions().add(option);
        }
    }

    private void parseIntention(String text, WsSelectionOutput output) {
        if(isIntentionParseDone) {
            return;
        }

        Matcher matcher = INTENTION_PATTERN.matcher(text);
        if(matcher.find()) {
            output.setIntention(matcher.group(1));
            isIntentionParseDone = true;
        }
    }
}
