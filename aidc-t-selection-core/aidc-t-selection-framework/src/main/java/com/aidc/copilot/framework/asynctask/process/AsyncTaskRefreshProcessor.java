package com.aidc.copilot.framework.asynctask.process;

import com.aidc.copilot.framework.asynctask.model.AsyncTask;
import com.aidc.copilot.framework.asynctask.model.AsyncTaskStatus;
import com.aidc.copilot.framework.asynctask.repository.AsyncTaskRepository;
import com.alibaba.copilot.boot.monitor.annotation.Monitor;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.taobao.eagleeye.EagleEye;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * @desc: 异步任务刷新
 * @author: yixiao.cx
 * @create: 2023-06-27
 **/
@Slf4j
@Component
public class AsyncTaskRefreshProcessor extends JavaProcessor {
    private static final int PAGE_NUM = 1;
    private static final int PAGE_SIZE = 100;

    @Resource
    private AsyncTaskRepository asyncTaskRepository;

    @Override
    @Monitor(name = "异步任务刷新", level = Monitor.Level.P1, layer = Monitor.Layer.OTHER)
    public ProcessResult process(JobContext context) throws Exception {
        try {
            EagleEye.startTrace(null, this.getClass().getName(), EagleEye.TYPE_CUSTOM_MESSAGE_SUB);
            List<AsyncTask> lostHeartbeatTasks = asyncTaskRepository.queryLoseHeartbeatTasks(PAGE_NUM, PAGE_SIZE);
            lostHeartbeatTasks.forEach(this::handleLoseHeartbeatTask);

            List<AsyncTask> retryTasks = asyncTaskRepository.queryNeedRetryTasks(PAGE_NUM, PAGE_SIZE);
            retryTasks.forEach(this::handleRetryTask);

            return new ProcessResult(true);
        } catch (Throwable e) {
            log.error("process error", e);
            return new ProcessResult(false);
        } finally {
            EagleEye.endTrace(null);
        }
    }

    private void handleLoseHeartbeatTask(AsyncTask task) {
        log.info("task lost heartbeat, taskId={}, taskType={}", task.getId(), task.getType());
        if(task.isExpired()) {
            AsyncTaskStatus status = task.getExecuteCount() == 0 ? AsyncTaskStatus.EXPIRED : AsyncTaskStatus.FAILURE;
            asyncTaskRepository.updateTaskStatus(task, status);
        } else {
            asyncTaskRepository.updateTaskStatus(task, AsyncTaskStatus.SUSPEND);
        }
    }

    private void handleRetryTask(AsyncTask task) {
        log.info("reset task status for retry, taskId={}, taskType={}", task.getId(), task.getType());
        asyncTaskRepository.updateTaskStatus(task, AsyncTaskStatus.SUSPEND);
    }
}
