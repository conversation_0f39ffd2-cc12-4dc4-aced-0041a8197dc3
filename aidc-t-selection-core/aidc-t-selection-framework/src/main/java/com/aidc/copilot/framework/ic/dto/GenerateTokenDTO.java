package com.aidc.copilot.framework.ic.dto;

import lombok.Data;

/**
 * @Author: mianyun.yt
 * @Date: 2023/5/8
 */
@Data
public class GenerateTokenDTO {
    private Number refresh_token_valid_time;
    private String havana_id;
    private String code;
    private Number expire_time;
    private String locale;
    private String user_nick;
    private String access_token;
    private String refresh_token;
    private String account_id;
    private String user_id;
    private String account_platform;
    private Number refresh_expires_in;
    private Number expires_in;
    private String sp;
    private String request_id;
    private String seller_id;
    private String account;
}
