package com.aidc.copilot.framework.ic.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

/**
 * @Author: mianyun.yt
 * @Date: 2023/5/4
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class MainSiteSearchRes {

    private Object layoutInfo;

    private Mods mods;

    private double _cost;

    private List<Object> templates;

    private Object pageInfo;

    private String _host;

    private int version;

    private String tpp_trace;

    private String tpp_buckets;

    private String pvid;

    private Object modsStyle;

    private String scm;

    private List<String> srpResult;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Mods {

        private ItemListObj itemList;

    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ItemListObj {

        private List<ItemDTO> content;

    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ItemDTO {

        private String itemType;
        private String productType;
        private String tItemType;
        private String nativeCardType;
        private String itemCardType;
        private String productId;
        private String lunchTime;
        private Image image;
        private Title title;
        private Prices prices;
        private List<Object> sellingPoints;
        // private Evaluation evaluation;
//        private Trade trade;
        private List<FeedBackView> feedBackViews;
//        private Trace trace;

    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class FeedBackView {

        private String title;

        private String appId;

        private Object extInfo;

        private FeedBackViewTrace trace;

    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class FeedBackViewTrace {

        private Long itemId;

        private String contentName;

    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Image {

        private String imgUrl;

        private Integer imgWidth;

        private Integer imgHeight;

        private String imgType;

    }

    /**
     * 标题. 产品设计上一些品类没有标题，比如：skirts
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Title {

        private String displayTitle;

        private Boolean shortTitle;

    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Prices {
        private String skuId;
        private String pricesStyle;
        private String builderType;
        private String currencySymbol;
        private String prefix;
        private SalePrice salePrice;
        private String taxRate;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class SalePrice {
        private int discount;
        private int minPriceDiscount;
        private String priceType;
        private String currencyCode;
        private double minPrice;
        private int minPriceType;
        private String formattedPrice;
    }

}

