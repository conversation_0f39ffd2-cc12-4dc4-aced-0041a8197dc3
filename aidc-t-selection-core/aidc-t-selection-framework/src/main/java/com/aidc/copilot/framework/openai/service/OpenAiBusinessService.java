package com.aidc.copilot.framework.openai.service;

import java.io.IOException;
import java.util.List;

import com.aidc.copilot.framework.openai.dto.RecommendPromptKey;

/**
 * @ClassName OpenAiBusinessService
 * <AUTHOR>
 * @Date 2023/6/10 16:37
 */
public interface OpenAiBusinessService {

    /**
     * 自动生成推荐标题和原因
     *
     * @param oldTitle
     * @param keyWords
     * @param modalParticle
     * @return
     */
    List<String> autoGenerateTitleReason(String oldTitle, List<String> keyWords, String modalParticle, Long minWords);

    /**
     * 自动生成副标题
     *
     * @param oldDesc
     * @return
     */
    List<String> autoGenerateSubHead(String oldDesc);

    /**
     * 自动生成新富文本信息
     *
     * @param title
     * @param oldDesc
     * @param keyWords
     * @param modalParticle
     * @param subHead
     * @return
     */
    String autoGenerateDesc(String title, String oldDesc, List<String> keyWords, String modalParticle, List<String> subHead)
        throws IOException;


    /**
     * 自动提炼默认关键词提取
     *
     * @param title
     * @return
     */
    String autoExtractionSearchKeyword(String title);

    /**
     * 基于首页prompt获取对应的key和recommendReason
     * @param prompt
     * @return
     */
    List<RecommendPromptKey> autoRecommendPromptKeyDto(String prompt);

    /**
     * 生成风格标准的周报
     *
     * @param weeklyNewspaperDetail
     * @param promptTemplate
     * @return
     */
    String generateWeeklyNewspaper(String weeklyNewspaperDetail,String promptTemplate,String generateModel)
        throws Exception;


    /**
     * 生成风格标准的周报
     *
     * @param weeklyNewspaperDetail
     * @param promptTemplate
     * @return
     */
    String getMyWeeklyNewspaperTemplate(String weeklyNewspaperDetail)
        throws Exception;



}
