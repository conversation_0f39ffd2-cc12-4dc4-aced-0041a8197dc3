package com.aidc.copilot.framework.email;

import com.alibaba.copilot.edm.client.seocopilot.request.EdmActivityTriggerRequest;
import com.alibaba.copilot.edm.client.seocopilot.EdmDynamicClient;
import com.alibaba.fastjson.JSON;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Service
public class EdmService {
    public static final String USER_NAME = "userName";

    public static final String REGISTERED_DAYS = "registeredDays";

    public static final String AI_TIMES = "aiTimes";

    public static final String TOTAL_REGISTERED_USERS = "totalRegisteredUsers";


    public static final String DATE = "date";

    public void triggerEmail(Long id, String email, Object params ) {
        EdmActivityTriggerRequest request = new EdmActivityTriggerRequest()
                .setToEmail(email)
                .setId(id)
                .setParams(params);
        EdmDynamicClient edmDynamicClient = new EdmDynamicClient();
        edmDynamicClient.sendEmail(request);
    }

    public void triggerEmail(EdmActivityTriggerRequest request) {
        EdmDynamicClient edmDynamicClient = new EdmDynamicClient();
        edmDynamicClient.sendEmail(request);
    }

}
