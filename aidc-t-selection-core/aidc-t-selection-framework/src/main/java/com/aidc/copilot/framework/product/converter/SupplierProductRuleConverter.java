package com.aidc.copilot.framework.product.converter;

import com.aidc.copilot.dal.product.dataobject.SupplierProductRuleDO;
import com.aidc.copilot.framework.product.model.supplier.SupplierProductRule;
import com.aidc.copilot.framework.product.model.supplier.SupplierProductRuleAttributes;
import com.aidc.copilot.framework.product.model.supplier.SupplierProductRuleType;
import com.alibaba.copilot.boot.basic.factory.Converter;
import org.springframework.beans.BeanUtils;

/**
 * @desc:
 * @author: yixiao.cx
 * @create: 2023-07-07
 **/
public class SupplierProductRuleConverter implements Converter<SupplierProductRuleDO, SupplierProductRule> {

    public static final Converter<SupplierProductRuleDO, SupplierProductRule> INSTANCE = new SupplierProductRuleConverter();

    private static final String[] ignoreProperties = new String[]{"attributes", "type"};

    @Override
    public SupplierProductRule convertA2B(SupplierProductRuleDO supplierProductRuleDO) {
        if (supplierProductRuleDO == null) {
            return null;
        }
        SupplierProductRule supplierProductRule = new SupplierProductRule();
        BeanUtils.copyProperties(supplierProductRuleDO, supplierProductRule, ignoreProperties);
        supplierProductRule.setType(SupplierProductRuleType.valueOf(supplierProductRuleDO.getType()));
        supplierProductRule.setAttributes(new SupplierProductRuleAttributes(supplierProductRuleDO.getAttributes()));

        return supplierProductRule;
    }

    @Override
    public SupplierProductRuleDO convertB2A(SupplierProductRule supplierProductRule) {
        if (supplierProductRule == null) {
            return null;
        }
        SupplierProductRuleDO supplierProductRuleDO = new SupplierProductRuleDO();
        BeanUtils.copyProperties(supplierProductRule, supplierProductRuleDO, ignoreProperties);
        supplierProductRuleDO.setType(supplierProductRule.getType().name());
        supplierProductRuleDO.setAttributes(supplierProductRule.getAttributes().toString());

        return supplierProductRuleDO;
    }
}
