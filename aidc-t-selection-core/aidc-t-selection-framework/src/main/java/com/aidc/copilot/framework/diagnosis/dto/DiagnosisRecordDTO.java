package com.aidc.copilot.framework.diagnosis.dto;

import com.aidc.copilot.framework.enums.DiagnosisStatus;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class DiagnosisRecordDTO {

    private Long id;

    /**
     * 店铺
     */
    private String shopDomain;

    /**
     * 操作者id
     */
    private Long merchantId;

    /**
     * 执行批次
     */
    private Integer batch;

    /**
     * 诊断结果
     */
    private String result;

    /**
     * 运行信息
     * ● 失败的商品id
     * ● 失败的原因
     * ● DS导入的商品数量
     * ● 其它渠道导入的商品数量
     */
    private String operation;

    /**
     * 诊断结束时间
     */
    private Date endTime;

    /**
     * 诊断状态
     */
    private DiagnosisStatus status;

    /**
     * 逻辑删除
     */
    private boolean deleted;

    /**
     * 扩展字段
     */
    private DiagnosisRecordAttributes attributes = new DiagnosisRecordAttributes(null);
}
