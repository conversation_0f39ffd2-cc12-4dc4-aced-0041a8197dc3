//package com.aidc.copilot.framework.commodity.impl;
//
//import com.aidc.copilot.framework.commodity.CategoryService;
//import com.aidc.copilot.framework.commodity.dto.CategoryPropertyDTO;
//import com.alibaba.global.category.StandardCategoryClient;
//import com.alibaba.global.category.domain.entity.StdCategoryDTO;
//import com.alibaba.global.category.domain.entity.StdCategoryPropertyDTO;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Service;
//import org.springframework.util.CollectionUtils;
//
//import java.util.List;
//import java.util.stream.Collectors;
//
///**
// * @Author: mianyun.yt
// * @Date: 2023/5/4
// */
//@Service
//@Slf4j
//public class CategoryServiceImpl implements CategoryService {
//
////    private final StandardCategoryClient categoryClient;
//
////    public CategoryServiceImpl(StandardCategoryClient categoryClient) {
////        this.categoryClient = categoryClient;
////    }
//
//    @Override
//    public List<CategoryPropertyDTO> getCategoryProperties(Long catId) {
//
//        try {
////            StdCategoryDTO stdCategory = categoryClient.getStdCategory(catId);
//            StdCategoryDTO stdCategory = null;
//            if (stdCategory == null) {
//                return null;
//            }
//            List<StdCategoryPropertyDTO> propertyDTOList = stdCategory.getAllCategoryProperties();
//            if (!CollectionUtils.isEmpty(propertyDTOList)) {
//                return propertyDTOList.stream().map(propertyDTO -> {
//                    CategoryPropertyDTO categoryPropertyDTO = new CategoryPropertyDTO();
//                    categoryPropertyDTO.setName(propertyDTO.getName());
//                    categoryPropertyDTO.setValues(propertyDTO.getValues());
//                    return categoryPropertyDTO;
//                }).collect(Collectors.toList());
//            }
//        } catch (Exception e) {
//            log.error(String.format("getCategoryProperties error: %s", catId), e);
//        }
//
//        return null;
//    }
//
//}
