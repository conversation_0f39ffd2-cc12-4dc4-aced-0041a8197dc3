package com.aidc.copilot.framework.enums;

import lombok.Getter;

/**
 * @ClassName OrderingRuleEnum
 * <AUTHOR>
 * @Date 2023/10/16 15:27
 */
@Getter
public enum OrderingRuleEnum {

    FEATURED("FEATURED"),

    NEWEST_ARRIVALS("NEWEST_ARRIVALS"),

    ORDERS("ORDERS"),

    PRICE("PRICE"),

    DS_CNT_6M("DS_CNT_6M");

    /**
     * 排序编码
     */
    private final String code;

    OrderingRuleEnum(String code) {
        this.code = code;
    }
}
