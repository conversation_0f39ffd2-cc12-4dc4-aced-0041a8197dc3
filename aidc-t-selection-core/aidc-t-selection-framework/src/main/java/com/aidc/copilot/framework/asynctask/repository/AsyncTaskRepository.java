package com.aidc.copilot.framework.asynctask.repository;

import com.aidc.copilot.dal.asynctask.dataobject.AsyncTaskDO;
import com.aidc.copilot.dal.asynctask.mapper.AsyncTaskMapper;
import com.aidc.copilot.framework.asynctask.model.AsyncTask;
import com.aidc.copilot.framework.asynctask.model.AsyncTaskReadStatus;
import com.aidc.copilot.framework.asynctask.model.AsyncTaskStatus;
import com.aidc.copilot.framework.asynctask.request.AsyncTaskCreateRequest;
import com.aidc.copilot.framework.asynctask.request.AsyncTaskQuery;
import com.aidc.copilot.framework.common.PageData;
import com.aidc.copilot.framework.common.PagingBean;
import com.alibaba.copilot.boot.basic.exception.ErrorCode;
import com.alibaba.copilot.boot.tools.verify.AssertionError;
import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @desc:
 * @author: yixiao.cx
 * @create: 2023-06-27
 **/
@Slf4j
@Repository
public class AsyncTaskRepository {
    @Value("${spring.profiles.active}")
    private String env;
    @Resource
    private AsyncTaskMapper asyncTaskMapper;

    @Value("${DEFAULT.COMMON.SHOP.DOMAIN}")
    private String defaultDomain;

    /**
     * 创建任务
     *
     * @param request
     * @return
     */
    public Long createTask(AsyncTaskCreateRequest request) {
        AsyncTaskDO insertDO = new AsyncTaskDO();
        insertDO.setGmtCreate(new Date());
        insertDO.setGmtModified(new Date());
        insertDO.setEnv(env);
        insertDO.setStore(request.getStore());
        insertDO.setAttributes(request.getAttributes());
        insertDO.setOuterId(request.getOuterId());
        insertDO.setUniqueKey(request.getUniqueKey());
        insertDO.setType(request.getType());
        insertDO.setPriority(1);
        insertDO.setRetryInterval(request.getRetryInterval());
        insertDO.setExecuteCount(0);
        insertDO.setMaxRetryTimes(request.getMaxRetryTimes());
        insertDO.setStartExecuteDate(request.getStartExecuteDate());
        insertDO.setNextExecuteDate(request.getStartExecuteDate());
        insertDO.setExpireDate(request.getExpireDate());
        insertDO.setStatus(AsyncTaskStatus.INITIAL.name());
        insertDO.setReadStatus(AsyncTaskReadStatus.UNREAD.name());
        insertDO.setRequest(((JSONObject) JSONObject.toJSON(request.getRequest())).toJSONString());
        int ret = 0;
        try {
            ret = asyncTaskMapper.insert(insertDO);
        } catch (Exception e) {
            log.error("asyncTask insert error", e);
            throw new AssertionError(ErrorCode.SYS_ERROR, "create task fail");
        }
        Assertor.assertParam(ret == 1, "create task fail");
        return insertDO.getId();
    }

    /**
     * 查询任务
     *
     * @param taskId
     * @return
     */
    public AsyncTask queryTaskById(Long taskId) {
        QueryWrapper<AsyncTaskDO> query = new QueryWrapper<>();
        query.eq("id", taskId);
        AsyncTaskDO taskDO = asyncTaskMapper.selectOne(query);
        return taskDO == null ? null : AsyncTask.of(taskDO);
    }


    /**
     * 查询任务
     *
     * @param uniqueKey
     * @return
     */
    public AsyncTask queryTaskByUniqueKey(String uniqueKey) {
        QueryWrapper<AsyncTaskDO> query = new QueryWrapper<>();
        query.eq("unique_key", uniqueKey);
        AsyncTaskDO taskDO = asyncTaskMapper.selectOne(query);
        return taskDO == null ? null : AsyncTask.of(taskDO);
    }

    /**
     * 查询任务
     *
     * @param outerId
     * @param taskType
     * @return
     */
    public List<AsyncTask> queryTaskByOuterId(String outerId, String taskType) {
        if (StringUtils.isBlank(outerId)) {
            return new ArrayList<>();
        }

        QueryWrapper<AsyncTaskDO> query = new QueryWrapper<>();
        query.eq("outer_id", outerId);
        query.eq(StringUtils.isNotBlank(taskType), "type", taskType);
        List<AsyncTaskDO> taskDOS = asyncTaskMapper.selectList(query);
        return taskDOS.stream().map(AsyncTask::of).collect(Collectors.toList());
    }

    /**
     * 查询任务
     *
     * @param query
     * @param page
     * @return
     */
    public PageData<AsyncTask> queryTasks(AsyncTaskQuery query, PagingBean page) {
        Page<AsyncTaskDO> queryPage = new Page<>(page.getPageIndex(), page.getPageSize());

        QueryWrapper<AsyncTaskDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(query.getTaskId() != null, "id", query.getTaskId());
        queryWrapper.eq(query.getStore() != null, "store", query.getStore());
        queryWrapper.eq(query.getOuterId() != null, "outer_id", query.getOuterId());
        queryWrapper.eq(query.getType() != null, "type", query.getType());
        queryWrapper.eq(query.getUniqueKey() != null, "unique_key", query.getUniqueKey());
        queryWrapper.eq(query.getStatus() != null, "status", query.getStatus());
        queryWrapper.eq(query.getReadStatus() != null, "read_status", query.getReadStatus());

        Page<AsyncTaskDO> selectResult = asyncTaskMapper.selectPage(queryPage, queryWrapper);
        List<AsyncTask> asyncTasks = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(selectResult.getRecords())) {
            asyncTasks = selectResult.getRecords().stream().map(AsyncTask::of).collect(Collectors.toList());
        }

        PageData<AsyncTask> result = new PageData<>();
        result.setData(asyncTasks);
        result.setTotal(selectResult.getTotal());
        result.setTotalPage(selectResult.getPages());
        result.setPageSize(selectResult.getSize());
        result.setPageNum(selectResult.getCurrent());

        return result;
    }

    /**
     * 获取当前可执行的任务
     *
     * @param pageNum
     * @param pageSize
     * @return
     */
    public List<AsyncTask> queryExecutableTasks(Integer pageNum, Integer pageSize) {
        List<String> executableStatus = Lists.newArrayList(AsyncTaskStatus.INITIAL.name(), AsyncTaskStatus.SUSPEND.name());
        QueryWrapper<AsyncTaskDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("env", env);
        queryWrapper.in("status", executableStatus);
        queryWrapper.le("next_execute_date", new Date());

        Page<AsyncTaskDO> queryPage = new Page<>(pageNum, pageSize);
        Page<AsyncTaskDO> selectResult = asyncTaskMapper.selectPage(queryPage, queryWrapper);
        if (CollectionUtils.isEmpty(selectResult.getRecords())) {
            return new ArrayList<>();
        }
        return selectResult.getRecords().stream().map(AsyncTask::of).collect(Collectors.toList());

        //List<AsyncTaskDO> taskDOS = asyncTaskMapper.selectExecutableTasks(new Date(), pageNum, pageSize);
        //return taskDOS.stream().map(AsyncTask::of).collect(Collectors.toList());
    }

    /**
     * 获取失去心跳的执行任务
     *
     * @param pageNum
     * @param pageSize
     * @return
     */
    public List<AsyncTask> queryLoseHeartbeatTasks(Integer pageNum, Integer pageSize) {
        // 20秒没有心跳
        Date activeDate = new Date(System.currentTimeMillis() - 15 * 1000);

        QueryWrapper<AsyncTaskDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("env", env);
        queryWrapper.in("status", Lists.newArrayList(
                AsyncTaskStatus.PENDING.name(),
                AsyncTaskStatus.RUNNING.name()
        ));
        queryWrapper.le("heartbeat_date", activeDate);

        Page<AsyncTaskDO> queryPage = new Page<>(pageNum, pageSize);
        Page<AsyncTaskDO> selectResult = asyncTaskMapper.selectPage(queryPage, queryWrapper);
        if (CollectionUtils.isEmpty(selectResult.getRecords())) {
            return new ArrayList<>();
        }
        return selectResult.getRecords().stream().map(AsyncTask::of).collect(Collectors.toList());
    }

    /**
     * 获取需要重试的任务
     *
     * @param pageNum
     * @param pageSize
     * @return
     */
    public List<AsyncTask> queryNeedRetryTasks(Integer pageNum, Integer pageSize) {
        QueryWrapper<AsyncTaskDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("env", env);
        queryWrapper.eq("status", AsyncTaskStatus.FAILURE.name());
        queryWrapper.gt("expire_date", new Date());
        queryWrapper.apply("max_retry_times > execute_count - 1");

        Page<AsyncTaskDO> queryPage = new Page<>(pageNum, pageSize);
        Page<AsyncTaskDO> selectResult = asyncTaskMapper.selectPage(queryPage, queryWrapper);
        if (CollectionUtils.isEmpty(selectResult.getRecords())) {
            return new ArrayList<>();
        }
        return selectResult.getRecords().stream().map(AsyncTask::of).collect(Collectors.toList());
    }

    /**
     * 查询当前执行的任务数量
     *
     * @param taskType
     * @return
     */
    public Long queryRunningTaskCount(String taskType) {
        if (StringUtils.isBlank(taskType)) {
            return 0L;
        }

        QueryWrapper<AsyncTaskDO> query = new QueryWrapper<>();
        query.eq("type", taskType)
                .eq("status", AsyncTaskStatus.RUNNING.name());
        return asyncTaskMapper.selectCount(query);
    }

    /**
     * 更新任务心跳时间
     *
     * @param taskId
     * @return
     */
    public boolean updateTaskHeartbeatDate(Long taskId) {
        UpdateWrapper<AsyncTaskDO> update = new UpdateWrapper<>();
        update.eq("id", taskId);
        update.eq("status", AsyncTaskStatus.RUNNING.name());

        update.set("gmt_modified", new Date());
        update.set("heartbeat_date", new Date());
        return 1 == asyncTaskMapper.update(null, update);
    }

    /**
     * 更新任务状态
     *
     * @param task
     * @param newStatus
     * @return
     */
    public boolean updateTaskStatus(AsyncTask task, AsyncTaskStatus newStatus) {
        return updateTaskStatusAndResult(task, newStatus, null);
    }

    /**
     * 更新任务状态和结果
     *
     * @param task
     * @param newStatus
     * @return
     */
    public boolean updateTaskStatusAndResult(AsyncTask task, AsyncTaskStatus newStatus) {
        return updateTaskStatusAndResult(task, newStatus, task.getResult());
    }

    private boolean updateTaskStatusAndResult(AsyncTask task, AsyncTaskStatus newStatus, String result) {
        UpdateWrapper<AsyncTaskDO> update = new UpdateWrapper<>();
        update.eq("id", task.getId());
        update.eq("status", task.getStatus());

        Date now = new Date();
        Integer executeCount = task.getExecuteCount();
        Date nextExecuteDate = new Date(System.currentTimeMillis() + task.getRetryInterval());

        update.set("gmt_modified", now);
        update.set("status", newStatus.name());
        if (newStatus == AsyncTaskStatus.INITIAL) {
            executeCount = 0;
            update.set("execute_count", executeCount);
        }
        if (newStatus == AsyncTaskStatus.PENDING) {
            update.set("heartbeat_date", now);
            update.set("execute_count", executeCount);
        }
        if (newStatus == AsyncTaskStatus.RUNNING) {
            executeCount += 1;
            update.set("heartbeat_date", now);
            update.set("execute_count", executeCount);
        }
        if (newStatus == AsyncTaskStatus.SUSPEND) {
            update.set("next_execute_date", nextExecuteDate);
        }
        if (result != null) {
            if (newStatus == AsyncTaskStatus.SUCCESS || newStatus == AsyncTaskStatus.FAILURE) {
                update.set("result", result);
            }
        }

        if (StringUtils.isNotBlank(task.getAttributes())) {
            update.set("attributes", task.getAttributes());
        }

        int ret = asyncTaskMapper.update(null, update);
        if (ret == 1) {
            task.setGmtModified(now);
            task.setHeartbeatDate(now);
            task.setExecuteCount(executeCount);
            task.setNextExecuteDate(nextExecuteDate);
            task.setStatus(newStatus.name());
            return true;
        }

        return false;
    }

    /**
     * @param storeDomain 店铺域名
     * @param recent      最近时间范围
     * @return 查询店铺在指定时间范围内的全部的任务组数量
     */
    public Long queryAllTaskGroupCountByRecent(String storeDomain, Duration recent) {
        return queryTaskGroupCountByRecentAndStatus(storeDomain, recent, null);
    }

    /**
     * @param storeDomain 店铺域名
     * @param recent      最近时间范围
     * @return 查询店铺在指定时间范围内的正在运行的任务组数量
     */
    public Long queryRunningTaskGroupCountByRecent(String storeDomain, Duration recent) {
        List<AsyncTaskStatus> runningStatuses = Arrays.asList(
                AsyncTaskStatus.INITIAL,
                AsyncTaskStatus.PENDING,
                AsyncTaskStatus.RUNNING,
                AsyncTaskStatus.SUSPEND
        );
        return queryTaskGroupCountByRecentAndStatus(storeDomain, recent, runningStatuses);
    }

    /**
     * @param storeDomain 店铺域名
     * @param recent      最近时间范围
     * @param statuses    指定状态范围, 为null或empty时不使用该过滤条件
     * @return 查询店铺在指定时间范围内和状态的任务组数量
     */
    public Long queryTaskGroupCountByRecentAndStatus(String storeDomain, Duration recent, List<AsyncTaskStatus> statuses) {
        Validate.notNull(recent, "Param recent is null");

        Date now = new Date();
        Date startTime = new Date(now.getTime() - recent.toMillis());

        LambdaQueryWrapper<AsyncTaskDO> queryWrapper = new QueryWrapper<AsyncTaskDO>()
                .select("distinct outer_id").lambda()
                .eq(AsyncTaskDO::getStore, storeDomain)
                .ge(AsyncTaskDO::getGmtCreate, startTime);

        if (CollectionUtils.isNotEmpty(statuses)) {
            List<String> statusNames = statuses.stream()
                    .map(AsyncTaskStatus::name)
                    .collect(Collectors.toList());
            queryWrapper = queryWrapper.in(AsyncTaskDO::getStatus, statusNames);
        }
        return asyncTaskMapper.selectCount(queryWrapper);
    }

    /**
     * @param storeDomain 店铺域名
     * @param recent      最近时间范围
     * @return 查询最近时间范围内的任务列表
     */
    public List<AsyncTaskDO> queryTaskListByRecent(String storeDomain, Long userId, Duration recent) {
        Validate.notNull(recent, "Param recent is null");

        // oneshop 虚拟 domain
        List<String> storeDomains = new ArrayList<>();
        storeDomains.add(storeDomain);
        storeDomains.add(userId + defaultDomain);

        Date now = new Date();
        Date startTime = new Date(now.getTime() - recent.toMillis());

        QueryWrapper<AsyncTaskDO> query = new QueryWrapper<AsyncTaskDO>()
                .in("store", storeDomains)
                .ge("gmt_create", startTime)
                .orderBy(true, false, "gmt_create");
        return asyncTaskMapper.selectList(query);
    }
}
