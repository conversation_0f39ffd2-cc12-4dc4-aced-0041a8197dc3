package com.aidc.copilot.framework.chain;

import java.util.List;

import com.aidc.copilot.framework.openai.model.Usage;
import lombok.Data;

/**
 * @ClassName CompletionResult
 * <AUTHOR>
 * @Date 2023/6/1 17:11
 */
@Data
public class CompletionResult {
    /**
     * A unique id assigned to this completion.
     */
    String id;

    /**
     * The type of object returned, should be "text_completion"
     */
    String object;

    /**
     * The creation time in epoch seconds.
     */
    long created;

    /**
     * The GPT-3 model used.
     */
    String model;

    /**
     * A list of generated completions.
     */
    List<CompletionChoice> choices;

    /**
     * The API usage for this request
     */
    Usage usage;
}
