package com.aidc.copilot.framework.common;

import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;

/**
 * 灰度切流bean
 */
@Data
public class ToggleWhiteFlow {

    /**
     * 黑名单
     */
    private List<Long> blackUserList = Lists.newArrayList();

    /**
     * 白名单
     */
    private List<Long> whiteUserList = Lists.newArrayList();

    /**
     * 是否allIn（true表示除去黑名单用户全部切换）
     */
    private boolean allIn = false;

    /**
     * 切流千分比
     */
    public static int scale = 0;
}