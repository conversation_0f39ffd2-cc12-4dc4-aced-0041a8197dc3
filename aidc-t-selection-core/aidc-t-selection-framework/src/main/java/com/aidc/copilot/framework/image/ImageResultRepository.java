package com.aidc.copilot.framework.image;

import com.aidc.copilot.dal.image.dataobject.AIImageResultDO;
import com.aidc.copilot.dal.image.mapper.AIImageResultMapper;
import com.aidc.copilot.framework.common.PageData;
import com.aidc.copilot.framework.common.PageResult;
import com.aidc.copilot.framework.enums.AIImageStatus;
import com.aidc.copilot.framework.enums.AIImageType;
import com.aidc.copilot.framework.image.converter.ImageResultConverter;
import com.aidc.copilot.framework.image.dto.ImageResultDTO;
import com.aidc.copilot.framework.image.request.ImageResultQuery;
import com.aidc.copilot.framework.utils.CollectionUtils;
import com.alibaba.copilot.boot.monitor.annotation.Monitor;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 图片处理结果repository
 */
@Repository
public class ImageResultRepository {

    @Resource
    private AIImageResultMapper resultMapper;

    @Monitor(name = "创建或修改图片结果", level = Monitor.Level.P1, layer = Monitor.Layer.REPOSITORY)
    public boolean createOrUpdate(ImageResultDTO resultDTO) {
        AIImageResultDO imageResultDO = ImageResultConverter.INSTANCE.dtoToDO(resultDTO);
        imageResultDO.setGmtModified(new Date());

        if (imageResultDO.getId() != null) {
            int result = resultMapper.updateById(imageResultDO);
            return result == 1L;
        }

        imageResultDO.setGmtCreate(new Date());
        int result = resultMapper.insert(imageResultDO);
        resultDTO.setId(imageResultDO.getId());
        return result == 1L;
    }


    @Monitor(name = "分页获取结果列表", level = Monitor.Level.P1, layer = Monitor.Layer.REPOSITORY)
    public PageResult<ImageResultDTO> queryResultsByPage(ImageResultQuery query, String merchantId, AIImageType taskType) {
        // 查询record记录
        Page<AIImageResultDO> queryPage = new Page<>(query.getPageNum(), query.getPageSize());

        QueryWrapper<AIImageResultDO> queryRecordWrapper = new QueryWrapper<>();
        queryRecordWrapper.eq("deleted", 0);
        queryRecordWrapper.eq("merchant_id", merchantId);
        queryRecordWrapper.eq("type", taskType.name());
        queryRecordWrapper.ne("status", AIImageStatus.FAILED.name());
        queryRecordWrapper.groupBy("record_id");
        queryRecordWrapper.orderBy(true, false, "record_id");

        Page<AIImageResultDO> resultDOPage = resultMapper.selectPage(queryPage, queryRecordWrapper);
        PageResult<ImageResultDTO> pageResult = new PageResult<>();
        pageResult.setSuccess(true);
        pageResult.setData(new PageData<>());

        if (CollectionUtils.isEmpty(resultDOPage.getRecords())) {
            pageResult.getData().setData(Lists.newArrayList());
            return pageResult;
        }

        // 获取当前页面需要展示的recordId
        List<Long> recordIds = resultDOPage.getRecords().stream().filter(Objects::nonNull).map(AIImageResultDO::getRecordId).collect(Collectors.toList());

        QueryWrapper<AIImageResultDO> queryResultWrapper = new QueryWrapper<>();
        queryResultWrapper.eq("deleted", 0);
        queryResultWrapper.in("record_id", recordIds);
        queryResultWrapper.orderBy(true, false, "record_id");
        List<AIImageResultDO> imageResultDOS = resultMapper.selectList(queryResultWrapper);

        pageResult.getData().setData(ImageResultConverter.INSTANCE.doToDtoList(imageResultDOS));
        pageResult.getData().setPageNum(resultDOPage.getCurrent());
        pageResult.getData().setPageSize(resultDOPage.getSize());
        pageResult.getData().setTotal(resultDOPage.getTotal());
        pageResult.getData().setTotalPage(resultDOPage.getPages());
        return pageResult;
    }

    @Monitor(name = "获取结果列表byRecordIds", level = Monitor.Level.P1, layer = Monitor.Layer.REPOSITORY)
    public List<ImageResultDTO> queryResultsByRecordIds(List<Long> recordIds, String merchantId) {
        if(CollectionUtils.isEmpty(recordIds)){
            return Lists.newArrayList();
        }

        QueryWrapper<AIImageResultDO> queryRecordWrapper = new QueryWrapper<>();
        queryRecordWrapper.eq("deleted", 0);
        queryRecordWrapper.eq("merchant_id", merchantId);
        queryRecordWrapper.in("record_id", recordIds);
        queryRecordWrapper.orderByDesc("record_id").orderByAsc("id");

        List<AIImageResultDO> resultDOs = resultMapper.selectList(queryRecordWrapper);
        if (CollectionUtils.isEmpty(resultDOs)) {
            return Lists.newArrayList();
        }

        return ImageResultConverter.INSTANCE.doToDtoList(resultDOs);
    }

    @Monitor(name = "获取结果列表byIds", level = Monitor.Level.P1, layer = Monitor.Layer.REPOSITORY)
    public List<ImageResultDTO> queryResultByIds(List<String> materialIds, String merchantId) {
        QueryWrapper<AIImageResultDO> queryRecordWrapper = new QueryWrapper<>();
        queryRecordWrapper.eq("deleted", 0);
        queryRecordWrapper.eq("merchant_id", merchantId);
        queryRecordWrapper.in("id", materialIds.stream().map(Long::valueOf).collect(Collectors.toList()));

        List<AIImageResultDO> resultDOs = resultMapper.selectList(queryRecordWrapper);
        if (CollectionUtils.isEmpty(resultDOs)) {
            return Lists.newArrayList();
        }

        return ImageResultConverter.INSTANCE.doToDtoList(resultDOs);
    }

    @Monitor(name = "根据原图id查询结果", level = Monitor.Level.P1, layer = Monitor.Layer.REPOSITORY)
    public ImageResultDTO queryByMaterialId(Long merchantId) {

        QueryWrapper<AIImageResultDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", merchantId);
        queryWrapper.eq("deleted", 0);

        return ImageResultConverter.INSTANCE.doToDto(resultMapper.selectOne(queryWrapper));
    }
}
