package com.aidc.copilot.framework.image.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 图片信息
 */
@Data
public class ImageInfoDTO {

    /**
     * url
     */
    private String url;

    /**
     * 抠图评分
     */
    private BigDecimal score;

    public ImageInfoDTO(String url, BigDecimal score) {
        this.url = url;
        this.score = score;
    }

    public ImageInfoDTO(String url) {
        this.url = url;
    }
}
