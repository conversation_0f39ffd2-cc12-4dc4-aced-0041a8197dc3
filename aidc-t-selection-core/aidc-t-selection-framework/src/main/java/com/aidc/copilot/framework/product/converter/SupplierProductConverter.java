package com.aidc.copilot.framework.product.converter;

import com.aidc.copilot.dal.product.dataobject.SupplierProductDO;
import com.aidc.copilot.framework.product.model.ProductImage;
import com.aidc.copilot.framework.product.model.ProductSourcePlatform;
import com.aidc.copilot.framework.product.model.supplier.SupplierProduct;
import com.aidc.copilot.framework.product.model.supplier.SupplierProductAttributes;
import com.aidc.copilot.framework.product.model.supplier.SupplierProductStatus;
import com.alibaba.copilot.boot.basic.factory.Converter;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.BeanUtils;

/**
 * @desc:
 * @author: yixiao.cx
 * @create: 2023-07-07
 **/
public class SupplierProductConverter implements Converter<SupplierProductDO, SupplierProduct> {

    public static final Converter<SupplierProductDO, SupplierProduct> INSTANCE = new SupplierProductConverter();

    private static final String[] ignoreProperties = new String[]{"attributes", "sourcePlatform", "status","images"};

    @Override
    public SupplierProduct convertA2B(SupplierProductDO supplierProductDO) {
        if (supplierProductDO == null) {
            return null;
        }
        SupplierProduct product = new SupplierProduct();
        BeanUtils.copyProperties(supplierProductDO, product, ignoreProperties);
        product.setSourcePlatform(ProductSourcePlatform.valueOf(supplierProductDO.getSourcePlatform()));
        product.setStatus(SupplierProductStatus.valueOf(supplierProductDO.getStatus()));
        product.setImages(JSONObject.parseArray(supplierProductDO.getImages(), ProductImage.class));
        product.setAttributes(new SupplierProductAttributes(supplierProductDO.getAttributes()));

        return product;
    }

    @Override
    public SupplierProductDO convertB2A(SupplierProduct supplierProduct) {
        if (supplierProduct == null) {
            return null;
        }
        SupplierProductDO productDO = new SupplierProductDO();
        BeanUtils.copyProperties(supplierProduct, productDO, ignoreProperties);
        productDO.setSourcePlatform(supplierProduct.getSourcePlatform().name());
        productDO.setStatus(supplierProduct.getStatus().name());
        productDO.setImages(JSONArray.toJSONString(supplierProduct.getImages()));
        productDO.setAttributes(supplierProduct.getAttributes().toString());

        return productDO;
    }
}
