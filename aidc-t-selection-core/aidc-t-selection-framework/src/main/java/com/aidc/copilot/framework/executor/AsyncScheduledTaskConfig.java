package com.aidc.copilot.framework.executor;

import com.alibaba.copilot.boot.tools.thread.ThreadTracePoolExecutor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/5/26
 */
@Slf4j
@Configuration
public class AsyncScheduledTaskConfig {
    @Value("${spring.task.execution.pool.core-size}")
    private int corePoolSize;
    @Value("${spring.task.execution.pool.max-size}")
    private int maxPoolSize;
    @Value("${spring.task.execution.pool.queue-capacity}")
    private int queueCapacity;
    @Value("${spring.task.execution.thread-name-prefix}")
    private String namePrefix;
    @Value("${spring.task.execution.pool.keep-alive}")
    private int keepAliveSeconds;

    @Bean
    public Executor tsPublishToShopifyAsyncTaskExecutor() {
        return ThreadTracePoolExecutor.of(
            namePrefix,
            corePoolSize,
            maxPoolSize,
            keepAliveSeconds,
            new LinkedBlockingQueue(queueCapacity)
        );
    }
}
