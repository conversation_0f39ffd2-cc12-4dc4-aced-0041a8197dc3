package com.aidc.copilot.framework.chain.ws;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName StateMachine
 * <AUTHOR>
 * @Date 2023/6/5 11:23
 */
public class StateMachine {
    private State state;
    private StringBuilder content;

    public StateMachine() {
        this.state = State.START;
        this.content = new StringBuilder();
    }

    public void setState(State newState, boolean appendContent) {
        this.state = newState;
        if (!appendContent) {
            this.content = new StringBuilder();
        }
    }

    public State getState() {
        return this.state;
    }

    public void append(String text) {
        this.content.append(text);
    }

    public String getContent() {
        return this.content.toString();
    }
}
