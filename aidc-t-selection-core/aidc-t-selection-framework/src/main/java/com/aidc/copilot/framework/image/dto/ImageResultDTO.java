package com.aidc.copilot.framework.image.dto;

import com.aidc.copilot.framework.enums.AIImageRelationType;
import com.aidc.copilot.framework.enums.AIImageStatus;
import com.aidc.copilot.framework.enums.AIImageType;
import lombok.Data;

import java.util.Date;

@Data
public class ImageResultDTO {

    private Long id;

    private Date gmtCreate;

    /**
     * 任务记录id
     */
    private Long recordId;

    /**
     * 操作者id
     */
    private String merchantId;

    /**
     * shopDomain
     */
    private String shopDomain;

    /**
     * 图片处理类型（抠图或去水印）
     */
    private AIImageType type;

    /**
     * 原图信息
     */
    private ImageInfoDTO originImage;

    /**
     * 结果图信息
     */
    private ImageInfoDTO resultImage;

    /**
     * 本地图片
     */
    private AIImageRelationType relationType;

    /**
     * 关联实体id（商品、工具）
     */
    private String relationId;

    /**
     * 状态
     */
    private AIImageStatus status;

    /**
     * 逻辑删除
     */
    private boolean deleted;

    /**
     * 扩展字段
     */
    private ImageResultAttributes attributes = new ImageResultAttributes(null);
}
