package com.aidc.copilot.framework.diamond.benefit;

import com.alibaba.common.lang.StringUtil;

import com.aidc.copilot.framework.diamond.AbstractConfigCenter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @ClassName SubscriptionPlansConfig
 * <AUTHOR>
 * @Date 2023/8/7 11:38
 */
@Component
@Slf4j
public class SubscriptionPlansConfig extends AbstractConfigCenter<String> {
    private final String defaultTemplate = "[{\"cycle\":\"Monthly\",\"name\":\"Monthly_Basic_Plan\",\"price\":0,\"priceContent\":\"Free\",\"trialDayContent\":\"Forever Free\",\"trialDays\":-1,\"type\":\"Basic_Plan\"},{\"cycle\":\"Yearly\",\"name\":\"Yearly_Basic_Plan\",\"price\":0,\"priceContent\":\"Free\",\"trialDayContent\":\"Forever Free\",\"trialDays\":-1,\"type\":\"Basic_Plan\"},{\"cycle\":\"Monthly\",\"name\":\"Monthly_Standard_Plan\",\"price\":1290,\"priceContent\":\"$12.90\",\"trialDayContent\":\"7 days free trial\",\"trialDays\":7,\"type\":\"Standard_Plan\"},{\"cycle\":\"Yearly\",\"name\":\"Yearly_Standard_Plan\",\"price\":12900,\"priceContent\":\"$129\",\"trialDayContent\":\"7 days free trial\",\"trialDays\":7,\"type\":\"Standard_Plan\"}]";

    @Override
    protected String getDataId() {
        return "com.aidc.copilot.subscription.plans";
    }

    @Override
    protected void compile(String dataStr) {
        data = dataStr;
    }

    @Override
    public String getData() {
        if (StringUtil.isBlank(super.getData())) {
            return defaultTemplate;
        }
        return super.getData();
    }
}
