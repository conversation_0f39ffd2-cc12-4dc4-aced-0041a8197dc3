package com.aidc.copilot.framework.executor;

import com.aidc.copilot.framework.redis.RedisService;
import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.fastjson.JSON;
import com.dingtalk.open.app.api.GenericEventListener;
import com.dingtalk.open.app.api.OpenDingTalkStreamClientBuilder;
import com.dingtalk.open.app.api.message.GenericOpenDingTalkEvent;
import com.dingtalk.open.app.api.security.AuthClientCredential;
import com.dingtalk.open.app.stream.protocol.event.EventAckStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import shade.com.alibaba.fastjson2.JSONObject;

import javax.annotation.Resource;

@Slf4j
@Configuration
public class OpenDingTalkStreamClientConfig {

//    @Value("${openDing.clientId}")
//    private String clientId;
//
//    @Value("${openDing.clientSecret}")
//    private String clientSecret;

    private static final String REDIS_KEY_PRE = "openDingTalkStreamClient";
    @Resource
    private RedisService redisService;

    public OpenDingTalkStreamClientConfig() {
        try {
            OpenDingTalkStreamClientBuilder
                    .custom()
                    .credential(new AuthClientCredential("ding2ooryvipdg4mimta", "nap7iTNTOMSgGJwOxZXJ1bIurulTurgAwKvWMzKuHzSglcWFOWD8_2-JFeZh2Aqq"))
                    //注册事件监听
                    .registerAllEventListener(new GenericEventListener() {
                        public EventAckStatus onEvent(GenericOpenDingTalkEvent event) {
                            try {
                                //事件唯一Id
                                String eventId = event.getEventId();
                                //事件类型
                                String eventType = event.getEventType();
                                if (eventType != null && eventType.equals("doc_content_export_result")) {
                                    //事件产生时间
                                    Long bornTime = event.getEventBornTime();
                                    //获取事件体
                                    JSONObject bizData = event.getData();
                                    //处理事件
                                    log.info("GenericOpenDingTalkEvent:{} {}", bornTime, JSON.toJSONString(event));
                                    //内容以taskId作为key存入Redis
                                    redisService.writeValueWithExpire(String.format("%s_%d", REDIS_KEY_PRE, bizData.getLong("taskId")), 3600, bizData.getString("content") );
                                }

                                //消费成功
                                return EventAckStatus.SUCCESS;
                            } catch (Exception e) {
                                //消费失败
                                return EventAckStatus.LATER;
                            }
                        }
                    })
                    .build().start();
        } catch (Exception e) {
            log.error("openDingTalkStreamClient failed:", e);
        }
    }
}
