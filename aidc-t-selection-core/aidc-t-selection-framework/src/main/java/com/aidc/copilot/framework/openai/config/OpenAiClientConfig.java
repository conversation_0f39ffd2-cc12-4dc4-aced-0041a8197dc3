package com.aidc.copilot.framework.openai.config;


import com.alibaba.copilot.boot.llm.openai.client.OpenAiBaseClient;
import com.alibaba.copilot.boot.llm.openai.client.OpenAiDefaultClient;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @ClassName OpenAiClientConfig
 * <AUTHOR>
 * @Date 2023/6/13 15:14
 */
@Configuration
public class OpenAiClientConfig {

    @Bean
    public OpenAiBaseClient openAiClient() {
        return new OpenAiDefaultClient("https://openai-keys.alibaba-inc.com", "DsCopilotToken");
    }
}

