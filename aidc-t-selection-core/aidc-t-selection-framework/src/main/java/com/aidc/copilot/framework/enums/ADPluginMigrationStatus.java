package com.aidc.copilot.framework.enums;

import com.alibaba.copilot.boot.basic.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * AD插件迁移状态
 */
@Getter
@AllArgsConstructor
public enum ADPluginMigrationStatus implements IEnum<ADPluginMigrationStatus> {

    /**
     * 迁移中
     */
    PROCESSING,

    /**
     * 迁移完成
     */
    PROCESSED,

    /**
     * 诊断失败
     */
    FAILURE,
    ;
}
