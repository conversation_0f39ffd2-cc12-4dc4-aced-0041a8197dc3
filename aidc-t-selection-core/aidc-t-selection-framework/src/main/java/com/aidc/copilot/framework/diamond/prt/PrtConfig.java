package com.aidc.copilot.framework.diamond.prt;

import com.aidc.copilot.framework.diamond.AbstractConfigCenter;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;

@Slf4j
@Component
public class PrtConfig extends AbstractConfigCenter<PrtConfigData> {
    protected final String clazzSimpleName = getClass().getSimpleName();
    private static final String DATA_ID = "aidc-prt-config";

    @Override
    protected String getDataId() {
        return DATA_ID;
    }

    @Override
    protected void compile(String dataStr) {
        log.info("{}#receiveConfigInfo receive configInfo, configInfo={}", clazzSimpleName, dataStr);
        try {
            JSONArray jsonArray = JSONArray.parseArray(dataStr);
            PrtConfigData prtConfigData = new PrtConfigData();
            List<PrtConfigData.ConfigData> configDataList = new ArrayList<>();
            for (int i = 0; i < jsonArray.size(); i++) {
                configDataList.add(JSON.parseObject(jsonArray.getString(i), PrtConfigData.ConfigData.class));
            }
            prtConfigData.setConfigDataList(configDataList);
            data = prtConfigData;
        } catch (Exception e) {
            log.error("CountryConfig#receiveConfigInfo error", e);
        }
    }
}