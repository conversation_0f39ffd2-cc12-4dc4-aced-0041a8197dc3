package com.aidc.copilot.framework.diamond.updatemodel;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 更换模特的配置数据
 *
 * <AUTHOR>
 * @version 2023/6/14
 */
@Data
@Accessors(chain = true)
public class UpdateModelConfigData implements Serializable {

    /**
     * 模特列表
     */
    private List<ModelItem> models;

    @Data
    public static class ModelItem implements Serializable {

        /**
         * 模特ID
         */
        private String id;

        /**
         * 模特预览图
         */
        private String previewUrl;

        /**
         * 模特正向prompt
         */
        private String prompt;

        /**
         * 模特负向prompt
         */
        private String negativePrompt;
    }
}
