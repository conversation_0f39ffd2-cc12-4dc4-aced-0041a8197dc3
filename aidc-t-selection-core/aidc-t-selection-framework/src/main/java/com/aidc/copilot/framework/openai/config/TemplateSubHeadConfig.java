package com.aidc.copilot.framework.openai.config;

import com.alibaba.common.lang.StringUtil;

import com.aidc.copilot.framework.diamond.AbstractConfigCenter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @ClassName TemplateSubHeadConfig
 * <AUTHOR>
 * @Date 2023/6/14 01:16
 */
@Component
@Slf4j
public class TemplateSubHeadConfig extends AbstractConfigCenter<String> {

    private final String defaultTemplate = "You are a text content assistant for shopify merchants and need to help "
        + "them optimize their text content.\n"
        + "\n"
        + "Let's start by knowing that good subheadings have the following characteristics:\n"
        + "Extract content summary: Understand the whole text content and separate out different paragraphs for "
        + "summary;\n"
        + "Optimize subheadings: Optimize different summaries into shorter subheading words\n"
        + "\n"
        + "Here would be an example:\n"
        + "Text content:\n"
        + "81-key Mechanical Keyboard Kit RGB Backlit DIY Mechanical keyboard Aluminum alloy computer accessories for"
        + " desktop laptops\n"
        + "1. Material: Aluminum alloy shell, multimedia aluminum alloy knob. Note that the key cap is a photographic"
        + " prop, and the product does not contain a key cap.\n"
        + "Origin: Mainland China\n"
        + "Note: Due to different display and light effects\n"
        + "\n"
        + "Subtitle: Features | specifications | Attention\n"
        + "\n"
        + "Text content:\n"
        + "${oldDesc}\n"
        + "\n"
        + "Subtitle:";

    @Override
    protected String getDataId() {
        return "com.aidc.copilot.subHead.template";
    }

    @Override
    protected void compile(String dataStr) {
        data = dataStr;
    }

    @Override
    public String getData() {
        if (StringUtil.isBlank(super.getData())) {
            return defaultTemplate;
        }
        return super.getData();
    }
}
