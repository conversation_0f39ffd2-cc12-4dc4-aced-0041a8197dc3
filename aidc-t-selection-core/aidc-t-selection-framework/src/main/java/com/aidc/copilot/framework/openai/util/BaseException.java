package com.aidc.copilot.framework.openai.util;

import com.aidc.copilot.framework.openai.model.IError;
import lombok.Getter;

/**
 * @<PERSON> yang<PERSON>ye
 * @Date 2023/5/30 21:19
 */
@Getter
public class BaseException extends RuntimeException {

    private String msg;
    private int code;

    public BaseException(IError error) {
        super(error.msg());
        this.code = error.code();
        this.msg = error.msg();
    }

    public BaseException(String msg) {
        super(msg);
        this.code = CommonError.SYS_ERROR.code();
        this.msg = msg;
    }

    public BaseException() {
        super(CommonError.SYS_ERROR.msg());
        this.code = CommonError.SYS_ERROR.code();
        this.msg = CommonError.SYS_ERROR.msg();
    }
}
