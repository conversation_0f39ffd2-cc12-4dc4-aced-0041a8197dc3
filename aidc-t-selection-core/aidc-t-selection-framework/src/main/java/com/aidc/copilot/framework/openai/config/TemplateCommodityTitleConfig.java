package com.aidc.copilot.framework.openai.config;

import com.alibaba.common.lang.StringUtil;

import com.aidc.copilot.framework.diamond.AbstractConfigCenter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @ClassName TemplateTitleConfig
 * <AUTHOR>
 * @Date 2023/6/13 16:37
 */
@Component
@Slf4j
public class TemplateCommodityTitleConfig extends AbstractConfigCenter<String> {

    private final String defaultTemplate = "You are a content optimization assistant for shopify merchants and need "
        + "to help merchants optimize product titles.\n"
        + "\n"
        + "Let's first know that good titles and reasons for recommendation have the following characteristics:\n"
        + "Clear and concise: try to keep the title concise;\n"
        + "Keyword optimization: Include relevant keywords in the title;\n"
        + "Value declaration: Highlight the core values and advantages of the product;\n"
        + "Trigger emotion: the combination of emotional expression to trigger consumers' emotions, stimulate the "
        + "desire to buy.\n"
        + "\n"
        + "Here would be an example:\n"
        + "Original title: The title that the user wants to optimize.\n"
        + "Keywords: Add these keywords to the optimized new title.\n"
        + "Text style: Use style to write new titles.\n"
        + "\n"
        + "\n"
        + "Original title: ${oldTitle}\n"
        + "Key words: ${keyWords}\n"
        + "Text style: ${modalParticle}\n"
        + "New title:";

    @Override
    protected String getDataId() {
        return "com.aidc.copilot.commodityTitle.template";
    }

    @Override
    protected void compile(String dataStr) {
        data = dataStr;
    }

    @Override
    public String getData() {
        if (StringUtil.isBlank(super.getData())) {
            return defaultTemplate;
        }
        return super.getData();
    }
}
