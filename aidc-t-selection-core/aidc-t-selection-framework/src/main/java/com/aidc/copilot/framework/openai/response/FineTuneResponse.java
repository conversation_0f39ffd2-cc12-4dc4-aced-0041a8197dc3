package com.aidc.copilot.framework.openai.response;

import java.io.Serializable;
import java.util.List;

import com.aidc.copilot.framework.openai.model.Event;
import com.aidc.copilot.framework.openai.model.HyperParam;
import com.aidc.copilot.framework.openai.model.TrainingFile;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class FineTuneResponse implements Serializable {

    private String id;

    private String object;

    private String model;

    @JsonProperty("created_at")
    private long createdAt;

    private List<Event> events;

    @JsonProperty("fine_tuned_model")
    private String fineTunedModel;

    @JsonProperty("hyperparams")
    private HyperParam hyperParams;

    @JsonProperty("organization_id")
    private String organizationId;

    @JsonProperty("result_files")
    private List resultFiles;

    private String status;

    @JsonProperty("validation_files")
    private List validationFiles;

    @JsonProperty("training_files")
    private List<TrainingFile> trainingFiles;

    @JsonProperty("updated_at")
    private long updatedAt;


}
