package com.aidc.copilot.framework.image.converter;

import com.aidc.copilot.dal.image.dataobject.AIImageResultDO;
import com.aidc.copilot.framework.enums.AIImageRelationType;
import com.aidc.copilot.framework.enums.AIImageStatus;
import com.aidc.copilot.framework.enums.AIImageType;
import com.aidc.copilot.framework.image.dto.ImageInfoDTO;
import com.aidc.copilot.framework.image.dto.ImageResultAttributes;
import com.aidc.copilot.framework.image.dto.ImageResultDTO;
import com.alibaba.fastjson.JSON;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface ImageResultConverter {
    ImageResultConverter INSTANCE = Mappers.getMapper(ImageResultConverter.class);

    /**
     * DO转换为DTO
     */
    @Mapping(target = "type", source = "type", qualifiedByName = "stringToAiImageType")
    @Mapping(target = "status", source = "status", qualifiedByName = "stringToAiImageStatus")
    @Mapping(target = "relationType", source = "relationType", qualifiedByName = "stringToAiImageRelationType")
    @Mapping(target = "originImage", source = "originImage", qualifiedByName = "fromJSON")
    @Mapping(target = "resultImage", source = "resultImage", qualifiedByName = "fromJSON")
    @Mapping(source = "attributes", target = "attributes", qualifiedByName = "stringToAttributes")
    ImageResultDTO doToDto(AIImageResultDO imageResultDO);

    /**
     * DTO转换为DO
     */
    @Mapping(target = "type", source = "type", qualifiedByName = "aiImageTypeToString")
    @Mapping(target = "status", source = "status", qualifiedByName = "aiImageStatusToString")
    @Mapping(target = "relationType", source = "relationType", qualifiedByName = "aiImageRelationTypeToString")
    @Mapping(target = "originImage", source = "originImage", qualifiedByName = "toJSON")
    @Mapping(target = "resultImage", source = "resultImage", qualifiedByName = "toJSON")
    @Mapping(target = "attributes", qualifiedByName = "attributesToString")
    AIImageResultDO dtoToDO(ImageResultDTO imageResultDTO);

    List<ImageResultDTO> doToDtoList(List<AIImageResultDO> doList);

    List<AIImageResultDO> dtoToDOList(List<ImageResultDTO> dtoList);

    @Named("stringToAttributes")
    default ImageResultAttributes stringToAttributes(String attributes) {
        if (attributes == null) {
            return new ImageResultAttributes(null);
        }
        return JSON.parseObject(attributes, ImageResultAttributes.class);
    }

    @Named("aiImageTypeToString")
    default String aiImageTypeToString(AIImageType type) {
        return type == null ? null : type.toString();
    }

    @Named("stringToAiImageType")
    default AIImageType stringToAiImageType(String type) {
        return type == null ? null : AIImageType.valueOf(type);
    }

    @Named("aiImageStatusToString")
    default String aiImageStatusToString(AIImageStatus type) {
        return type == null ? null : type.toString();
    }

    @Named("stringToAiImageStatus")
    default AIImageStatus stringToAiImageStatus(String type) {
        return type == null ? null : AIImageStatus.valueOf(type);
    }

    @Named("aiImageRelationTypeToString")
    default String aiImageRelationTypeToString(AIImageRelationType type) {
        return type == null ? null : type.toString();
    }

    @Named("stringToAiImageRelationType")
    default AIImageRelationType stringToAiImageRelationType(String type) {
        return type == null ? null : AIImageRelationType.valueOf(type);
    }

    @Named("toJSON")
    default String toJSON(ImageInfoDTO imageInfoDTO) {
        return imageInfoDTO == null ? null : JSON.toJSONString(imageInfoDTO);
    }

    @Named("fromJSON")
    default ImageInfoDTO fromJSON(String json) {
        try {
            return json == null ? null : JSON.parseObject(json, ImageInfoDTO.class);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Named("attributesToString")
    default String attributesToString(ImageResultAttributes attributes) {
        return attributes.toString();
    }
}
