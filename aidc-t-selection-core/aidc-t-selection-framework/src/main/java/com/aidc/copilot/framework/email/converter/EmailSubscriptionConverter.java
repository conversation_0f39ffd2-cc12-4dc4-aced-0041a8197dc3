package com.aidc.copilot.framework.email.converter;

import com.aidc.copilot.dal.email.dataobject.EmailSubscriptionDO;
import com.aidc.copilot.dal.product.dataobject.ChannelProductDO;
import com.aidc.copilot.framework.email.model.EmailSubscription;
import com.alibaba.copilot.boot.basic.factory.Converter;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2024/1/17
 */
@Component
public class EmailSubscriptionConverter implements Converter<EmailSubscriptionDO, EmailSubscription> {
    public static final Converter<EmailSubscriptionDO, EmailSubscription> INSTANCE = new EmailSubscriptionConverter();

    @Override
    public EmailSubscription convertA2B(EmailSubscriptionDO emailSubscriptionDO) {
        if (emailSubscriptionDO == null) {
            return null;
        }

        EmailSubscription emailSubscription = new EmailSubscription();
        emailSubscription.setId(emailSubscriptionDO.getId());
        emailSubscription.setEmail(emailSubscriptionDO.getEmail());
        emailSubscription.setCategory(emailSubscriptionDO.getCategory());
        emailSubscription.setCategoryName(emailSubscriptionDO.getCategoryName());
        emailSubscription.setUserId(emailSubscriptionDO.getUserId());
        emailSubscription.setSaleCountry(emailSubscriptionDO.getSaleCountry());
        emailSubscription.setExclusiveOpportunities(emailSubscriptionDO.getExclusiveOpportunities());

        return emailSubscription;
    }

    @Override
    public EmailSubscriptionDO convertB2A(EmailSubscription emailSubscription) {
        return null;
    }
}
