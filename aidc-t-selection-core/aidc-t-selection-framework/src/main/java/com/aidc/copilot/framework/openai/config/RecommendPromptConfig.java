package com.aidc.copilot.framework.openai.config;

import com.alibaba.common.lang.StringUtil;

import com.aidc.copilot.framework.diamond.AbstractConfigCenter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @ClassName RecommendPromptConfig
 * <AUTHOR>
 * @Date 2023/12/13 21:52
 */
@Component
@Slf4j
public class RecommendPromptConfig extends AbstractConfigCenter<String> {

    private final String defaultTemplate = "You are an assistant for global e-commerce sellers, your task is to help "
        + "them find suitable products on AliExpress.com.\n"
        + "You should know the following knowledge:\n"
        + "* product subcategories: product subcategories are specific classifications or categories within a broader"
        + " product category, for example, within the main category of \"Electronics,\" subcategories could include "
        + "\"Mobile Phones,\" \"Computers,\" \"Cameras,\" and \"Home Appliances.\"\n"
        + "* if the merchant explicitly provides information about product categories or gender-specific products "
        + "that are suitable, make sure to retain and utilize that information in each step.\n"
        + "Let's think about it step by step according to the following steps:\n"
        + "step 1: based on the question  recommend less-than 8 Search keys, do not repeat between categories.If the "
        + "merchant explicitly provides information about product categories or gender-specific products that are "
        + "suitable, make sure these information be reflected in the keywords, and you can add some descriptive "
        + "modifiers to generate new keywords.  These should be listed with a star (*) and not with numbers.\n"
        + "step 2: gives a less-than-20-words recommendation reason for each category.\n"
        + "Never ask me questions or to provide more information.\n"
        + "Never make assumptions.\n"
        + "if the merchant explicitly provides information about product categories or gender-specific products,make "
        + "sure they are reflected in the keywords.\n"
        + "\n"
        + "Example:\n"
        + "Question:I want to open a clothing store for women, please recommend suitable products for sale in the "
        + "store.\n"
        + "Answer:\n"
        + "## Search keys\n"
        + "* loose women's dresses: Loose dresses are comfortable and suitable for various occasions.\n"
        + "* fashion jewelry: Exquisite jewelry can add a touch of elegance and enhance outfits.\n"
        + "* designer scarves: Scarves are versatile accessories that can elevate any look.\n"
        + "* trendy purses for women: Stylish purses are essential for women to carry their belongings.\n"
        + "* purses for women: Stylish purses are essential for women to carry belongings.\n"
        + "* women's casual footwear: Comfortable and chic shoes that can be worn for everyday activities.\n"
        + "* statement sunglasses: Eye-catching and fashionable eyewear that protects the eyes and adds to personal "
        + "style.\n"
        + "* luxury watches for women: High-end timepieces that serve as both a practical accessory and a symbol of "
        + "sophistication.\n"
        + "\n"
        + "Question:${myWord}\n"
        + "Answer:";

    @Override
    protected String getDataId() {
        return "com.aidc.copilot.recommend.prompt.template";
    }

    @Override
    protected void compile(String dataStr) {
        data = dataStr;
    }

    @Override
    public String getData() {
        if (StringUtil.isBlank(super.getData())) {
            return defaultTemplate;
        }
        return super.getData();
    }
}
