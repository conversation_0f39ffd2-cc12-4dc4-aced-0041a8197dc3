package com.aidc.copilot.framework.exception;

import lombok.Getter;

/**
 * @Author: mianyun.yt
 * @Date: 2023/5/29
 */
@Getter
public enum CommodityExceptionEnum {

    /**
     * 系统错误
     */
    PARAM_ERROR("0000", "参数错误"),

    /**
     * 业务错误
     */
    COMMODITY_NOT_EXIST("1001", "dsCopilot not exit, commodityId: %s"),

    /**
     * 底层依赖错误
     */
    IC_PRODUCT_NOT_EXIST("2001", "The product has been removed from the shelves. Please reselect the product"),
    SHOPIFY_PRODUCT_NOT_EXIST("2002", "No Shopify data for this product"),
    SHOPIFY_PRODUCT_FETCH_FAIL("2003", "Shopify product not found on this app"),
    SHOPIFY_PRODUCT_VARIANT_OVER_MAX("2003", "The number of SKUs pushed to Shopify exceeds the limit of 100"),
    SHOPIFY_PRODUCT_DO_NOT_EXIST("2004", "shopifyProductDo not exit, productId: %s"),
    ;

    private final String code;
    private final String message;

    CommodityExceptionEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

}
