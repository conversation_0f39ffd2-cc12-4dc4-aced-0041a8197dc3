<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">

	<parent>
		<groupId>com.aidc.copilot</groupId>
		<artifactId>aidc-t-selection-core</artifactId>
		<version>1.0.0</version>
		<relativePath>../pom.xml</relativePath>
	</parent>

	<modelVersion>4.0.0</modelVersion>
	<artifactId>aidc-t-selection-dal</artifactId>
	<packaging>jar</packaging>
	<name>aidc-t-selection-dal</name>

	<dependencies>
		<!-- 依赖子模块 -->

		<!-- 中间件 -->
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-boot-starter</artifactId>
		</dependency>
		<!-- 二方包 -->

		<!-- 三方包 -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba.copilot</groupId>
			<artifactId>copilot-boot-monitor-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba.lindorm</groupId>
			<artifactId>lindorm-sql-fat-client</artifactId>
		</dependency>
    </dependencies>

</project>
