<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aidc.copilot.dal.product.mapper.ReleaseProductMapper">
    <select id="queryProdByProdIds" resultType="com.aidc.copilot.dal.product.dataobject.ReleaseProductDO">
        SELECT * FROM release_product
        WHERE publish_product_id IN
        <foreach item="item" index="index" collection="prodIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        LIMIT #{limit}
    </select>
</mapper>
