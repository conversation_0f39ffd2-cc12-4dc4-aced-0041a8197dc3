package com.aidc.copilot.dal.preference.dataobject;

import com.aidc.copilot.dal.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: mianyun.yt
 * @Date: 2023/5/5
 */
@EqualsAndHashCode(callSuper = false)
@Data
@TableName(value = "preference")
public class PreferenceDO extends BaseDO {

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 配置属性
     */
    private String field;

    /**
     * 配置值
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String value;

    private Long creator;

    private Long modifier;

}
