package com.aidc.copilot.dal.preference.service.impl;

import com.aidc.copilot.dal.preference.dataobject.PreferenceDO;
import com.aidc.copilot.dal.preference.mapper.PreferenceMapper;
import com.aidc.copilot.dal.preference.service.PreferenceMapperService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: mianyun.yt
 * @Date: 2023/5/5
 */
@Service
public class PreferenceMapperServiceImpl extends ServiceImpl<PreferenceMapper, PreferenceDO>
    implements PreferenceMapperService {
    @Override
    public List<PreferenceDO> listByUserId(Long userId) {
        QueryWrapper<PreferenceDO> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", userId);
        return list(wrapper);
    }


    @Override
    public Long countDistinctUserId() {
        QueryWrapper<PreferenceDO> wrapper = new QueryWrapper<>();
        wrapper.select("DISTINCT user_id");
        return baseMapper.selectCount(wrapper);
    }

    @Override
    public List<PreferenceDO> queryPreferenceByField(Long userId, String field) {
        QueryWrapper<PreferenceDO> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", userId);
        wrapper.eq("field", field);
        return list(wrapper);
    }

    @Override
    public Boolean updatePreferenceByValue(Long id, String value) {
        PreferenceDO preferenceDo = new PreferenceDO();
        preferenceDo.setId(id);
        preferenceDo.setValue(value);
        return updateById(preferenceDo);
    }

    @Override
    public Boolean savePreference(Long userId, String field, String value) {
        PreferenceDO preferenceDo = new PreferenceDO();
        preferenceDo.setUserId(userId);
        preferenceDo.setValue(value);
        preferenceDo.setField(field);
        preferenceDo.setValue(value);
        return save(preferenceDo);
    }
}
