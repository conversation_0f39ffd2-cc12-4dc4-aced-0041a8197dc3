package com.aidc.copilot.dal.product.dataobject;

import com.aidc.copilot.dal.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/7/7
 */
@Setter
@Getter
@TableName(value = "release_product")
public class ReleaseProductDO extends BaseDO {
    /**
     * 商家ID
     */
    private Long merchantId;
    /**
     * 商家店铺
     */
    private String shopDomain;

    /**
     * 渠道（Import AE 等平台）商品 ID
     */
    private Long channelProductId;

    /**
     * 来源（AE 等平台）商品 ID
     */
    private Long sourceProductId;
    /**
     * 导入来源
     */
    private String sourcePlatform;
    /**
     * 发布到目标平台的商品 ID
     */
    private Long publishProductId;
    /**
     * 发布平台
     */
    private String publishPlatform;

    /**
     * 标题
     */
    private String title;
    /**
     * 规格
     */
    private String specification;
    /**
     * 描述
     */
    private String descriptionUrl;

    /**
     * 图片集合
     */
    private String images;

    /**
     * 删除标记
     */
    private Boolean deleted;

    /**
     * 发布状态
     */
    private String status;

    /**
     * 属性（JSON格式）
     */
    private String attributes;
}
