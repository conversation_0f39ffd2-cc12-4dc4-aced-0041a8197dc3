package com.aidc.copilot.dal.settings.dataobject;

import com.aidc.copilot.dal.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR>
 * @description store data object
 * @date 2023/5/5 10:37
 **/
@Data
@TableName(value = "store", autoResultMap = true)
public class StoreDO extends BaseDO {
    /**
     * member id
     */
    private Long memberId;
    /**
     * store id
     */
    private Long storeId;
    /**
     * source of store
     */
    private String source;
    /**
     * token to access store
     */
    private String accessToken;
    /**
     * name of store
     */
    private String name;
    /**
     * domain of store
     */
    private String domain;
    /**
     * email of store
     */
    private String email;
    /**
     * status
     */
    private Integer status;
    /**
     * 扩展属性
     */
    private String attributes;
}
