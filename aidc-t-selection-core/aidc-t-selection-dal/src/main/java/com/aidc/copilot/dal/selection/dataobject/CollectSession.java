package com.aidc.copilot.dal.selection.dataobject;

import com.aidc.copilot.dal.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * @Author: smy
 * @Date: 2023/5/18 11:32 AM
 */
@Data
@TableName(value = "collect_sessions", autoResultMap = true)
public class CollectSession extends BaseDO {

    private Long creator;

    private Long modifier;


    private Integer isDeleted;

    private Long userId;

    private String sessionId;

    private String content;
}
