package com.aidc.copilot.dal.subscription.dataobject;

import com.aidc.copilot.dal.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * @ClassName SubscribeRetentionFeedbackDO
 * <AUTHOR>
 * @Date 2024/2/26 16:45
 */
@Data
@TableName(value = "subscribe_retention_feedback", autoResultMap = true)
public class SubscribeRetentionFeedbackDO extends BaseDO {

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 取消订阅原因
     */
    private String subscribeCancelReason;

    /**
     * 反馈案例
     */
    private String feedbackExample;

    /**
     * 改进计划
     */
    private String improvePlan;


}
