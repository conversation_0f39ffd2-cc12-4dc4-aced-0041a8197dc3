package com.aidc.copilot.dal.asynctask.dataobject;

import com.aidc.copilot.dal.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * @desc: 异步任务
 * @author: yixiao.cx
 * @create: 2023-06-25
 **/
@Setter
@Getter
@TableName(value = "async_task")
public class AsyncTaskDO extends BaseDO {
    /**
     * 所属环境
     */
    private String env;
    /**
     * 任务所属店铺
     */
    private String store;
    /**
     * 外部ID
     */
    private String outerId;
    /**
     * 任务Key(幂等用)
     */
    private String uniqueKey;
    /**
     * 任务类型
     */
    private String type;
    /**
     * 任务优先级
     */
    private Integer priority;
    /**
     * 重试间隔(毫秒)
     */
    private Integer retryInterval;
    /**
     * 执行次数
     */
    private Integer executeCount;
    /**
     * 最大重试次数
     */
    private Integer maxRetryTimes;
    /**
     * 开始执行时间
     */
    private Date startExecuteDate;
    /**
     * 下次执行时间
     */
    private Date nextExecuteDate;
    /**
     * 过期时间
     */
    private Date expireDate;
    /**
     * 心跳时间
     */
    private Date heartbeatDate;
    /**
     * 任务状态
     */
    private String status;
    /**
     * 已读状态
     */
    private String readStatus;
    /**
     * 扩展属性
     */
    private String attributes;
    /**
     * 任务请求
     */
    private String request;
    /**
     * 任务结果
     */
    private String result;
}
