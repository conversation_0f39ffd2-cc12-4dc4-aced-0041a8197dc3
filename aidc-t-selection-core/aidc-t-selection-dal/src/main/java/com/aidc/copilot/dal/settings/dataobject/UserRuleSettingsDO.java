package com.aidc.copilot.dal.settings.dataobject;

import com.aidc.copilot.dal.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR>
 * @description store binding data object
 * @date 2023/5/5 10:54
 **/
@Data
@TableName(value = "user_rule_settings", autoResultMap = true)
public class UserRuleSettingsDO extends BaseDO {
    /**
     * dser user id
     */
    private Long userId;
    /**
     * shopify store id
     */
    private String storeId;
    /**
     * price rule settgings {"priceRatio":"0.2","customPrice":"2.22","isActive":"1/0","selected":"customPrice/priceRatio"}
     */
    private String priceRule;
    /**
     * 0:normal,1:isDelete
     */
    private Integer isDelete;
}
