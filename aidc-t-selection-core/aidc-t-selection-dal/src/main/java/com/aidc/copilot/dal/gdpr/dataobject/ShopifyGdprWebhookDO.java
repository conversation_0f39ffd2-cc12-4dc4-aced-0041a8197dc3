package com.aidc.copilot.dal.gdpr.dataobject;

import com.aidc.copilot.dal.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR> @ alibaba-inc.com>
 * @since 2023/5/23
 */

@Data
@TableName(value = "shopify_gdpr_msg", autoResultMap = true)
public class ShopifyGdprWebhookDO extends BaseDO {

    private String topic;
    private String msg;
    private Integer status;
    private String msgId;
}
