package com.aidc.copilot.dal.subscription.dataobject;

import java.util.Date;

import com.aidc.copilot.dal.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 用户订阅表
 * <AUTHOR> @ alibaba-inc.com>
 * @since 2023/6/5
 */

@Data
@TableName(value = "user_subscription", autoResultMap = true)
public class UserSubscriptionDO extends BaseDO {

    private Long userId;

    /**
     * 当前订阅状态
     */
    private String status;

    /**
     * 订阅会员name
     */
    private String planName;

    /**
     * todo
     * 订阅计划账单日
     * 1-31
     * 按照用户注册时间为准
     * 若账单日为每月22号，则套餐使用量以23-次月22号计算；
     *
     */
    private Integer statementDate;

    /**
     * 付款店铺
     */
    private String billingShop;

    /**
     * shooify 订阅id
     */
    private String subscriptionId;

    /**
     * 冻结时间
     */
    private Date frozenTime;


    /**
     * 试用开始时间
     */
    private Date trialStartDay;

    /**
     * 试用结束时间
     */
    private Date trialEndDay;
    /**
     * 试用天数 - 每次取消订阅 -> 再次订阅的时候更新，防薅羊毛用
     */
    private Integer remainingTrialDay;

}
