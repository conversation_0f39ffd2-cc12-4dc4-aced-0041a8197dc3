package com.aidc.copilot.dal.enums;

import lombok.Getter;

@Getter
public enum CommodityStatus {

    PENDING("pending", "待上架"),

    ONLINE("online", "已上架"),

    OFFLINE("offline", "已下架"),

    PUBLISHING("publishing", "发布中"),
    ;


    private final String key;

    private final String displayName;


    CommodityStatus(String key, String displayName) {
        this.key = key;
        this.displayName = displayName;
    }

}
