package com.aidc.copilot.dal.subscription.dataobject;

import com.aidc.copilot.dal.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 权益使用记录
 * <AUTHOR> @ alibaba-inc.com>
 * @since 2023/6/5
 */

@Data
@TableName(value = "benifit_usage_record", autoResultMap = true)
public class BenefitUsageRecordDO extends BaseDO {

    /**
     * use or return
     */
    private String type;

    /**
     * tried
     * confirmed
     * canceled
     */
    private String status;

    private String usageKey;

    private Long userId;

}
