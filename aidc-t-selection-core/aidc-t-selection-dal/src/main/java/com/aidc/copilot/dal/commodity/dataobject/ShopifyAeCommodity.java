package com.aidc.copilot.dal.commodity.dataobject;

import com.aidc.copilot.dal.handler.VariantOptionTypeHandler;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.util.Map;

/**
 * @Author: mianyun.yt
 * @Date: 2023/5/16
 */
@Data
public class ShopifyAeCommodity {

    /**
     * Shopify 商品 id
     */
    private Long productId;

    /**
     * Shopify sku id
     */
    private Long variantId;

    /**
     * t-selection 商品 id
     */
    private Long commodityId;

    /**
     * t-selection 商品 sku id
     */
    private Long commoditySkuId;

    /**
     * t-selection 商品 sku id
     */
    @TableField(typeHandler = VariantOptionTypeHandler.class)
    private Map<String, String> variantOption;

    /**
     * ae 商品 id
     */
    private Long itemId;

    /**
     * ae 商品 sku id
     */
    private Long itemSkuId;

}
