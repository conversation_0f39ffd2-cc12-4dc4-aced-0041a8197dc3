package com.aidc.copilot.dal.product.dataobject;

import com.aidc.copilot.dal.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/7/7
 */
@Setter
@Getter
@TableName(value = "supplier_product")
public class SupplierProductDO extends BaseDO {
    /**
     * 商家ID
     */
    private Long merchantId;

    /**
     * 商家店铺
     */
    private String shopDomain;

    /**
     * 发布产品ID
     */
    private Long releaseProductId;

    /**
     * 来源产品ID
     */
    private Long sourceProductId;

    /**
     * 来源
     */
    private String sourcePlatform;

    /**
     * 标题
     */
    private String title;

    /**
     * 图片集合
     */
    private String images;

    /**
     * 状态
     */
    private String status;

    /**
     * 删除标记
     */
    private Boolean deleted;

    /**
     * 属性（JSON格式）
     */
    private String attributes;
}
