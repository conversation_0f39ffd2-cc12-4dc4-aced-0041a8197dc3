package com.aidc.copilot.dal.commodity.dataobject;

import com.aidc.copilot.dal.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/6/20
 */
@EqualsAndHashCode(callSuper = false)
@Data
@TableName(value = "commodity_algorithms_image", autoResultMap = true)
public class CommodityAlgorithmsImageDO extends BaseDO implements Serializable {
    /**
     * 登录商家 id
     */
    private Long merchantId;
    /**
     * 登录商家绑定的店铺
     */
    private String store;
    /**
     * 关联商品 id
     */
    private Long commodityId;
    /**
     * 商品原图 url
     */
    private String originalImage;
    /**
     * 处理后图片 url
     */
    private String optimizeImage;
    /**
     * 图片顺序
     */
    private Integer sequence;
    /**
     * 算法处理类型
     */
    private Integer type;
    /**
     * 是否删除 0-未删除  1-已删除
     */
    private Integer deleted;
}
