package com.aidc.copilot.dal.product.dataobject;

import com.aidc.copilot.dal.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/7/7
 */
@Setter
@Getter
@TableName(value = "channel_sku")
public class ChannelSkuDO extends BaseDO {
    /**
     * 商家ID
     */
    private Long merchantId;
    /**
     * 渠道商品ID
     */
    private Long channelProductId;

    /**
     * 来源SKU ID
     */
    private Long sourceSkuId;

    /**
     * 成本
     */
    private BigDecimal originPrice;

    /**
     * 是否已发布
     */
    private Boolean released;

    /**
     * 删除标记
     */
    private Boolean deleted;

    /**
     * 属性（JSON格式）
     */
    private String attributes;
}
