package com.aidc.copilot.dal.email.dataobject;

import com.aidc.copilot.dal.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2024/1/16
 */

@EqualsAndHashCode(callSuper = false)
@Data
@TableName(value = "email_subscription", autoResultMap = true)
public class EmailSubscriptionDO extends BaseDO implements Serializable {
    private Long userId;
    private String saleCountry;
    private String category;
    private String categoryName;
    private String exclusiveOpportunities;
    private String email;
    private Boolean deleted;
}
