package com.aidc.copilot.dal.diagnosis.dataobject;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class DiagnosisResultRecommendDO {

    /**
     * 商品id
     */
    private Long itemId;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 销售价格
     */
    private Long sellPrice;

    /**
     * 购买成本
     */
    private Long purchaseCost;

    /**
     * 物流成本
     */
    private Long logisticsCost;

    /**
     * 店铺开店时长
     */
    private String stability;

    /**
     * 收益
     */
    private String profit;

    /**
     * 历史销售数量
     */
    private String historicalSales;

    /**
     * 近6月订单数
     */
    private Long ordCnt6m;

    /**
     * 商品主图
     */
    private String mainImageUrl;
}
