package com.aidc.copilot.dal.handler;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/6/5
 */
@MappedJdbcTypes(JdbcType.VARCHAR)
@MappedTypes({Map.class})
public abstract class MapTypeHandler<K, V> extends BaseTypeHandler<Map<K, V>> {
    @Override
    public void setNonNullParameter(PreparedStatement preparedStatement, int i, Map<K, V> kvMap, JdbcType jdbcType) throws SQLException {
        // 将 Map 转为 JSON 字符串
        String json = JSON.toJSONString(kvMap);
        // 将 JSON 字符串设置到 PreparedStatement 中
        preparedStatement.setString(i, json);
    }

    @Override
    public Map<K, V> getNullableResult(ResultSet resultSet, String columnName) throws SQLException {
        return this.getMapByJsonString(resultSet.getString(columnName));
    }

    @Override
    public Map<K, V> getNullableResult(ResultSet resultSet, int columnIndex) throws SQLException {
        return this.getMapByJsonString(resultSet.getString(columnIndex));
    }

    @Override
    public Map<K, V> getNullableResult(CallableStatement callableStatement, int columnIndex) throws SQLException {
        return this.getMapByJsonString(callableStatement.getString(columnIndex));
    }

    private Map<K, V> getMapByJsonString(String content) {
        return StringUtils.isBlank(content) ? new HashMap<>() : JSON.parseObject(content, this.specificType());
    }

    /**
     * 具体类型，由子类提供
     *
     * @return 具体类型
     */
    protected abstract TypeReference<Map<K, V>> specificType();
}
