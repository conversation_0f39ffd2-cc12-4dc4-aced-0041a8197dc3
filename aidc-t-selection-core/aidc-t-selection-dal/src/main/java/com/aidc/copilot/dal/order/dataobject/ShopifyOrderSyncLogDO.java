package com.aidc.copilot.dal.order.dataobject;
import com.aidc.copilot.dal.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * Description:shopify_order 同步日志
 * <AUTHOR>
 * Date 2023-05-05
 */

@Data
@TableName(value = "shopify_order_sync_log", autoResultMap = true)
public class ShopifyOrderSyncLogDO extends BaseDO {

    /**
     * 主订单
     */
    private Long orderId;

    /**
     * shopify_order_Num
     */
    private Long orderNum;

    /**
     * shop_domian
     */
    private String shopDomain;

    /**
     * shop_Id
     */
    private Long shopId;

    /**
     * shopify事件：orders/create 等
     */
    private String shopifyTopic;

    /**
     * 记录事件内容
     */
    private String orderBody;

}