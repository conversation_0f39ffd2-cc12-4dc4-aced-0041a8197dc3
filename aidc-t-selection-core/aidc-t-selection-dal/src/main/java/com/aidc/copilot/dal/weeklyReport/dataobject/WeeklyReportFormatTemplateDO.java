package com.aidc.copilot.dal.weeklyReport.dataobject;

import com.aidc.copilot.dal.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

/**
 * @description
 * @date 2024/6/27
 */
@Setter
@Getter
@TableName(value = "weekly_report_format_template")
public class WeeklyReportFormatTemplateDO extends BaseDO {
    /**
     * 阿里工号
     */
    private String userId;
    /**
     * 格式模版
     */
    private String formatTemplate;
}
