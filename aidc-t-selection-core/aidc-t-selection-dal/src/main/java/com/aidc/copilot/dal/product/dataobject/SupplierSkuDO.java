package com.aidc.copilot.dal.product.dataobject;

import com.aidc.copilot.dal.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/7/7
 */
@Setter
@Getter
@TableName(value = "supplier_sku")
public class SupplierSkuDO extends BaseDO {
    /**
     * 商家ID
     */
    private Long merchantId;
    /**
     * 商家店铺
     */
    private String shopDomain;
    /**
     * 供应商产品ID
     */
    private Long supplierProductId;

    /**
     * 来源平台的 SKU ID
     */
    private Long sourceSkuId;

    /**
     * 发布后目标平台的 SKU ID
     */
    private Long releaseSkuId;

    /**
     * 删除标记
     */
    private Boolean deleted;

    /**
     * 属性（JSON格式）
     */
    private String attributes;
}
