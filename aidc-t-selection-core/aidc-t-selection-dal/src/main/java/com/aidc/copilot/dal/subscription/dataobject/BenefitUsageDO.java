package com.aidc.copilot.dal.subscription.dataobject;

import com.aidc.copilot.dal.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 权益使用情况
 * <AUTHOR> @ alibaba-inc.com>
 * @since 2023/6/5
 */

@Data
@TableName(value = "benifit_usage", autoResultMap = true)
public class BenefitUsageDO extends BaseDO {

    private Long userId;

    private String usageKey;

    /**
     * 已使用权益次数
     */
    private Long usageCount;

    /**
     * 所属周期
     * 按月周期权益: todo
     * 按日周期权益：20230606
     * 一次性权益：null
     */
    private String cycle;

}
