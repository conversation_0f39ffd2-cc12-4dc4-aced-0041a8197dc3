package com.aidc.copilot.dal.handler;

import com.alibaba.fastjson2.TypeReference;

import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/6/5
 */
public class VariantOptionTypeHandler extends MapTypeHandler<String, String> {
    @Override
    protected TypeReference<Map<String, String>> specificType() {
        return new TypeReference<Map<String, String>>() {
        };
    }
}
