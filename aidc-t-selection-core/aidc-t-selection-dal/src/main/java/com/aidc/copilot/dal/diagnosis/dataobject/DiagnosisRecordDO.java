package com.aidc.copilot.dal.diagnosis.dataobject;

import com.aidc.copilot.dal.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "shop_diagnosis_record", autoResultMap = true)
public class DiagnosisRecordDO extends BaseDO {

    /**
     * 店铺
     */
    private String shopDomain;

    /**
     * 操作者id
     */
    private String merchantId;

    /**
     * 执行批次
     */
    private Integer batch;

    /**
     * 诊断结果
     */
    private String result;

    /**
     * 运行信息
     */
    private String operation;

    /**
     * 诊断结束时间
     */
    private Date endTime;

    /**
     * 诊断状态
     */
    private String status;

    /**
     * 逻辑删除
     */
    private boolean deleted;

    /**
     * 扩展字段
     */
    private String attributes;
}
