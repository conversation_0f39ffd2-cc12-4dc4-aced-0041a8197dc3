package com.aidc.copilot.dal.handler;

import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.util.ArrayList;

/**
 * 将数据库 json 数据转为 List
 *
 * @Author: mianyun.yt
 * @Date: 2023/5/16
 */
@Slf4j
@MappedTypes({Object.class})
@MappedJdbcTypes(JdbcType.VARCHAR)
public class JsonArrayHandler extends FastjsonTypeHandler {

    private final Class<?> type;

    public JsonArrayHandler(Class<?> type) {
        super(type);
        this.type = type;
    }

    @Override
    protected Object parse(String json) {
        if (json == null || json.isEmpty()) {
            return new ArrayList<>();
        }
        return JSONArray.parseArray(json, type);
    }

}
