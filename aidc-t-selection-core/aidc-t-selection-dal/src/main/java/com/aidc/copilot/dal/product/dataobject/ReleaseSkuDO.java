package com.aidc.copilot.dal.product.dataobject;

import com.aidc.copilot.dal.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/7/7
 */
@Setter
@Getter
@TableName(value = "release_sku")
public class ReleaseSkuDO extends BaseDO {
    /**
     * 商家ID
     */
    private Long merchantId;
    /**
     * 商家店铺
     */
    private String shopDomain;
    /**
     * 发布商品ID
     */
    private Long releaseProductId;
    /**
     * 渠道SKU ID
     */
    private Long channelSkuId;
    /**
     * 来源 SKU ID
     */
    private Long sourceSkuId;

    /**
     * 发布后目标平台的 SKU ID
     */
    private Long publishSkuId;

    /**
     * 原始成本
     */
    private BigDecimal originPrice;

    /**
     * 发布价格
     */
    private BigDecimal publishPrice;

    /**
     * 原始库存
     */
    private Integer originInventory;

    /**
     * 删除标记
     */
    private Boolean deleted;

    /**
     * 属性（JSON格式）
     */
    private String attributes;
}
