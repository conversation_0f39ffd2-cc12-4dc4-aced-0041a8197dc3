package com.aidc.copilot.dal.product.dataobject;

import com.aidc.copilot.dal.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/7/7
 */
@Setter
@Getter
@TableName(value = "channel_product")
public class ChannelProductDO extends BaseDO {
    /**
     * 商家ID
     */
    private Long merchantId;
    /**
     * 来源商品ID
     */
    private Long sourceProductId;

    /**
     * 导入来源
     */
    private String sourcePlatform;

    /**
     * 标题
     */
    private String title;

    /**
     * 规格
     */
    private String specification;

    /**
     * 描述
     */
    private String descriptionUrl;

    /**
     * 图片集合
     */
    private String images;

    /**
     * 是否已发布
     */
    private Boolean released;


    /**
     * 一级类目id
     */
    private Long firstCategoryId;

    /**
     * 删除标记
     */
    private Boolean deleted;

    /**
     * 属性（JSON格式）
     */
    private String attributes;
}
