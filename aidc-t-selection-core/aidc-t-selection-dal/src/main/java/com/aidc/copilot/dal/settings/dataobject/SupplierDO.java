package com.aidc.copilot.dal.settings.dataobject;

import com.aidc.copilot.dal.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR>
 * @description supplier data object
 * @date 2023/5/5 10:49
 **/
@Data
@TableName(value = "supplier", autoResultMap = true)
public class SupplierDO extends BaseDO {
    /**
     * supplier id from other system
     */
    private Long supplierId;
    /**
     * supplier name from other system
     */
    private String supplierName;
    /**
     * source
     */
    private String source;
    /**
     * member id
     */
    private Long memberId;
    /**
     * the access token to call api
     */
    private String accessToken;
    /**
     * status
     */
    private Integer status;
}
