package com.aidc.copilot.dal.selection.dataobject;

import com.aidc.copilot.dal.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * @Author: smy
 * @Date: 2023/5/12 11:14 AM
 */
@Data
@TableName(value = "recommend_prompts", autoResultMap = true)
public class RecommendPrompt extends BaseDO {

    private Long creator;

    private Long modifier;


    private Integer isDeleted;

    private String userId;

    private String userAvatar;

    private String prompt;

    private String description;

    private String imgUrl;

    private String keysProductJson;

}
