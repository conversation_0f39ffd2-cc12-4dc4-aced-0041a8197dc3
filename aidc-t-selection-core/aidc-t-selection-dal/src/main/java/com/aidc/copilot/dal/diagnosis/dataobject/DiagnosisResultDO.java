package com.aidc.copilot.dal.diagnosis.dataobject;

import com.aidc.copilot.dal.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "shop_diagnosis_result", autoResultMap = true)
public class DiagnosisResultDO extends BaseDO {

    private String shopDomain;

    private Integer batch;

    private String merchantId;

    private Long itemId;

    private String imageUrl;

    private String title;

    private Long sellPrice;

    private Long purchaseCost;

    private Long logisticsCost;

    private String recommendation;

    private String source;

    private boolean optimized;

    /**
     * 第一个推荐品的收益
     */
    @TableField(value = "max_profit")
    private Long firstRecommendProfit;

    private boolean deleted;

    private String attributes;
}
