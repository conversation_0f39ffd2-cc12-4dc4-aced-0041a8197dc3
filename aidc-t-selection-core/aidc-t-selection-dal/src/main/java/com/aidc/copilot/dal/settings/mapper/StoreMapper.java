package com.aidc.copilot.dal.settings.mapper;

import java.util.List;
import java.util.Map;

import com.aidc.copilot.dal.settings.dataobject.StoreDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 * @description store mapper
 * @date 2023/5/5 10:44
 **/
public interface StoreMapper extends BaseMapper<StoreDO> {

    @Select("SELECT member_id, COUNT(*) AS count FROM my_table GROUP BY member_id")
    List<Map<Long, Integer>> groupByMemberId();



}
