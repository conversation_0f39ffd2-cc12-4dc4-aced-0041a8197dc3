package com.aidc.copilot.dal.selection.dataobject;

import com.aidc.copilot.dal.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName(value = "manager_banner", autoResultMap = true)
public class HomepageBannerDO extends BaseDO {

    private String bannerId;

    private String country;

    private Date beginTime;

    private Date endTime;

    private Integer priority;

    private String status;

    private String attr;

}
