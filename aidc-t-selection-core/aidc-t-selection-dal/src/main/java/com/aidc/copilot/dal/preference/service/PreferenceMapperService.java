package com.aidc.copilot.dal.preference.service;

import com.aidc.copilot.dal.preference.dataobject.PreferenceDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * @Author: mianyun.yt
 * @Date: 2023/5/5
 */
public interface PreferenceMapperService extends IService<PreferenceDO> {
    /**
     * 根据 userId 查询偏好
     */
    List<PreferenceDO> listByUserId(Long userId);


    /**
     * 查询个人偏好总用户量
     */
    Long countDistinctUserId();

    /**
     * 根据 用户id 和 配置属性 查询 配置值
     *
     * @return
     */
    List<PreferenceDO> queryPreferenceByField(Long userId, String field);

    /**
     * 修改配置值
     *
     * @return
     */
    Boolean updatePreferenceByValue(Long id, String value);

    /**
     * 保存配置值
     */
    Boolean savePreference(Long userId, String field, String value);
}
