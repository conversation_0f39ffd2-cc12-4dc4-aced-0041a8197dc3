package com.aidc.copilot.dal.image.dataobject;

import com.aidc.copilot.dal.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "ai_image_result", autoResultMap = true)
public class AIImageResultDO extends BaseDO {

    /**
     * 生成记录id
     */
    private Long recordId;

    /**
     * 操作者id
     */
    private String merchantId;

    /**
     * shopDomain
     */
    private String shopDomain;

    /**
     * 图片处理类型（抠图或去水印）
     */
    private String type;

    /**
     * 原图信息
     */
    private String originImage;

    /**
     * 结果图信息
     */
    private String resultImage;

    /**
     * 关联实体类型（商品、工具）
     */
    private String relationType;

    /**
     * 关联实体id（商品、工具）
     */
    private String relationId;

    /**
     * 状态
     */
    private String status;

    /**
     * 逻辑删除
     */
    private boolean deleted;

    /**
     * 扩展字段
     */
    private String attributes;
}
