package com.aidc.copilot.dal.subscription.dataobject;

import com.aidc.copilot.dal.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 订阅计划权益
 * <AUTHOR> @ alibaba-inc.com>
 * @since 2023/6/5
 */

@Data
@TableName(value = "subscription_benifit", autoResultMap = true)
public class SubscriptionBenefitDO extends BaseDO {


    private String name;

    private String type;

    /**
     * map JSONString
     * 0:不享受权益
     * -1:无限制
     */
    private String details;

}
