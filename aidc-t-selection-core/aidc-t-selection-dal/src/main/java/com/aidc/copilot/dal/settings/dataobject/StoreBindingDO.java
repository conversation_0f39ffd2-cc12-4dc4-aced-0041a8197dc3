package com.aidc.copilot.dal.settings.dataobject;

import com.aidc.copilot.dal.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR>
 * @description store binding data object
 * @date 2023/5/5 10:54
 **/
@Data
@TableName(value = "store_binding", autoResultMap = true)
public class StoreBindingDO extends BaseDO {
    /**
     * store id
     */
    private Long storeId;
    /**
     * source
     */
    private String source;
    /**
     * member id
     */
    private Long memberId;
}
