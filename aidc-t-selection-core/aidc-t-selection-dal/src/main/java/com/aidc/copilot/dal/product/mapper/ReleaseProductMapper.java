package com.aidc.copilot.dal.product.mapper;

import com.aidc.copilot.dal.product.dataobject.ReleaseProductDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/7/7
 */
public interface ReleaseProductMapper extends BaseMapper<ReleaseProductDO> {

    /**
     * 根据Shopify商品id批量查询商品
     * @param prodIds
     * @param limit
     * @return
     */
    List<ReleaseProductDO> queryProdByProdIds(@Param("prodIds") List<Long> prodIds, @Param("limit") int limit);

}
