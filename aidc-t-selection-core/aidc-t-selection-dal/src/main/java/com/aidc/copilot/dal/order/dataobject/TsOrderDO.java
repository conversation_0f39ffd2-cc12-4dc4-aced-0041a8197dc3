package com.aidc.copilot.dal.order.dataobject;

import com.aidc.copilot.dal.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/5/8
 */
@Data
@TableName(value = "ts_order", autoResultMap = true)
public class TsOrderDO extends BaseDO {

    private String itemTitle;
    private String skuInfo;
    private String itemPic;
    private Integer quantity;
    private Long memberId;
    private String shipCountyCode;
    private String shipCounty;
    private Long customerProductId;
    private Long customerVariantId;
    private String customerOrderNum;
    private Long customerMainOrderId;
    private Long customerOrderId;
    private Integer customerOrderStatus;
    private String customerStore;
    private String customerOrderPrice;
    private Long fulfilledItemId;
    private Long fulfilledSkuId;
    private String fulfilledSkuAttr;
    private Long price;
    private String shipService;
    private Long shipFee;
    private String currencyCode;
    private Long fulfilledOrderId;
    private Long fulfilledMainOrderId;
    private Integer fulfilledOrderStatus;
    private Long fulfilledBuyerId;
    private Long fulfilledSellerId;
    private Integer status;
    private String address;
    private String attributes;
    private Long version;

}
