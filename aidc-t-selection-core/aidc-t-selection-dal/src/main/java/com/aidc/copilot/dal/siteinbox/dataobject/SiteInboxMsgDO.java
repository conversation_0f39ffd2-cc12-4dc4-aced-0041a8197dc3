package com.aidc.copilot.dal.siteinbox.dataobject;

import com.aidc.copilot.dal.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName(value = "site_inbox_msg", autoResultMap = true)
public class SiteInboxMsgDO  extends BaseDO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 收件人_用户ID
     */
    private Long recipientId;

    /**
     * 站内信标题
     */
    private String msgTitle;

    /**
     * 站内信
     */
    private String msgBody;

    /**
     * 状态:-1 删除  0 未读  1 已读
     */
    private Integer status;

    /**
     * 发件人 可空
     */
    private Long senderId;

    /**
     * 消息分类 预留
     */
    private Integer msgType;

    /**
     * 消息跳转链接
     */
    private String viewLinkUrl;


    /**
     * 消息跳转标题
     */
    private String viewLinkTitle;
}
