package com.aidc.copilot.dal.lindorm.product.dataobject;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName(value = "ae_item_order_1d", autoResultMap = true)
public class LindormProduct {

    private Long itemId;

    private String countryId;

    @TableField("`date`")
    private String date;

    private Long ordCnt;

}
