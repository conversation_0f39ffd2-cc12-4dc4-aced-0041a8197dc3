package com.aidc.copilot.dal.order.dataobject;

import com.aidc.copilot.dal.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName(value = "shopify_order", autoResultMap = true)


/**
 * Description:shopify_orders
 * <AUTHOR>
 * Date 2023-05-05
 */
public class ShopifyOrderDO extends BaseDO {
    /**
     * shop_id
     */
    private Long shopId;

    /**
     * shop_domain
     */
    private String shopDomain;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 主订单ID 无父订单即主订单
     */
    private Long mainOrderId;

    /**
     * -1 删除  0 cancel  1 下单  2 已支付  3 已发货  4 已退款
     */
    private Integer status;

    /**
     * 商品Id
     */
    private Long productId;

    /**
     * sku描述
     */
    private String sku;

    /**
     * variant_id
     */
    private Long variantId;

    /**
     * quantity
     */
    private Long quantity;

    /**
     * 客户信息:JSONObject
     */
    private String customer;

    /**
     * 邮寄地址:JSONObject
     */
    private String shippingAddress;

    /**
     * 运费信息:JSONArray
     */
    private String shippingLines;

    /**
     * order_number
     */
    private Long orderNumber;

    /**
     * currency:USD CNY
     */
    private String currency;

    /**
     * current_total_discounts
     */
    private String currentTotalDiscounts;

    /**
     * current_subtotal_price
     */
    private String currentSubtotalPrice;

    /**
     * current_total_price
     */
    private String currentTotalPrice;

    /**
     * current_total_tax
     */
    private String currentTotalTax;

    /**
     * token
     */
    private String token;

    /**
     * 1 包含  0 不包含
     */
    private Integer taxesIncluded;

    /**
     * total_discounts
     */
    private String totalDiscounts;

    /**
     * total_line_items_price
     */
    private String totalLineItemsPrice;

    /**
     * 总额total_outstanding
     */
    private String totalOutstanding;

    /**
     * total_tax
     */
    private String totalTax;

    /**
     * total_shipping_price (虚:_set)
     */
    private String totalShippingPrice;

    /**
     * total_shipping_price JSONArray
     */
    private String totalShippingPriceSet;

    /**
     * total_tip_received
     */
    private String totalTipReceived;

    /**
     * total_weight
     */
    private String totalWeight;

    /**
     * 1 true  0 false
     */
    private Integer productExists;

    /**
     * item_line_price
     */
    private String price;

    /**
     * 1 true  0 false
     */
    private Integer requiresShipping;

    /**
     * 1 true  0 false
     */
    private Integer taxable;

    /**
     * item_line_title
     */
    private String title;

    /**
     * variant_inventory_management
     */
    private String variantInventoryManagement;

    /**
     * variant_title
     */
    private String variantTitle;

    /**
     * 供应商vendor
     */
    private String vendor;

    /**
     * customer_locale
     */
    private String customerLocale;

    /**
     * estimated_taxes  1 true 0 false
     */
    private Integer estimatedTaxes;

    /**
     * financial_status voided 等
     */
    private String financialStatus;

    /**
     * fulfillment_status pending 等
     */
    private String fulfillmentStatus;

    /**
     * name
     */
    private String name;

    /**
     * note
     */
    private String note;

    /**
     * note_attributes JSONArray
     */
    private String noteAttributes;

    /**
     * phone
     */
    private String phone;

    /**
     * 是否测试单 1 true 0 false
     */
    private Integer test;

    /**
     * subtotal_price
     */
    private String subtotalPrice;

    /**
     * buyer_accepts_marketing 1 true 0 false
     */
    private int buyerAcceptsMarketing;

    /**
     * cancel_reason
     */
    private String cancelReason;

    /**
     * cancelled_at  时间
     */
    private String cancelledAt;

    /**
     * created_at
     */
    private String createdAt;

    /**
     * closed_at
     */
    private String closedAt;

    /**
     * contact_email
     */
    private String contactEmail;

    /**
     * cart_token
     */
    private String cartToken;

    /**
     * checkout_id
     */
    private String checkoutId;

    /**
     * checkout_token
     */
    private String checkoutToken;

    /**
     * email
     */
    private String email;

    /**
     * number
     */
    private String number;


    /**
     * billing_address  JSONObject
     */
    private String billingAddress;

    /**
     * line_items  JSONArrary
     */
    private String lineItems;


    /**
     * total_price
     */
    private String totalPrice;

    /**
     * member_id:store的DS用户ID
     */
    private Long memberId;

}
