# 基础镜像的Dockerfile ： https://lark.alipay.com/aone/docker/rm2g1d
# 基于基础镜像
FROM hub.docker.alibaba-inc.com/aone-base/aidc-t-selection_ajdk:20231127110226

# 设置打开jpda 调试端口。如果需要则打开下面的注释内容
ENV JPDA_ENABLE=1

# 设置spring profile或者自定义的jvm参数。如果需要则打开下面的注释内容
ENV SERVICE_OPTS=-Dspring.profiles.active=testing

# 将构建出的主包复制到指定镜像目录中
COPY ${APP_NAME}.tgz /home/<USER>/${APP_NAME}/target/${APP_NAME}.tgz

# 创建sls机器标识
RUN mkdir -p /etc/ilogtail/
RUN echo -e "$APP_NAME-testing" > /etc/ilogtail/user_defined_id
RUN mkdir -p /etc/ilogtail/users
RUN touch /etc/ilogtail/users/1105571179876820