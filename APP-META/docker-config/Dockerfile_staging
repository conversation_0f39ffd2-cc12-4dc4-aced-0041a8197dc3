# 基础镜像的Dockerfile ： https://lark.alipay.com/aone/docker/rm2g1d
# 基于基础镜像
#FROM hub.docker.alibaba-inc.com/aone-base/aidc-t-selection_ws:20230531203900
FROM hub.docker.alibaba-inc.com/aone-base/aidc-t-selection_ajdk:20231127110226

# security need
COPY --from=reg.docker.alibaba-inc.com/alisecurity/security_shield:1.0 /home/<USER>/security_shield /home/<USER>/security_shield

#字体安装
RUN yum install ipa-gothic-fonts xorg-x11-fonts-100dpi xorg-x11-fonts-75dpi xorg-x11-utils xorg-x11-fonts-cyrillic xorg-x11-fonts-Type1 xorg-x11-fonts-misc -y && \
wget https://hrdata.oss-cn-zhangjiakou.aliyuncs.com/odin_data/fonts/msyh.ttf -O /home/<USER>/msyh.ttf  && \
yum install -y freetype freetype-devel fontconfig fontconfig-devel && \
mkdir -p /usr/share/fonts/chinese/TrueType/ && \
cp /home/<USER>/msyh.ttf /usr/share/fonts/chinese/TrueType/ && \
fc-cache -fv


############# staging #################
# 设置spring profile或者自定义的jvm参数。如果需要则打开下面的注释内容
ENV SERVICE_OPTS=-Dspring.profiles.active=staging

# 将构建出的主包复制到指定镜像目录中
COPY ${APP_NAME}.tgz /home/<USER>/${APP_NAME}/target/${APP_NAME}.tgz

# 远程DEBUG, 0 关闭，1打开
ENV JPDA_ENABLE=1

# 创建sls机器标识
RUN mkdir -p /etc/ilogtail/
RUN echo -e "$APP_NAME-staging" > /etc/ilogtail/user_defined_id
RUN mkdir -p /etc/ilogtail/users
RUN touch /etc/ilogtail/users/1105571179876820