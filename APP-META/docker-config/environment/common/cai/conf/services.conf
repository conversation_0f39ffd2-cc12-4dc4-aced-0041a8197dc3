#####beacon
beacon on;
beacon_cfg info-beacon.cfg taobao-channel.cfg;

#####trans
trans off;

#####error_page
# error_page 400 http://err.taobao.com/error1.html;
# error_page 403 http://err.taobao.com/error1.html;
# error_page 404 http://err.taobao.com/error1.html;
# error_page 405 http://err.taobao.com/error1.html;
# error_page 408 http://err.taobao.com/error1.html;
# error_page 410 http://err.taobao.com/error1.html;
# error_page 411 http://err.taobao.com/error1.html;
# error_page 412 http://err.taobao.com/error1.html;
# error_page 413 http://err.taobao.com/error1.html;
# error_page 414 http://err.taobao.com/error1.html;
# error_page 415 http://err.taobao.com/error1.html;
# error_page 500 http://err.taobao.com/error2.html;
# error_page 501 http://err.taobao.com/error2.html;
# error_page 502 http://err.taobao.com/error2.html;
# error_page 503 http://err.taobao.com/error2.html;
# error_page 506 http://err.taobao.com/error2.html;

#####tmd
#include tmd_http.conf;

#####gray
#include gray_conf/resource/gray-config-server.conf;

#####waf
#waf on;

#####limit speed
#limit_req_zone          $binary_remote_addr  zone=req:20m   rate=200r/s;
#limit_req               zone=req  burst=100;
#limit_conn_zone         $binary_remote_addr  zone=conn:20m;
#limit_conn              conn 200;