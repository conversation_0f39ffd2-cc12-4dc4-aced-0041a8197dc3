# 基础镜像的Dockerfile ： https://lark.alipay.com/aone/docker/rm2g1d
# 基于基础镜像
FROM reg.docker.alibaba-inc.com/aone-base/know-you-commodity_init:20230410154909

############# staging #################
# 设置spring profile或者自定义的jvm参数。如果需要则打开下面的注释内容
ENV SERVICE_OPTS=-Dspring.profiles.active=staging

# 将构建出的主包复制到指定镜像目录中
COPY ${APP_NAME}.tgz /home/<USER>/${APP_NAME}/target/${APP_NAME}.tgz

# 远程DEBUG, 0 关闭，1打开
#ENV JPDA_ENABLE=1