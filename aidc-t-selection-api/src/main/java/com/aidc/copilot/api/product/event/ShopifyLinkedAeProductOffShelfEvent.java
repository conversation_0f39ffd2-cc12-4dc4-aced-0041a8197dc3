package com.aidc.copilot.api.product.event;

import com.aidc.copilot.api.product.enums.ProductOffShelfReasonEnum;
import com.alibaba.copilot.boot.event.eventbus.annotation.Event;
import lombok.*;

/**
 * 上架到Shopify对应AE的商品已下架Event
 */
@Setter
@Getter
@Event
public class ShopifyLinkedAeProductOffShelfEvent {

    /**
     * shopify店铺domain
     */
    private String shopifyShopDomain;

    /**
     * shopify商品id
     */
    private Long shopifyProductId;

    /**
     * 来源商品id
     */
    private Long sourceProductId;

    /**
     * 商品下架原因
     * @see ProductOffShelfReasonEnum
     */
    private String reason;

}
