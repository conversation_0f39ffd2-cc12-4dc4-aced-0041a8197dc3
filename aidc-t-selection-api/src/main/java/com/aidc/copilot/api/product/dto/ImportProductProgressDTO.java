package com.aidc.copilot.api.product.dto;

import com.google.common.collect.Lists;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 导入商品进度DTO
 */
@Data
@Accessors(chain = true)
public class ImportProductProgressDTO {

    /**
     * 状态码
     */
    private String status;

    /**
     * 任务创建时间
     */
    private Long createTime;

    /**
     * 商品信息列表
     */
    private List<ProductResp> importProductList = Lists.newArrayList();

    @Data
    @Accessors(chain = true)
    public static class ProductResp {

        /**
         * shopify商品id
         */
        private Long shopifyProductId;

        /**
         * 来源商品id
         */
        private Long sourceProductId;

        /**
         * 是否成功
         */
        private boolean success;

        /**
         * 错误码
         */
        private String failCode;

        /**
         * 错误信息
         */
        private String failMsg;
    }
}
