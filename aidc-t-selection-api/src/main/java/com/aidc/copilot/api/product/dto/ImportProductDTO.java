package com.aidc.copilot.api.product.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2024/1/8
 */
@Data
public class ImportProductDTO implements Serializable {
    /**
     * 产品 id
     */
    private List<Long> productIds;

    /**
     * import来源
     */
    private String importSource;

    /**
     * token
     */
    private String token;

    /**
     * oneshop UIC id
     */
    private Long onShopUserId;

    /**
     * user id
     */
    private Long userId;

    private String shopDomain;
}
