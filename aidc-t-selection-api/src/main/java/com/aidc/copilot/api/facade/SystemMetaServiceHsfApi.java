package com.aidc.copilot.api.facade;

import com.aidc.copilot.api.annotation.AccountKey;
import com.aidc.copilot.api.dto.SystemCountryDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2023/12/3
 */
public interface SystemMetaServiceHsfApi {

    /**
     * @return 获取国家信息
     */
    List<SystemCountryDTO> getCountryList();

    /**
     * @return 获取国家信息
     */
    String queryMyUserId(@AccountKey String userId);
}
