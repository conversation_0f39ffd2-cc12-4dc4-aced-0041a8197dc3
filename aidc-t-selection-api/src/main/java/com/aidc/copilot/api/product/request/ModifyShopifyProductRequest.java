package com.aidc.copilot.api.product.request;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 修改Shopify商品request
 */
@Data
@Accessors(chain = true)
public class ModifyShopifyProductRequest extends BaseProductRequest {

    /**
     * shopify商品id
     */
    @NotNull(message = "shopifyProductId illegal")
    private Long shopifyProductId;

    /**
     * 划线价
     */
    @NotNull(message = "compareAtPrice illegal")
    @DecimalMin(value = "0.0", message = "compareAtPrice must be greater than or equal to 0")
    private BigDecimal compareAtPrice;

    /**
     * 售卖价
     */
    @NotNull(message = "price illegal")
    @DecimalMin(value = "0.0", message = "price must be greater than or equal to 0")
    private BigDecimal price;
}
