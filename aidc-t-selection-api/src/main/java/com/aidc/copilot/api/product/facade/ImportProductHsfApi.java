package com.aidc.copilot.api.product.facade;

import com.aidc.copilot.api.product.dto.ImportProductProgressDTO;
import com.aidc.copilot.api.product.request.ImportProductRequest;
import com.aidc.copilot.api.product.request.ModifyShopifyProductRequest;
import com.aidc.copilot.api.product.request.QueryImportProgressRequest;
import com.alibaba.copilot.boot.basic.result.SingleResult;

/**
 * 导入商品HSF
 */
public interface ImportProductHsfApi {

    /**
     * 提交选购导入商品任务
     *
     * @param merchantId 商家id
     * @param request    请求入参
     * @return 任务id
     */
    SingleResult<Long> submitImportProductTask(Long merchantId, ImportProductRequest request);

    /**
     * 查询导入商品任务进度
     *
     * @param merchantId 商家id
     * @param request    请求入参
     * @return 任务信息
     */
    SingleResult<ImportProductProgressDTO> queryImportProductProgress(Long merchantId, QueryImportProgressRequest request);

    /**
     * 修改Shopify商品划线价和售卖价
     *
     * @param merchantId 商家id
     * @param request    请求入参
     * @return 是否成功，true：成功
     */
    SingleResult<Boolean> modifyShopifyProductPrice(Long merchantId, ModifyShopifyProductRequest request);
}
