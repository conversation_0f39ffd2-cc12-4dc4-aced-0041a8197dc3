package com.aidc.copilot.api.product.request;

import com.aidc.copilot.api.product.ImportProductSource;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 导入商品request
 */
@Data
@Accessors(chain = true)
public class ImportProductRequest extends BaseProductRequest {

    /**
     * 业务请求id
     * （为了保证幂等）
     */
    private String requestId;

    /**
     * 商品来源
     */
    @NotNull(message = "importSource illegal")
    private ImportProductSource importSource;

    /**
     * 是否上架到 Shopify 商店
     * （true：上架，false：不上架）
     */
    private Boolean publishShopify = true;

    /**
     * 商品列表
     */
    @NotEmpty(message = "products illegal")
    private List<ProductReq> products;

    @Data
    @Accessors(chain = true)
    public static class ProductReq {

        /**
         * 来源商品id
         */
        @NotNull(message = "sourceProductId illegal")
        @Min(value = 1, message = "sourceProductId should be greater than 0")
        private Long sourceProductId;

        /**
         * 划线价
         */
        @NotNull(message = "price illegal")
        private BigDecimal compareAtPrice;

        /**
         * 售卖价
         */
        @NotNull(message = "compareAtPrice illegal")
        private BigDecimal price;

        /**
         * 优化后的商品标题
         */
        private String title;

        /**
         * 优化后的商品描述
         */
        private String desc;

        /**
         * 优化后的商品主图
         */
        private List<String> mainImages;

        /**
         * 商品标签
         */
        private Set<String> tags = new HashSet<>();
    }
}
